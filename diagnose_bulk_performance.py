#!/usr/bin/env python3
"""
Diagnostic script to identify bulk API performance issues

This script helps diagnose why bulk API might not be providing expected
performance improvements by checking various factors.

Usage:
    python diagnose_bulk_performance.py

Author: AI Assistant
Version: 2.0
Date: 2025-08-04
"""

import json
import time
import sys
import os
from pathlib import Path

def check_implementation():
    """Check if bulk API implementation is correct"""
    print("🔍 Checking Bulk API Implementation...")
    
    try:
        from fmc_migration_v2 import FMCMigrationEngine, HostObject
        print("✅ Successfully imported FMC Migration Engine")
        
        # Check if bulk methods exist
        if hasattr(HostObject, 'bulk_create'):
            print("✅ HostObject.bulk_create method found")
        else:
            print("❌ HostObject.bulk_create method missing")
            return False
            
        # Check migration engine
        import inspect
        sig = inspect.signature(FMCMigrationEngine.__init__)
        if 'use_bulk_api' in sig.parameters:
            print("✅ FMCMigrationEngine supports use_bulk_api parameter")
        else:
            print("❌ FMCMigrationEngine missing use_bulk_api parameter")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def check_configuration():
    """Check if configuration supports bulk operations"""
    print("\n🔧 Checking Configuration...")
    
    # Look for existing config files
    config_files = list(Path('.').glob('*.json'))
    if not config_files:
        print("⚠️  No JSON configuration files found in current directory")
        return False
    
    print(f"📄 Found {len(config_files)} configuration files:")
    for config_file in config_files[:5]:  # Show first 5
        print(f"   - {config_file}")
    
    # Check a sample config file
    sample_config = config_files[0]
    try:
        with open(sample_config, 'r') as f:
            config = json.load(f)
        
        # Check structure
        if 'api_calls' in config:
            print("✅ Configuration has api_calls structure")
            
            # Count objects
            total_objects = 0
            for section in ['host_objects', 'network_objects', 'service_objects']:
                if section in config['api_calls'] and 'data' in config['api_calls'][section]:
                    count = len(config['api_calls'][section]['data'])
                    total_objects += count
                    print(f"   - {section}: {count} objects")
            
            print(f"📊 Total objects in sample config: {total_objects}")
            
            if total_objects > 5:
                print("✅ Object count sufficient for bulk API (>5)")
            else:
                print("⚠️  Object count low - bulk API may not be used (<= 5)")
                
        else:
            print("❌ Configuration missing api_calls structure")
            return False
            
    except Exception as e:
        print(f"❌ Error reading config file: {e}")
        return False
    
    return True

def check_fmc_connection():
    """Check FMC connection configuration"""
    print("\n🌐 Checking FMC Connection Configuration...")
    
    try:
        # Check if fmcapi is available
        import fmcapi
        print("✅ fmcapi library is available")
        
        # Check connection parameters in the script
        with open('fmc_migration_v2.py', 'r') as f:
            content = f.read()
            
        if 'fmc_host = "https://' in content:
            # Extract FMC host
            lines = content.split('\n')
            for line in lines:
                if 'fmc_host = "https://' in line:
                    host = line.split('"')[1]
                    print(f"📡 FMC Host configured: {host}")
                    break
        else:
            print("⚠️  FMC host configuration not found in script")
        
        # Check for hardcoded credentials (security note)
        if 'password = "' in content and '!Techn0l0gy01!' in content:
            print("⚠️  Hardcoded credentials found - consider using environment variables")
        
        return True
        
    except ImportError:
        print("❌ fmcapi library not available - install with: pip install fmcapi")
        return False
    except Exception as e:
        print(f"❌ Error checking connection: {e}")
        return False

def analyze_logs():
    """Analyze recent log files for bulk API usage"""
    print("\n📋 Analyzing Recent Logs...")
    
    log_dir = Path('logs')
    if not log_dir.exists():
        print("⚠️  No logs directory found")
        return False
    
    # Find recent log files
    log_files = list(log_dir.glob('fmc_migration_v2_*.log'))
    if not log_files:
        print("⚠️  No migration log files found")
        return False
    
    # Get most recent log file
    recent_log = max(log_files, key=lambda f: f.stat().st_mtime)
    print(f"📄 Analyzing recent log: {recent_log}")
    
    try:
        with open(recent_log, 'r') as f:
            log_content = f.read()
        
        # Look for bulk API indicators
        bulk_indicators = [
            'BULK_API_ENABLED',
            'BULK_API_START', 
            'BULK_API_COMPLETE',
            'Using bulk API',
            'bulk operation',
            'bulk=true'
        ]
        
        individual_indicators = [
            'INDIVIDUAL_API',
            'Using individual API',
            'individual calls'
        ]
        
        bulk_found = any(indicator in log_content for indicator in bulk_indicators)
        individual_found = any(indicator in log_content for indicator in individual_indicators)
        
        if bulk_found:
            print("✅ Bulk API usage detected in logs")
            
            # Look for performance metrics
            lines = log_content.split('\n')
            for line in lines:
                if 'BULK_API_COMPLETE' in line:
                    print(f"   📊 {line.strip()}")
                elif 'obj/s' in line and 'bulk' in line.lower():
                    print(f"   📈 {line.strip()}")
                    
        else:
            print("❌ No bulk API usage found in logs")
        
        if individual_found:
            print("⚠️  Individual API usage detected - bulk may not be triggered")
        
        # Look for errors
        error_lines = [line for line in log_content.split('\n') if 'ERROR' in line or 'Failed' in line]
        if error_lines:
            print(f"⚠️  Found {len(error_lines)} error lines:")
            for error_line in error_lines[:3]:  # Show first 3
                print(f"   ❌ {error_line.strip()}")
        
        return bulk_found
        
    except Exception as e:
        print(f"❌ Error reading log file: {e}")
        return False

def provide_recommendations():
    """Provide recommendations for improving bulk API performance"""
    print("\n💡 Recommendations for Bulk API Performance:")
    print("=" * 50)
    
    print("1. 📊 **Verify Object Count**")
    print("   - Bulk API is only used for >5 objects")
    print("   - Test with 50+ objects for best results")
    print("   - Use test_bulk_api.py to generate test configs")
    
    print("\n2. 🔧 **Check Configuration**")
    print("   - Ensure use_bulk_api=True (default)")
    print("   - Verify FMC connection is working")
    print("   - Check for authentication issues")
    
    print("\n3. 📋 **Monitor Logs**")
    print("   - Look for 'BULK_API_ENABLED' in logs")
    print("   - Check for 'BULK_API_COMPLETE' with timing")
    print("   - Watch for fallback to individual calls")
    
    print("\n4. 🚀 **Test Performance**")
    print("   - Run: python test_bulk_performance.py")
    print("   - Compare bulk vs individual timing")
    print("   - Test with different object counts")
    
    print("\n5. 🌐 **Network Factors**")
    print("   - Bulk API benefits increase with network latency")
    print("   - Local FMC may show smaller improvements")
    print("   - Test over WAN connections for best results")

def main():
    """Main diagnostic function"""
    print("🩺 FMC Bulk API Performance Diagnostic")
    print("=" * 60)
    
    all_checks_passed = True
    
    # Run checks
    if not check_implementation():
        all_checks_passed = False
    
    if not check_configuration():
        all_checks_passed = False
    
    if not check_fmc_connection():
        all_checks_passed = False
    
    if not analyze_logs():
        all_checks_passed = False
    
    # Summary
    print("\n" + "=" * 60)
    if all_checks_passed:
        print("✅ All checks passed - bulk API should be working")
        print("\n🎯 Next steps:")
        print("   1. Run a test migration with verbose logging")
        print("   2. Check logs for bulk API usage confirmation")
        print("   3. Compare performance with --no-bulk option")
    else:
        print("⚠️  Some issues detected - see recommendations below")
    
    provide_recommendations()

if __name__ == "__main__":
    main()
