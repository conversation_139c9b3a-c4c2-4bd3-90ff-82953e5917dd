#!/usr/bin/env python3
"""
Configuration Issue Fixer
Automatically fixes common issues in the FMC migration configuration.
"""

import json
import sys
import ipaddress
import re
from typing import Dict, List, Any
from datetime import datetime

def convert_range_to_cidr(range_str: str) -> str:
    """Convert IP range to CIDR notation where possible"""
    try:
        # Handle ranges like "***********-*************"
        if '-' in range_str:
            start_ip, end_ip = range_str.split('-')
            start = ipaddress.ip_address(start_ip.strip())
            end = ipaddress.ip_address(end_ip.strip())
            
            # Try to find a CIDR that covers this range
            for prefix_len in range(32, -1, -1):
                try:
                    network = ipaddress.ip_network(f"{start}/{prefix_len}", strict=False)
                    if network.network_address <= start and network.broadcast_address >= end:
                        return str(network)
                except ValueError:
                    continue
            
            # If no single CIDR works, return the first IP as a /32
            return f"{start}/32"
        
        return range_str
    except Exception:
        return range_str

def is_fqdn(value: str) -> bool:
    """Check if a value is an FQDN"""
    # Simple FQDN detection - contains dots and doesn't look like an IP
    if '.' not in value:
        return False

    # Check if it looks like an IP address or CIDR
    try:
        ipaddress.ip_address(value)
        return False
    except ValueError:
        pass

    try:
        ipaddress.ip_network(value, strict=False)
        return False
    except ValueError:
        pass

    # If it contains letters and dots, it's likely an FQDN
    return any(c.isalpha() for c in value)

def fix_network_objects(networks: List[Dict]) -> List[Dict]:
    """Fix network object issues"""
    fixed_networks = []
    
    for network in networks:
        fixed_network = network.copy()
        value = network.get('value', '')
        
        if value:
            # Check if it's an IP range
            if '-' in value and not is_fqdn(value):
                fixed_value = convert_range_to_cidr(value)
                if fixed_value != value:
                    print(f"  Fixed range: {value} -> {fixed_value}")
                    fixed_network['value'] = fixed_value
            
            # Check if it's an FQDN (should be FQDN object type)
            elif is_fqdn(value):
                print(f"  Converting to FQDN object: {network.get('name')} ({value})")
                fixed_network['type'] = 'FQDN'
                # Keep the value as-is for FQDN objects
        
        fixed_networks.append(fixed_network)
    
    return fixed_networks

def fix_service_objects(services: List[Dict]) -> List[Dict]:
    """Fix service object issues"""
    # Common port mappings
    port_mappings = {
        'pcanywhere-data': '5631',
        'pcanywhere-status': '5632',
        'www': '80',
        'https': '443',
        'ssh': '22',
        'ftp': '21',
        'telnet': '23',
        'smtp': '25',
        'dns': '53',
        'snmp': '161'
    }
    
    fixed_services = []
    
    for service in services:
        fixed_service = service.copy()
        port = service.get('port', '')
        
        if port and port in port_mappings:
            print(f"  Fixed port: {service.get('name')} {port} -> {port_mappings[port]}")
            fixed_service['port'] = port_mappings[port]
        
        fixed_services.append(fixed_service)
    
    return fixed_services

def fix_object_groups(groups: List[Dict], available_objects: Dict) -> List[Dict]:
    """Fix object group issues by removing references to non-existent objects"""
    fixed_groups = []
    
    # Create a set of all available object names
    all_objects = set()
    for obj_category in available_objects.values():
        all_objects.update(obj_category.keys())
    
    for group in groups:
        fixed_group = group.copy()
        original_objects = group.get('objects', [])
        fixed_objects = []
        removed_count = 0
        
        for obj_ref in original_objects:
            obj_name = obj_ref.get('name', '')
            if obj_name in all_objects:
                fixed_objects.append(obj_ref)
            else:
                removed_count += 1
        
        if removed_count > 0:
            print(f"  Fixed group {group.get('name')}: removed {removed_count} invalid references")
        
        fixed_group['objects'] = fixed_objects
        
        # Only include groups that have at least one valid object
        if fixed_objects:
            fixed_groups.append(fixed_group)
        else:
            print(f"  Removed empty group: {group.get('name')}")
    
    return fixed_groups

def fix_configuration(input_file: str, output_file: str) -> bool:
    """Fix issues in the configuration file"""
    print("=" * 80)
    print("🔧 FMC Configuration Issue Fixer")
    print("=" * 80)
    print(f"📅 Fix Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📄 Input File: {input_file}")
    print(f"📄 Output File: {output_file}")
    print()
    
    # Load configuration
    try:
        with open(input_file, 'r') as f:
            config = json.load(f)
        print("✅ Configuration file loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return False
    
    # Fix network objects
    if 'api_calls' in config and 'network_objects' in config['api_calls']:
        networks = config['api_calls']['network_objects'].get('data', [])
        print(f"🔧 Fixing {len(networks)} network objects...")
        fixed_networks = fix_network_objects(networks)
        config['api_calls']['network_objects']['data'] = fixed_networks
        print("✅ Network objects fixed")
    
    # Fix service objects
    if 'api_calls' in config and 'service_objects' in config['api_calls']:
        services = config['api_calls']['service_objects'].get('data', [])
        print(f"🔧 Fixing {len(services)} service objects...")
        fixed_services = fix_service_objects(services)
        config['api_calls']['service_objects']['data'] = fixed_services
        print("✅ Service objects fixed")
    
    # Fix object groups
    if 'api_calls' in config and 'object_groups' in config['api_calls']:
        groups = config['api_calls']['object_groups'].get('data', [])
        available_objects = config.get('object_lookup', {})
        print(f"🔧 Fixing {len(groups)} object groups...")
        fixed_groups = fix_object_groups(groups, available_objects)
        config['api_calls']['object_groups']['data'] = fixed_groups
        print(f"✅ Object groups fixed ({len(groups) - len(fixed_groups)} groups removed)")
    
    # Update metadata
    if 'metadata' in config:
        config['metadata']['last_fixed'] = datetime.now().isoformat()
        config['metadata']['fixed_by'] = 'fix_config_issues.py'
    
    # Save fixed configuration
    try:
        with open(output_file, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"✅ Fixed configuration saved to: {output_file}")
        return True
    except Exception as e:
        print(f"❌ Failed to save fixed configuration: {e}")
        return False

def main():
    """Main function"""
    input_file = "fmc_migration_config.json"
    output_file = "fmc_migration_config_fixed.json"
    
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    print("This tool will attempt to automatically fix common issues in the FMC configuration.")
    print("It will create a new fixed configuration file.")
    print()
    
    success = fix_configuration(input_file, output_file)
    
    if success:
        print()
        print("🎉 Configuration fixing completed!")
        print(f"💡 Run validation on {output_file} to verify fixes")
        print(f"💡 Command: python validate_config_data.py {output_file}")
    else:
        print()
        print("❌ Configuration fixing failed")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
