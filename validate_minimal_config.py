#!/usr/bin/env python3
"""
Minimal Configuration Validator
Tests the FMC migration system with a small subset of objects to validate functionality.
"""

import json
import sys
import os
from datetime import datetime
from fmc_migration_v2 import FMCMigrationEngine

def load_config(config_file):
    """Load and validate the minimal configuration"""
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        print(f"✅ Loaded configuration: {config_file}")
        print(f"📊 Total objects to test:")
        
        metadata = config.get('metadata', {})
        totals = metadata.get('total_objects', {})
        
        for obj_type, count in totals.items():
            print(f"   • {obj_type}: {count}")
        
        return config
    
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return None

def validate_object_structure(config):
    """Validate the structure of objects in the configuration"""
    issues = []
    
    # Check host objects
    if 'host_objects' in config.get('api_calls', {}):
        hosts = config['api_calls']['host_objects'].get('data', [])
        for host in hosts:
            if not all(key in host for key in ['name', 'type', 'value']):
                issues.append(f"Host object missing required fields: {host.get('name', 'UNKNOWN')}")
            
            # Validate IP address
            try:
                import ipaddress
                ipaddress.ip_address(host.get('value', ''))
            except ValueError:
                issues.append(f"Invalid IP address in host {host.get('name', 'UNKNOWN')}: {host.get('value', '')}")
    
    # Check network objects
    if 'network_objects' in config.get('api_calls', {}):
        networks = config['api_calls']['network_objects'].get('data', [])
        for network in networks:
            if not all(key in network for key in ['name', 'type', 'value']):
                issues.append(f"Network object missing required fields: {network.get('name', 'UNKNOWN')}")
            
            # Validate network CIDR
            try:
                import ipaddress
                ipaddress.ip_network(network.get('value', ''), strict=False)
            except ValueError:
                issues.append(f"Invalid network CIDR in network {network.get('name', 'UNKNOWN')}: {network.get('value', '')}")
    
    # Check service objects
    if 'service_objects' in config.get('api_calls', {}):
        services = config['api_calls']['service_objects'].get('data', [])
        for service in services:
            if not all(key in service for key in ['name', 'protocol', 'port']):
                issues.append(f"Service object missing required fields: {service.get('name', 'UNKNOWN')}")
            
            # Validate port number
            try:
                port = int(service.get('port', 0))
                if not (1 <= port <= 65535):
                    issues.append(f"Invalid port number in service {service.get('name', 'UNKNOWN')}: {port}")
            except ValueError:
                issues.append(f"Non-numeric port in service {service.get('name', 'UNKNOWN')}: {service.get('port', '')}")
    
    return issues

def test_fmcapi_connection():
    """Test basic fmcapi functionality without actual FMC connection"""
    try:
        import fmcapi
        print("✅ fmcapi library imported successfully")
        
        # Test creating objects without connection (will fail but shows structure)
        try:
            # This will fail but shows if the classes are available
            host_obj = fmcapi.Hosts(fmc=None)
            print("✅ fmcapi.Hosts class available")
        except Exception as e:
            print(f"⚠️  fmcapi.Hosts test: {e}")
        
        try:
            network_obj = fmcapi.Networks(fmc=None)
            print("✅ fmcapi.Networks class available")
        except Exception as e:
            print(f"⚠️  fmcapi.Networks test: {e}")
        
        try:
            port_obj = fmcapi.ProtocolPortObjects(fmc=None)
            print("✅ fmcapi.ProtocolPortObjects class available")
        except Exception as e:
            print(f"⚠️  fmcapi.ProtocolPortObjects test: {e}")
        
        return True
    
    except ImportError as e:
        print(f"❌ fmcapi library not available: {e}")
        return False

def run_validation_test(config_file):
    """Run the complete validation test"""
    print("=" * 80)
    print("🧪 FMC Migration System - Minimal Validation Test")
    print("=" * 80)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Step 1: Load configuration
    print("🔍 Step 1: Loading minimal configuration...")
    config = load_config(config_file)
    if not config:
        return False
    print()
    
    # Step 2: Validate object structure
    print("🔍 Step 2: Validating object structure...")
    issues = validate_object_structure(config)
    if issues:
        print("❌ Configuration validation issues found:")
        for issue in issues:
            print(f"   • {issue}")
        return False
    else:
        print("✅ Configuration structure is valid")
    print()
    
    # Step 3: Test fmcapi availability
    print("🔍 Step 3: Testing fmcapi library...")
    if not test_fmcapi_connection():
        print("❌ fmcapi library issues detected")
        return False
    print()
    
    # Step 4: Test migration engine initialization
    print("🔍 Step 4: Testing migration engine initialization...")
    try:
        # Test with mock credentials (will fail connection but test initialization)
        engine = FMCMigrationEngine(
            fmc_host="https://test.example.com",
            username="test",
            password="test",
            verify_ssl=False,
            quiet=True
        )
        print("✅ Migration engine initialized successfully")
        print("⚠️  Note: Actual FMC connection will be tested separately")
    except Exception as e:
        print(f"❌ Migration engine initialization failed: {e}")
        print("⚠️  This may be expected without a real FMC connection")
    print()
    
    print("=" * 80)
    print("🎉 Minimal validation test completed successfully!")
    print("=" * 80)
    print()
    print("📋 Next Steps:")
    print("1. Test with actual FMC connection")
    print("2. Run migration with minimal config")
    print("3. Verify objects are created correctly")
    print("4. Scale up to larger configurations")
    
    return True

if __name__ == "__main__":
    config_file = "minimal_validation_config.json"
    
    if len(sys.argv) > 1:
        config_file = sys.argv[1]
    
    if not os.path.exists(config_file):
        print(f"❌ Configuration file not found: {config_file}")
        sys.exit(1)
    
    success = run_validation_test(config_file)
    sys.exit(0 if success else 1)
