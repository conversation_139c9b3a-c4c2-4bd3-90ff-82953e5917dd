{"session_id": "migration_1754343914", "connection_type": "fmcapi", "timestamp": "2025-08-04T14:46:24.470714", "results": {"hosts": {"phase_name": "phase1_hosts", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost1", "✅ Updated host: TestHost2", "✅ Updated host: TestHost3"], "duration_seconds": 22.00764489173889, "success_rate": 0.0}, "networks": {"phase_name": "phase1_networks", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork1", "✅ Updated network: TestNetwork2"], "duration_seconds": 15.753277778625488, "success_rate": 0.0}, "services": {"phase_name": "phase1_services", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestHTTP", "✅ Updated service: TestHTTPS", "✅ Updated service: TestSSH"], "duration_seconds": 8.742680072784424, "success_rate": 0.0}, "object_groups": {"phase_name": "phase1_object_groups", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to update object_group: TestNetworkGroup - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType"], "duration_seconds": 6.249855041503906, "success_rate": 0.0}, "service_groups": {"phase_name": "phase1_service_groups", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to update service_group: TestWebServices - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType"], "duration_seconds": 5.591966152191162, "success_rate": 0.0}, "access_rules": {"phase_name": "phase1_access_rules", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule1 - fmcapi post() failed - no ID returned. Result: False"], "duration_seconds": 8.515934228897095, "success_rate": 0.0}}, "totals": {"total_objects": 11, "created": 0, "updated": 8, "failed": 3, "skipped": 0, "success_rate": 72.72727272727273}, "phantom_objects": []}