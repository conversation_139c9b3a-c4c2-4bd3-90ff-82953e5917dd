#!/usr/bin/env python3
"""
Test script for FMC Bulk API functionality

This script tests the bulk API enhancements to ensure they work correctly
and provides performance comparisons.

Usage:
    python test_bulk_api.py

Author: AI Assistant
Version: 2.0
Date: 2025-08-04
"""

import json
import time
import sys
from typing import List, Dict

def create_test_config(num_objects: int = 50) -> Dict:
    """
    Create a test configuration with the specified number of objects
    
    Args:
        num_objects: Number of test objects to create
        
    Returns:
        Test configuration dictionary
    """
    config = {
        "api_calls": {
            "host_objects": {
                "data": []
            },
            "network_objects": {
                "data": []
            },
            "service_objects": {
                "data": []
            }
        }
    }
    
    # Create test host objects
    for i in range(num_objects):
        config["api_calls"]["host_objects"]["data"].append({
            "name": f"BulkTestHost_{i+1:03d}",
            "type": "Host",
            "value": f"192.168.{(i // 254) + 1}.{(i % 254) + 1}",
            "description": f"Bulk API test host {i+1}"
        })
    
    # Create test network objects
    for i in range(num_objects):
        config["api_calls"]["network_objects"]["data"].append({
            "name": f"BulkTestNet_{i+1:03d}",
            "type": "Network", 
            "value": f"10.{(i // 254) + 1}.{(i % 254) + 1}.0/24",
            "description": f"Bulk API test network {i+1}"
        })
    
    # Create test service objects
    protocols = ["TCP", "UDP"]
    for i in range(num_objects):
        config["api_calls"]["service_objects"]["data"].append({
            "name": f"BulkTestSvc_{i+1:03d}",
            "type": "ProtocolPortObject",
            "protocol": protocols[i % 2],
            "port": str(8000 + i),
            "description": f"Bulk API test service {i+1}"
        })
    
    return config

def save_test_config(config: Dict, filename: str = "test_bulk_config.json"):
    """
    Save test configuration to file
    
    Args:
        config: Configuration dictionary
        filename: Output filename
    """
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Test configuration saved: {filename}")
    return filename

def run_bulk_test():
    """
    Run bulk API test
    """
    print("🚀 FMC Bulk API Test")
    print("=" * 50)
    
    # Test different object counts
    test_counts = [10, 25, 50]
    
    for count in test_counts:
        print(f"\n📊 Creating test config with {count} objects...")
        
        # Create test configuration
        config = create_test_config(count)
        config_file = f"test_bulk_config_{count}.json"
        save_test_config(config, config_file)
        
        print(f"📄 Test config created: {config_file}")
        print(f"   - {len(config['api_calls']['host_objects']['data'])} host objects")
        print(f"   - {len(config['api_calls']['network_objects']['data'])} network objects") 
        print(f"   - {len(config['api_calls']['service_objects']['data'])} service objects")
        
        # Instructions for manual testing
        print(f"\n🔧 To test bulk API with {count} objects:")
        print(f"   python fmc_migration_v2.py {config_file}")
        print(f"\n🔧 To test individual API with {count} objects:")
        print(f"   python fmc_migration_v2.py {config_file} --no-bulk")
        
        print(f"\n📈 To run performance comparison:")
        print(f"   python fmc_bulk_migration_demo.py <fmc_host> <username> <password>")
    
    print("\n" + "=" * 50)
    print("✅ Test configurations created successfully!")
    print("\n💡 Next steps:")
    print("1. Update FMC connection details in fmc_migration_v2.py")
    print("2. Run the test configurations with and without --no-bulk")
    print("3. Compare performance and results")
    print("4. Check logs for detailed operation information")

def validate_bulk_api_implementation():
    """
    Validate that bulk API implementation is working
    """
    print("\n🔍 Validating Bulk API Implementation...")
    
    try:
        # Import the migration engine
        from fmc_migration_v2 import FMCMigrationEngine, HostObject, NetworkObject, ProtocolPortObject
        print("✅ Successfully imported FMC Migration Engine with bulk API support")
        
        # Check if bulk methods exist
        if hasattr(HostObject, 'bulk_create'):
            print("✅ HostObject.bulk_create method found")
        else:
            print("❌ HostObject.bulk_create method missing")
            
        if hasattr(NetworkObject, 'bulk_create'):
            print("✅ NetworkObject.bulk_create method found")
        else:
            print("❌ NetworkObject.bulk_create method missing")
            
        if hasattr(ProtocolPortObject, 'bulk_create'):
            print("✅ ProtocolPortObject.bulk_create method found")
        else:
            print("❌ ProtocolPortObject.bulk_create method missing")
        
        # Check migration engine bulk support
        try:
            # This will fail without FMC connection, but we can check the constructor
            engine_class = FMCMigrationEngine
            import inspect
            sig = inspect.signature(engine_class.__init__)
            if 'use_bulk_api' in sig.parameters:
                print("✅ FMCMigrationEngine supports use_bulk_api parameter")
            else:
                print("❌ FMCMigrationEngine missing use_bulk_api parameter")
        except Exception as e:
            print(f"⚠️  Could not validate FMCMigrationEngine: {e}")
        
        print("✅ Bulk API implementation validation complete")
        
    except ImportError as e:
        print(f"❌ Failed to import migration engine: {e}")
        print("   Make sure fmc_migration_v2.py is in the same directory")
        return False
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False
    
    return True

def main():
    """
    Main test function
    """
    print("🧪 FMC Bulk API Test Suite")
    print("=" * 60)
    
    # Validate implementation
    if not validate_bulk_api_implementation():
        print("❌ Bulk API implementation validation failed")
        sys.exit(1)
    
    # Run bulk test
    run_bulk_test()
    
    print("\n🎉 Test suite completed successfully!")
    print("\n📚 For more information, see:")
    print("   - BULK_API_ENHANCEMENT_GUIDE.md")
    print("   - fmc_bulk_migration_demo.py")

if __name__ == "__main__":
    main()
