#!/usr/bin/env python3
"""
Test script to fix the 4 failing objects and achieve 100% success rate
"""

import os
import sys
import json
import logging
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fmc_migration_v2 import FMCMigrationEngine, HostObject, ProtocolPortObject

def test_fix_failures():
    """Test the specific failing objects to achieve 100% success"""
    
    print("🔧 Testing fixes for the 4 failing objects...")
    print("📊 Target: 100% success rate")
    
    # Load environment variables
    load_dotenv()
    print("📁 Loaded environment variables from: .env")
    
    # Create migration engine
    engine = FMCMigrationEngine()
    
    # Test data for the 4 failing objects
    test_data = {
        "host_objects": {
            "endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/hosts",
            "method": "POST",
            "data": [
                {
                    "name": "NexTalkSec",
                    "type": "Host",
                    "value": "**************",
                    "description": "Migrated from ASA - Test fix for duplicate handling",
                    "overridable": False
                }
            ]
        },
        "service_objects": {
            "endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/protocolportobjects",
            "method": "POST",
            "data": [
                {
                    "name": "obj-tcp-eq-80",
                    "type": "TCPPortObject",
                    "protocol": "TCP",
                    "port": "www",
                    "description": "Migrated from ASA - Test fix for named port"
                },
                {
                    "name": "obj-tcp-eq-23",
                    "type": "TCPPortObject",
                    "protocol": "TCP",
                    "port": "telnet",
                    "description": "Migrated from ASA - Test fix for named port"
                },
                {
                    "name": "obj-tcp-eq-5631",
                    "type": "TCPPortObject",
                    "protocol": "TCP",
                    "port": "pcanywhere-data",
                    "description": "Migrated from ASA - Test fix for named port"
                }
            ]
        }
    }
    
    # Test host object (NexTalkSec)
    print("\n🏠 Testing NexTalkSec host object fix...")
    host_result = engine.migrate_objects("hosts", test_data["host_objects"]["data"], HostObject, "Test host fix")
    print(f"✅ Host result: {host_result.created + host_result.updated} success, {host_result.failed} failed")

    if host_result.failed > 0:
        print("❌ Host failures:")
        for detail in host_result.details:
            if hasattr(detail, 'success') and not detail.success:
                print(f"   • {detail.object_name}: {detail.message}")

    # Test service objects (port fixes)
    print("\n🔧 Testing service objects with port name fixes...")
    service_result = engine.migrate_objects("services", test_data["service_objects"]["data"], ProtocolPortObject, "Test service fix")
    print(f"✅ Service result: {service_result.created + service_result.updated} success, {service_result.failed} failed")

    if service_result.failed > 0:
        print("❌ Service failures:")
        for detail in service_result.details:
            if hasattr(detail, 'success') and not detail.success:
                print(f"   • {detail.object_name}: {detail.message}")
    
    # Calculate overall results
    total_objects = 4
    total_success = (host_result.created + host_result.updated) + (service_result.created + service_result.updated)
    total_failed = host_result.failed + service_result.failed
    success_rate = (total_success / total_objects) * 100
    
    print(f"\n======================================================================")
    print(f"🎯 FIX TEST RESULTS")
    print(f"======================================================================")
    print(f"📊 Total Objects: {total_objects}")
    print(f"✅ Successful: {total_success}")
    print(f"❌ Failed: {total_failed}")
    print(f"🎯 Success Rate: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print(f"\n🎉 SUCCESS! Achieved 100% success rate!")
        print(f"✅ All fixes are working correctly")
        return True
    else:
        print(f"\n⚠️ Still have {total_failed} failures. Need more fixes.")
        return False

if __name__ == "__main__":
    success = test_fix_failures()
    sys.exit(0 if success else 1)
