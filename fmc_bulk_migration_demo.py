#!/usr/bin/env python3
"""
FMC Bulk Migration Demo Script

This script demonstrates the enhanced bulk API capabilities of the FMC Migration Toolkit v2.0.
It provides performance comparisons between bulk and individual API operations.

Features:
- Bulk API operations for faster migration
- Performance benchmarking
- Fallback to individual operations when needed
- Detailed logging and reporting

Author: AI Assistant
Version: 2.0
Date: 2025-08-04
"""

import json
import time
import sys
import os
from pathlib import Path
from typing import Dict, List, Any

# Import the enhanced migration engine
try:
    from fmc_migration_v2 import FMCMigrationEngine, HostObject, NetworkObject, ProtocolPortObject
except ImportError as e:
    print(f"Error: Could not import FMC Migration Engine: {e}")
    print("Please ensure fmc_migration_v2.py is in the same directory.")
    sys.exit(1)

class BulkMigrationDemo:
    """
    Demonstration class for FMC bulk migration capabilities
    """
    
    def __init__(self, fmc_host: str, username: str, password: str):
        self.fmc_host = fmc_host
        self.username = username
        self.password = password
        
        # Create migration engines for comparison
        self.bulk_engine = FMCMigrationEngine(
            fmc_host=fmc_host,
            username=username,
            password=password,
            use_bulk_api=True,
            quiet=False
        )
        
        self.individual_engine = FMCMigrationEngine(
            fmc_host=fmc_host,
            username=username,
            password=password,
            use_bulk_api=False,
            quiet=False
        )
    
    def generate_test_data(self, object_type: str, count: int) -> List[Dict]:
        """
        Generate test data for migration testing
        
        Args:
            object_type: Type of objects to generate ('hosts', 'networks', 'services')
            count: Number of objects to generate
            
        Returns:
            List of object data dictionaries
        """
        test_data = []
        
        if object_type == 'hosts':
            for i in range(count):
                test_data.append({
                    'name': f'BulkTestHost_{i+1:04d}',
                    'type': 'Host',
                    'value': f'192.168.{(i // 254) + 1}.{(i % 254) + 1}',
                    'description': f'Bulk migration test host {i+1}'
                })
        
        elif object_type == 'networks':
            for i in range(count):
                test_data.append({
                    'name': f'BulkTestNetwork_{i+1:04d}',
                    'type': 'Network',
                    'value': f'10.{(i // 254) + 1}.{(i % 254) + 1}.0/24',
                    'description': f'Bulk migration test network {i+1}'
                })
        
        elif object_type == 'services':
            protocols = ['TCP', 'UDP']
            for i in range(count):
                protocol = protocols[i % 2]
                port = 8000 + i
                test_data.append({
                    'name': f'BulkTestService_{i+1:04d}',
                    'type': 'ProtocolPortObject',
                    'protocol': protocol,
                    'port': str(port),
                    'description': f'Bulk migration test service {i+1}'
                })
        
        return test_data
    
    def run_performance_comparison(self, object_counts: List[int] = [10, 50, 100, 500]) -> Dict[str, Any]:
        """
        Run performance comparison between bulk and individual operations
        
        Args:
            object_counts: List of object counts to test
            
        Returns:
            Performance comparison results
        """
        print("🚀 Starting FMC Bulk API Performance Comparison")
        print("=" * 80)
        
        results = {
            'timestamp': time.time(),
            'comparisons': [],
            'summary': {}
        }
        
        for count in object_counts:
            print(f"\n📊 Testing with {count} objects...")
            
            # Generate test data
            test_hosts = self.generate_test_data('hosts', count)
            
            # Test bulk operation
            print(f"🔄 Testing bulk API with {count} hosts...")
            bulk_start = time.time()
            try:
                bulk_result = self.bulk_engine.migrate_objects(
                    object_type='hosts',
                    objects_data=test_hosts,
                    object_class=HostObject,
                    description=f'Bulk Test ({count} hosts)'
                )
                bulk_duration = time.time() - bulk_start
                bulk_success = True
                bulk_created = bulk_result.created
                bulk_failed = bulk_result.failed
            except Exception as e:
                bulk_duration = time.time() - bulk_start
                bulk_success = False
                bulk_created = 0
                bulk_failed = count
                print(f"❌ Bulk operation failed: {e}")
            
            # Test individual operations
            print(f"🔄 Testing individual API with {count} hosts...")
            individual_start = time.time()
            try:
                individual_result = self.individual_engine.migrate_objects(
                    object_type='hosts',
                    objects_data=test_hosts,
                    object_class=HostObject,
                    description=f'Individual Test ({count} hosts)'
                )
                individual_duration = time.time() - individual_start
                individual_success = True
                individual_created = individual_result.created
                individual_failed = individual_result.failed
            except Exception as e:
                individual_duration = time.time() - individual_start
                individual_success = False
                individual_created = 0
                individual_failed = count
                print(f"❌ Individual operation failed: {e}")
            
            # Calculate performance metrics
            if individual_duration > 0:
                speed_improvement = ((individual_duration - bulk_duration) / individual_duration) * 100
            else:
                speed_improvement = 0
            
            comparison = {
                'object_count': count,
                'bulk': {
                    'duration': bulk_duration,
                    'success': bulk_success,
                    'created': bulk_created,
                    'failed': bulk_failed,
                    'objects_per_second': count / bulk_duration if bulk_duration > 0 else 0
                },
                'individual': {
                    'duration': individual_duration,
                    'success': individual_success,
                    'created': individual_created,
                    'failed': individual_failed,
                    'objects_per_second': count / individual_duration if individual_duration > 0 else 0
                },
                'performance': {
                    'speed_improvement_percent': speed_improvement,
                    'time_saved_seconds': individual_duration - bulk_duration
                }
            }
            
            results['comparisons'].append(comparison)
            
            # Print results
            print(f"📈 Results for {count} objects:")
            print(f"   Bulk API:       {bulk_duration:.2f}s ({count/bulk_duration:.1f} obj/s)")
            print(f"   Individual API: {individual_duration:.2f}s ({count/individual_duration:.1f} obj/s)")
            print(f"   Speed improvement: {speed_improvement:.1f}%")
            print(f"   Time saved: {individual_duration - bulk_duration:.2f}s")
        
        # Calculate summary statistics
        if results['comparisons']:
            avg_improvement = sum(c['performance']['speed_improvement_percent'] for c in results['comparisons']) / len(results['comparisons'])
            total_time_saved = sum(c['performance']['time_saved_seconds'] for c in results['comparisons'])
            
            results['summary'] = {
                'average_speed_improvement_percent': avg_improvement,
                'total_time_saved_seconds': total_time_saved,
                'tests_completed': len(results['comparisons'])
            }
        
        return results
    
    def save_performance_report(self, results: Dict[str, Any], filename: str = None) -> str:
        """
        Save performance comparison results to a JSON file
        
        Args:
            results: Performance comparison results
            filename: Optional filename (auto-generated if not provided)
            
        Returns:
            Path to saved report file
        """
        if filename is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"fmc_bulk_performance_report_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"📄 Performance report saved: {filename}")
        return filename
    
    def print_performance_summary(self, results: Dict[str, Any]):
        """
        Print a formatted summary of performance results
        """
        print("\n" + "=" * 80)
        print("📊 BULK API PERFORMANCE SUMMARY")
        print("=" * 80)
        
        if not results.get('summary'):
            print("❌ No performance data available")
            return
        
        summary = results['summary']
        print(f"✅ Tests completed: {summary['tests_completed']}")
        print(f"📈 Average speed improvement: {summary['average_speed_improvement_percent']:.1f}%")
        print(f"⏱️  Total time saved: {summary['total_time_saved_seconds']:.2f} seconds")
        
        print("\n📋 Detailed Results:")
        print("-" * 80)
        print(f"{'Objects':<10} {'Bulk (s)':<12} {'Individual (s)':<15} {'Improvement':<12} {'Time Saved':<12}")
        print("-" * 80)
        
        for comparison in results['comparisons']:
            count = comparison['object_count']
            bulk_time = comparison['bulk']['duration']
            individual_time = comparison['individual']['duration']
            improvement = comparison['performance']['speed_improvement_percent']
            time_saved = comparison['performance']['time_saved_seconds']
            
            print(f"{count:<10} {bulk_time:<12.2f} {individual_time:<15.2f} {improvement:<12.1f}% {time_saved:<12.2f}s")
        
        print("-" * 80)
        print("\n💡 Recommendations:")
        if summary['average_speed_improvement_percent'] > 50:
            print("   🚀 Bulk API provides significant performance benefits!")
            print("   📦 Recommended for large-scale migrations (>50 objects)")
        elif summary['average_speed_improvement_percent'] > 20:
            print("   ✅ Bulk API provides moderate performance benefits")
            print("   📦 Recommended for medium-scale migrations (>20 objects)")
        else:
            print("   ⚠️  Bulk API provides minimal performance benefits")
            print("   🔄 Individual API may be sufficient for small migrations")
        
        print("=" * 80)

def main():
    """
    Main function to run the bulk migration demo
    """
    if len(sys.argv) < 4:
        print("Usage: python fmc_bulk_migration_demo.py <fmc_host> <username> <password>")
        print("\nExample:")
        print("  python fmc_bulk_migration_demo.py fmc.example.com admin Cisco123")
        sys.exit(1)
    
    fmc_host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    
    try:
        # Create demo instance
        demo = BulkMigrationDemo(fmc_host, username, password)
        
        # Run performance comparison
        results = demo.run_performance_comparison([10, 50, 100])
        
        # Save and display results
        report_file = demo.save_performance_report(results)
        demo.print_performance_summary(results)
        
        print(f"\n📄 Full report saved to: {report_file}")
        
    except KeyboardInterrupt:
        print("\n⚠️  Demo interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
