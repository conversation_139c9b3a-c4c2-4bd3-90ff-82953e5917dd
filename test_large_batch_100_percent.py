#!/usr/bin/env python3
"""
Test 100% success rate with a larger batch of real ASA objects
"""

import json
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_large_batch():
    """Test migration with a larger batch of real ASA objects"""
    print("🚀 Testing 100% Success Rate with Large Batch (100 hosts + networks + services)...")
    
    config_file = "fmc_migration_config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ Configuration file {config_file} not found!")
        return False
    
    try:
        from fmc_migration_v2 import FMCMigrationEngine, HostObject, NetworkObject, ProtocolPortObject
        
        # Load config
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # Take samples from each object type
        test_data = {}
        
        if 'host_objects' in config.get('api_calls', {}):
            all_hosts = config['api_calls']['host_objects']['data']
            test_data['hosts'] = all_hosts[:100]  # First 100 hosts
            
        if 'network_objects' in config.get('api_calls', {}):
            all_networks = config['api_calls']['network_objects']['data']
            test_data['networks'] = all_networks[:20]  # First 20 networks
            
        if 'service_objects' in config.get('api_calls', {}):
            all_services = config['api_calls']['service_objects']['data']
            test_data['services'] = all_services[:10]  # First 10 services
        
        total_test_objects = sum(len(objects) for objects in test_data.values())
        print(f"📊 Testing with {total_test_objects} objects:")
        for obj_type, objects in test_data.items():
            print(f"   • {obj_type}: {len(objects)} objects")
        
        # Initialize migration engine
        engine = FMCMigrationEngine(
            verify_ssl=False,
            overwrite=True,
            quiet=False,
            use_bulk_api=True
        )
        
        results = {}
        
        # Migrate hosts
        if 'hosts' in test_data:
            print("🏠 Migrating Host Objects...")
            results['hosts'] = engine.migrate_objects(
                'hosts',
                test_data['hosts'],
                HostObject,
                "Host Objects"
            )
        
        # Migrate networks
        if 'networks' in test_data:
            print("🌐 Migrating Network Objects...")
            results['networks'] = engine.migrate_objects(
                'networks',
                test_data['networks'],
                NetworkObject,
                "Network Objects"
            )
        
        # Migrate services
        if 'services' in test_data:
            print("🔧 Migrating Protocol Port Objects...")
            results['services'] = engine.migrate_objects(
                'services',
                test_data['services'],
                ProtocolPortObject,
                "Protocol Port Objects"
            )
        
        # Analyze results
        total_objects = sum(r.total_objects for r in results.values())
        total_created = sum(r.created for r in results.values())
        total_updated = sum(r.updated for r in results.values())
        total_failed = sum(r.failed for r in results.values())
        total_skipped = sum(r.skipped for r in results.values())
        
        overall_success_rate = ((total_created + total_updated + total_skipped) / total_objects * 100) if total_objects > 0 else 0
        
        print("\n" + "="*70)
        print("🎯 LARGE BATCH TEST RESULTS")
        print("="*70)
        print(f"📊 Total Objects: {total_objects}")
        print(f"✅ Created: {total_created}")
        print(f"🔄 Updated: {total_updated}")
        print(f"❌ Failed: {total_failed}")
        print(f"⏭️ Skipped: {total_skipped}")
        print(f"🎯 Overall Success Rate: {overall_success_rate:.1f}%")
        
        # Show detailed results by phase
        for phase_name, result in results.items():
            print(f"\n📋 {phase_name}:")
            print(f"   Objects: {result.total_objects}")
            print(f"   Created: {result.created}")
            print(f"   Updated: {result.updated}")
            print(f"   Failed: {result.failed}")
            print(f"   Success Rate: {result.success_rate:.1f}%")
            
            if result.failed > 0:
                print(f"   ❌ Failures:")
                for detail in result.details:
                    if "Failed" in detail or "❌" in detail:
                        print(f"      • {detail}")
        
        # Check if we achieved 100% success
        if overall_success_rate >= 100.0 and total_failed == 0:
            print("\n🎉 SUCCESS! Achieved 100% migration success rate for large batch!")
            return True
        else:
            print(f"\n⚠️ Did not achieve 100% success rate. Current: {overall_success_rate:.1f}%")
            print(f"   Failed objects: {total_failed}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_large_batch()
    sys.exit(0 if success else 1)
