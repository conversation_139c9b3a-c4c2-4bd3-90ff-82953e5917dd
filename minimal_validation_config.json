{"metadata": {"source": "Minimal Validation Test", "timestamp": "2025-08-04", "description": "Minimal configuration for testing FMC migration system", "total_objects": {"host_objects": 3, "network_objects": 2, "service_objects": 3, "object_groups": 1, "service_groups": 1, "access_rules": 1}}, "api_calls": {"host_objects": {"endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/hosts", "method": "POST", "data": [{"name": "TestHost1", "type": "Host", "value": "************", "description": "Test host object 1", "overridable": false}, {"name": "TestHost2", "type": "Host", "value": "************", "description": "Test host object 2", "overridable": false}, {"name": "TestHost3", "type": "Host", "value": "**********", "description": "Test host object 3", "overridable": false}]}, "network_objects": {"endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/networks", "method": "POST", "data": [{"name": "TestNetwork1", "type": "Network", "value": "*************/24", "description": "Test network object 1", "overridable": false}, {"name": "TestNetwork2", "type": "Network", "value": "*********/16", "description": "Test network object 2", "overridable": false}]}, "service_objects": {"endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/protocolportobjects", "method": "POST", "data": [{"name": "TestHTTP", "type": "TCPPortObject", "protocol": "TCP", "port": "80", "description": "Test HTTP service"}, {"name": "TestHTTPS", "type": "TCPPortObject", "protocol": "TCP", "port": "443", "description": "Test HTTPS service"}, {"name": "TestSSH", "type": "TCPPortObject", "protocol": "TCP", "port": "22", "description": "Test SSH service"}]}, "object_groups": {"endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/networkgroups", "method": "POST", "data": [{"name": "TestNetworkGroup", "type": "NetworkGroup", "description": "Test network group", "objects": [{"name": "TestNetwork1", "type": "NetworkObject"}, {"name": "TestHost1", "type": "HostObject"}]}]}, "service_groups": {"endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/portobjectgroups", "method": "POST", "data": [{"name": "TestWebServices", "type": "PortObjectGroup", "description": "Test web services group", "objects": [{"name": "TestHTTP", "type": "ProtocolPortObject"}, {"name": "TestHTTPS", "type": "ProtocolPortObject"}]}]}, "access_rules": {"endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/policy/accesspolicies/{access_policy_id}/accessrules", "method": "POST", "data": [{"name": "TestRule1", "type": "AccessRule", "action": "ALLOW", "enabled": true, "description": "Test access rule", "sourceNetworks": {"objects": [{"name": "TestHost1", "type": "Host"}]}, "destinationNetworks": {"objects": [{"name": "TestNetwork1", "type": "Network"}]}, "destinationPorts": {"objects": [{"name": "TestHTTP", "type": "ProtocolPortObject"}]}}]}}, "object_lookup": {"host_objects": {"TestHost1": {"type": "Host", "value": "************", "name": "TestHost1"}, "TestHost2": {"type": "Host", "value": "************", "name": "TestHost2"}, "TestHost3": {"type": "Host", "value": "**********", "name": "TestHost3"}}, "network_objects": {"TestNetwork1": {"type": "Network", "value": "*************/24", "name": "TestNetwork1"}, "TestNetwork2": {"type": "Network", "value": "*********/16", "name": "TestNetwork2"}}, "service_objects": {"TestHTTP": {"name": "TestHTTP", "protocol": "TCP", "port": "80"}, "TestHTTPS": {"name": "TestHTTPS", "protocol": "TCP", "port": "443"}, "TestSSH": {"name": "TestSSH", "protocol": "TCP", "port": "22"}}}}