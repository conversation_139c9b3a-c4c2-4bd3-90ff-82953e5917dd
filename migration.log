PS H:\cisco-fmc> python.exe .\fmc_migration_v2.py .\fmc_migration_config.json --overwrite --quiet --latest
INFO:fmc_migration_v2:================================================================================
INFO:fmc_migration_v2:FMC Migration Engine v2.0 Started
INFO:fmc_migration_v2:Session ID: migration_1754340331
INFO:fmc_migration_v2:Connection Type: fmcapi
INFO:fmc_migration_v2:🔍 Connection Diagnostic:
INFO:fmc_migration_v2:   • FMC Object Type: <class 'fmcapi.fmc.FMC'>
INFO:fmc_migration_v2:   • fmcapi Available: True
INFO:fmc_migration_v2:   • Available Methods: __enter__ (context manager)
INFO:root:Requesting new tokens from https://*************/api/fmc_platform/v1/auth/generatetoken.
INFO:root:Building base to URLs.
INFO:root:Populating vdbVersion, sruVersion, serverVersion, and geoVersion FMC instance variables.
INFO:root:This FMC's version is 7.4.2 (build 172)
INFO:fmc_migration_v2:   • fmcapi Hosts object created successfully
INFO:fmc_migration_v2:   • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
INFO:root:Auto deploy changes set to False.  Use the Deploy button in FMC to push changes to FTDs.
INFO:fmc_migration_v2:🔍 Connection diagnostic complete
INFO:fmc_migration_v2:================================================================================
INFO:fmc_migration_v2:🔍 Running pre-migration validation...
INFO:root:Requesting new tokens from https://*************/api/fmc_platform/v1/auth/generatetoken.
INFO:root:Building base to URLs.
INFO:root:Populating vdbVersion, sruVersion, serverVersion, and geoVersion FMC instance variables.
INFO:root:This FMC's version is 7.4.2 (build 172)
WARNING:root:   GET query for __test_connection__ is not found.
INFO:root:Auto deploy changes set to False.  Use the Deploy button in FMC to push changes to FTDs.
INFO:fmc_migration_v2:================================================================================
INFO:fmc_migration_v2:PRE-MIGRATION VALIDATION SUMMARY
INFO:fmc_migration_v2:================================================================================
INFO:fmc_migration_v2:[OK] Overall Status: PASSED
INFO:fmc_migration_v2:[OK] Configuration: 1011 objects found
INFO:fmc_migration_v2:   • host_objects: 629
INFO:fmc_migration_v2:   • network_objects: 63
INFO:fmc_migration_v2:   • service_objects: 29
INFO:fmc_migration_v2:   • service_groups: 66
INFO:fmc_migration_v2:   • access_rules: 224
INFO:fmc_migration_v2:[OK] Connection: fmcapi - API accessible
INFO:fmc_migration_v2:================================================================================
INFO:fmc_migration_v2:BULK_API_ENABLED: Processing 629 objects with bulk API
INFO:fmc_migration_v2:No API delay settings found to optimize
INFO:fmc_migration_v2:BULK_API_START: Beginning bulk operation for 629 hosts
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 100 objects
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name RadSaratoga already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name NATHAN9.dmz already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name Medent.RPTS already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 100 objects
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name spc.rtr already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name NLHDC1_IPMI already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name retsolinc3.com already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 100 objects
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name IRIS already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name obj-************ already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name AMC.PACS.NEW already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 100 objects
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name BPC.External already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name CHC.EXTERNAL.1 already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name CENTRALINK_VISTA1 already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 100 objects
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name CENTRALINK already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name love.explorethebest.com.spam.3 already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name DI.PETCTVIEWER already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 100 objects
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name DI.R.AND.F already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name LUCIUS23B.NLH.ORG already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name PRADEV.NLH.ORG already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 29 objects
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/hosts?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name LUCIUS28.NLH.ORG already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
INFO:fmc_migration_v2:BULK_API_COMPLETE: 629 objects in 3.33s (189.1 obj/s)
ERROR:fmc_migration_v2:Bulk operation failed for RadSaratoga: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for RadAmsMem: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for RadStMarys: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for RadSeton: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for RadBellevue: Bulk validation error: {}
INFO:fmc_migration_v2:[FILE] Enhanced checkpoint saved: migration_checkpoints\migration_1754340331_phase1_hosts.json
INFO:fmc_migration_v2:BULK_API_ENABLED: Processing 63 objects with bulk API
INFO:fmc_migration_v2:No API delay settings found to optimize
INFO:fmc_migration_v2:BULK_API_START: Beginning bulk operation for 63 networks
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 63 objects
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/networks?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name TeleMedVT3 already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/networks?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'Invalid IP Address.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
INFO:fmc_migration_v2:BULK_API_COMPLETE: 63 objects in 0.33s (188.9 obj/s)
ERROR:fmc_migration_v2:Bulk operation failed for TeleMedVT3: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for TelemedVT4: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for TelemedVT5: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for TeleMedVT1: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for Medent.VPN.net: Bulk validation error: {}
INFO:fmc_migration_v2:[FILE] Enhanced checkpoint saved: migration_checkpoints\migration_1754340331_phase1_networks.json
INFO:fmc_migration_v2:BULK_API_ENABLED: Processing 29 objects with bulk API
INFO:fmc_migration_v2:No API delay settings found to optimize
INFO:fmc_migration_v2:BULK_API_START: Beginning bulk operation for 29 services
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 29 objects
ERROR:root:Error in POST operation --> 400 Client Error: 400 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/protocolportobjects?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'FRAMEWORK', 'messages': [{'description': 'The object name obj-tcp-eq-15002 already exists. Enter a new name.'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
INFO:fmc_migration_v2:BULK_API_COMPLETE: 29 objects in 0.17s (169.5 obj/s)
ERROR:fmc_migration_v2:Bulk operation failed for obj-tcp-eq-80: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for obj-tcp-eq-15002: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for obj-tcp-eq-15331: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for obj-tcp-eq-3389: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for obj-tcp-eq-2222: Bulk validation error: {}
INFO:fmc_migration_v2:[FILE] Enhanced checkpoint saved: migration_checkpoints\migration_1754340331_phase1_services.json
INFO:fmc_migration_v2:BULK_API_ENABLED: Processing 66 objects with bulk API
INFO:fmc_migration_v2:No API delay settings found to optimize
INFO:fmc_migration_v2:BULK_API_START: Beginning bulk operation for 66 service_groups
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 66 objects
WARNING:root:Either:
        1. Payload too large.  FMC can only handle a payload of 2048000 bytes.
        2.The payload contains an unprocessable or unreadable entity such as a invalid attribut name or incorrect JSON syntax
ERROR:root:Error in POST operation --> 422 Client Error: 422 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/portobjectgroups?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'OTHER', 'messages': [{'bulkPayloadIndex': '1', 'description': 'Unprocessable Entity'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
WARNING:root:Either:
        1. Payload too large.  FMC can only handle a payload of 2048000 bytes.
        2.The payload contains an unprocessable or unreadable entity such as a invalid attribut name or incorrect JSON syntax
ERROR:root:Error in POST operation --> 422 Client Error: 422 for url: https://*************/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/object/portobjectgroups?bulk=true
ERROR:root:json_response -->    {'error': {'category': 'OTHER', 'messages': [{'bulkPayloadIndex': '1', 'description': 'Unprocessable Entity'}], 'severity': 'ERROR'}}
WARNING:root:POST failure.  No data in API response.
INFO:fmc_migration_v2:BULK_API_COMPLETE: 66 objects in 0.29s (230.1 obj/s)
ERROR:fmc_migration_v2:Bulk operation failed for PaceGlobalgrp: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for timeservice: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for timeserviceUDP: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for QUEST: Bulk validation error: {}
ERROR:fmc_migration_v2:Bulk operation failed for citrixXML: Bulk validation error: {}
INFO:fmc_migration_v2:[FILE] Enhanced checkpoint saved: migration_checkpoints\migration_1754340331_phase1_service_groups.json
INFO:fmc_migration_v2:BULK_API_ENABLED: Processing 224 objects with bulk API
INFO:fmc_migration_v2:No API delay settings found to optimize
INFO:fmc_migration_v2:BULK_API_START: Beginning bulk operation for 224 access_rules
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 100 objects
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_1', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_2', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_3', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_4', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_5', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_6', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_7', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_8', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_9', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_10', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_11', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_12', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_13', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_14', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_15', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_16', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_17', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_18', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_19', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_20', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_21', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_22', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_23', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_24', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_25', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_26', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_27', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_28', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_29', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_30', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_31', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_32', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_33', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_34', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_35', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_36', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_37', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_38', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_39', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_40', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_41', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_42', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_43', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_44', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_45', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_46', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_47', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_48', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_access_in_rule_49', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
WARNING:root:post() method failed due to failure to pass valid_for_post() test.
DEBUG:fmc_migration_v2:bulk_post failed: 'bool' object is not subscriptable, falling back to individual creation
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 100 objects
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_11', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_12', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_13', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_14', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_15', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_16', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_17', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_18', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_19', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_20', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_21', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_22', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_23', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_24', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_25', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_26', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_27', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_28', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_29', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_30', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_31', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_32', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_33', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_34', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_35', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_36', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_37', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_38', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_39', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_40', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_41', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_42', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_43', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_44', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_45', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_46', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_47', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_48', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_49', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_50', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_access_in_rule_51', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'DMZ_access_in_V1_rule_1', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'DMZ_access_in_V1_rule_2', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'DMZ_access_in_V1_rule_3', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'DMZ_access_in_V1_rule_4', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'DMZ_access_in_V1_rule_5', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'DMZ_access_in_V1_rule_6', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_nat0_outbound_rule_1', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'inside_nat0_outbound_rule_2', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
WARNING:root:post() method failed due to failure to pass valid_for_post() test.
DEBUG:fmc_migration_v2:bulk_post failed: 'bool' object is not subscriptable, falling back to individual creation
DEBUG:fmc_migration_v2:Using fmcapi bulk_post for 24 objects
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'sfr_redirect_rule_1', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'Guest_access_in_rule_1', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'Guest_access_in_rule_2', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'Guest_access_in_rule_3', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'Vendor_access_in_rule_1', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'Vendor_access_in_rule_2', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'Vendor_access_in_rule_3', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'Vendor_access_in_rule_4', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'Vendor_access_in_rule_5', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_1', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_2', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_3', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_4', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_5', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_6', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_7', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_8', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_9', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_10', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_11', 'action': 'BLOCK', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_12', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'AnyConnect_Client_Local_Print_rule_13', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_cryptomap_4_rule_1', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
ERROR:root:BULK POST FAILED: Missing value "acp_id" in {'name': 'outside_cryptomap_8_rule_1', 'action': 'ALLOW', 'sourceNetworks': None, 'destinationNetworks': None, 'sourcePorts': None, 'destinationPorts': None, 'applications': None, 'enabled': True, 'logBegin': False, 'logEnd': True}
WARNING:root:post() method failed due to failure to pass valid_for_post() test.
DEBUG:fmc_migration_v2:bulk_post failed: 'bool' object is not subscriptable, falling back to individual creation
INFO:fmc_migration_v2:BULK_API_COMPLETE: 224 objects in 0.02s (13431.0 obj/s)
ERROR:fmc_migration_v2:Bulk operation failed for inside_access_in_rule_1: Bulk validation error: {'error': "'bool' object is not subscriptable"}
ERROR:fmc_migration_v2:Bulk operation failed for inside_access_in_rule_2: Bulk validation error: {'error': "'bool' object is not subscriptable"}
ERROR:fmc_migration_v2:Bulk operation failed for inside_access_in_rule_3: Bulk validation error: {'error': "'bool' object is not subscriptable"}
ERROR:fmc_migration_v2:Bulk operation failed for inside_access_in_rule_4: Bulk validation error: {'error': "'bool' object is not subscriptable"}
ERROR:fmc_migration_v2:Bulk operation failed for inside_access_in_rule_5: Bulk validation error: {'error': "'bool' object is not subscriptable"}
INFO:fmc_migration_v2:[FILE] Enhanced checkpoint saved: migration_checkpoints\migration_1754340331_phase1_access_rules.json
INFO:fmc_migration_v2:================================================================================
INFO:fmc_migration_v2:MIGRATION SUMMARY
INFO:fmc_migration_v2:================================================================================
INFO:fmc_migration_v2:[INFO] OVERALL RESULTS:
INFO:fmc_migration_v2:   • Total Objects: 1011
INFO:fmc_migration_v2:   • Created: 0
INFO:fmc_migration_v2:   • Updated: 0
INFO:fmc_migration_v2:   • Failed: 1011
INFO:fmc_migration_v2:   • Skipped: 0
INFO:fmc_migration_v2:   • Success Rate: 0.0%
INFO:fmc_migration_v2:================================================================================
INFO:fmc_migration_v2:[FILE] Summary saved: migration_summary_migration_1754340331.json
WARNING:fmc_migration_v2:[WARN]  Migration completed with 1011 failures