#!/usr/bin/env python3
"""
Test script to validate 100% success rate migration improvements
"""

import json
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from fmc_migration_v2 import FMCMigrationEngine

def create_test_config():
    """Create a comprehensive test configuration with various object types"""
    test_config = {
        "metadata": {
            "source": "100% Success Rate Test",
            "timestamp": "2025-08-04",
            "total_objects": {
                "host_objects": 5,
                "network_objects": 3,
                "service_objects": 4,
                "object_groups": 2,
                "service_groups": 1,
                "access_rules": 2
            }
        },
        "api_calls": {
            "host_objects": {
                "endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/hosts",
                "method": "POST",
                "data": [
                    {
                        "name": "TestHost_WebServer",
                        "type": "Host",
                        "value": "*************",
                        "description": "Test web server host",
                        "overridable": False
                    },
                    {
                        "name": "TestHost_DatabaseServer",
                        "type": "Host",
                        "value": "*************",
                        "description": "Test database server host",
                        "overridable": False
                    },
                    {
                        "name": "TestHost_FileServer",
                        "type": "Host",
                        "value": "*************",
                        "description": "Test file server host",
                        "overridable": False
                    },
                    {
                        "name": "TestHost_PrintServer",
                        "type": "Host",
                        "value": "************",
                        "description": "Test print server host",
                        "overridable": False
                    },
                    {
                        "name": "TestHost_BackupServer",
                        "type": "Host",
                        "value": "*************",
                        "description": "Test backup server host",
                        "overridable": False
                    }
                ]
            },
            "network_objects": {
                "endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/networks",
                "method": "POST",
                "data": [
                    {
                        "name": "TestNetwork_LAN",
                        "type": "Network",
                        "value": "***********/24",
                        "description": "Test LAN network",
                        "overridable": False
                    },
                    {
                        "name": "TestNetwork_DMZ",
                        "type": "Network",
                        "value": "********/24",
                        "description": "Test DMZ network",
                        "overridable": False
                    },
                    {
                        "name": "TestNetwork_Management",
                        "type": "Network",
                        "value": "**********/24",
                        "description": "Test management network",
                        "overridable": False
                    }
                ]
            },
            "service_objects": {
                "endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/protocolportobjects",
                "method": "POST",
                "data": [
                    {
                        "name": "TestService_HTTP_8080",
                        "type": "ProtocolPortObject",
                        "protocol": "TCP",
                        "port": "8080",
                        "description": "Test HTTP service on port 8080"
                    },
                    {
                        "name": "TestService_HTTPS_8443",
                        "type": "ProtocolPortObject",
                        "protocol": "TCP",
                        "port": "8443",
                        "description": "Test HTTPS service on port 8443"
                    },
                    {
                        "name": "TestService_Database",
                        "type": "ProtocolPortObject",
                        "protocol": "TCP",
                        "port": "3306",
                        "description": "Test MySQL database service"
                    },
                    {
                        "name": "TestService_FTP",
                        "type": "ProtocolPortObject",
                        "protocol": "TCP",
                        "port": "21",
                        "description": "Test FTP service"
                    }
                ]
            },
            "object_groups": {
                "endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/networkgroups",
                "method": "POST",
                "data": [
                    {
                        "name": "TestGroup_Servers",
                        "type": "NetworkGroup",
                        "description": "Test server group",
                        "objects": [
                            {"name": "TestHost_WebServer", "type": "Host"},
                            {"name": "TestHost_DatabaseServer", "type": "Host"},
                            {"name": "TestHost_FileServer", "type": "Host"}
                        ]
                    },
                    {
                        "name": "TestGroup_Networks",
                        "type": "NetworkGroup",
                        "description": "Test network group",
                        "objects": [
                            {"name": "TestNetwork_LAN", "type": "Network"},
                            {"name": "TestNetwork_DMZ", "type": "Network"}
                        ]
                    }
                ]
            },
            "service_groups": {
                "endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/portobjectgroups",
                "method": "POST",
                "data": [
                    {
                        "name": "TestGroup_WebServices",
                        "type": "PortObjectGroup",
                        "description": "Test web services group",
                        "objects": [
                            {"name": "TestService_HTTP_8080", "type": "ProtocolPortObject"},
                            {"name": "TestService_HTTPS_8443", "type": "ProtocolPortObject"}
                        ]
                    }
                ]
            },
            "access_rules": {
                "endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/policy/accesspolicies/{acp_id}/accessrules",
                "method": "POST",
                "data": [
                    {
                        "name": "TestRule_AllowWeb",
                        "type": "AccessRule",
                        "action": "ALLOW",
                        "enabled": True,
                        "sourceNetworks": [
                            {"name": "TestNetwork_LAN", "type": "Network"}
                        ],
                        "destinationNetworks": [
                            {"name": "TestHost_WebServer", "type": "Host"}
                        ],
                        "destinationPorts": [
                            {"name": "TestService_HTTP_8080", "type": "ProtocolPortObject"}
                        ],
                        "logBegin": False,
                        "logEnd": True
                    },
                    {
                        "name": "TestRule_AllowDatabase",
                        "type": "AccessRule",
                        "action": "ALLOW",
                        "enabled": True,
                        "sourceNetworks": [
                            {"name": "TestHost_WebServer", "type": "Host"}
                        ],
                        "destinationNetworks": [
                            {"name": "TestHost_DatabaseServer", "type": "Host"}
                        ],
                        "destinationPorts": [
                            {"name": "TestService_Database", "type": "ProtocolPortObject"}
                        ],
                        "logBegin": False,
                        "logEnd": True
                    }
                ]
            }
        }
    }
    
    return test_config

def run_100_percent_test():
    """Run the 100% success rate test"""
    print("🚀 Starting 100% Success Rate Migration Test...")
    
    # Create test configuration
    test_config = create_test_config()
    
    # Save test configuration
    config_file = "test_100_percent_config.json"
    with open(config_file, 'w') as f:
        json.dump(test_config, f, indent=2)
    
    print(f"📁 Created test configuration: {config_file}")
    
    try:
        # Initialize migration engine
        engine = FMCMigrationEngine(
            verify_ssl=False,
            overwrite=True,
            quiet=False,
            use_bulk_api=True
        )
        
        print("🔍 Running pre-migration validation...")
        pre_validation = engine.pre_migration_validation(config_file)
        
        if not pre_validation.get('success', False):
            print("❌ Pre-migration validation failed!")
            return False
        
        print("✅ Pre-migration validation passed!")
        
        print("🚀 Starting migration...")
        results = engine.run_full_migration(config_file)
        
        # Analyze results
        total_objects = sum(r.total_objects for r in results.values())
        total_created = sum(r.created for r in results.values())
        total_updated = sum(r.updated for r in results.values())
        total_failed = sum(r.failed for r in results.values())
        total_skipped = sum(r.skipped for r in results.values())
        
        success_rate = ((total_created + total_updated + total_skipped) / total_objects * 100) if total_objects > 0 else 0
        
        print("\n" + "="*80)
        print("🎯 100% SUCCESS RATE TEST RESULTS")
        print("="*80)
        print(f"📊 Total Objects: {total_objects}")
        print(f"✅ Created: {total_created}")
        print(f"🔄 Updated: {total_updated}")
        print(f"❌ Failed: {total_failed}")
        print(f"⏭️ Skipped: {total_skipped}")
        print(f"🎯 Success Rate: {success_rate:.1f}%")
        
        # Check if we achieved 100% success
        if success_rate >= 100.0:
            print("\n🎉 SUCCESS! Achieved 100% migration success rate!")
            return True
        else:
            print(f"\n⚠️ Did not achieve 100% success rate. Current: {success_rate:.1f}%")
            
            # Show detailed failures
            for phase_name, result in results.items():
                if result.failed > 0:
                    print(f"\n❌ Failures in {phase_name}:")
                    for detail in result.details:
                        if "Failed" in detail or "❌" in detail:
                            print(f"   • {detail}")
            
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False
    
    finally:
        # Clean up test file
        if os.path.exists(config_file):
            os.remove(config_file)

if __name__ == "__main__":
    success = run_100_percent_test()
    sys.exit(0 if success else 1)
