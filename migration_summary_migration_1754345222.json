{"session_id": "migration_1754345222", "connection_type": "fmcapi", "timestamp": "2025-08-04T15:12:03.351535", "results": {"hosts": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 5, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created host: TestHost_WebServer", "✅ Created host: TestHost_DatabaseServer", "✅ Created host: TestHost_FileServer", "✅ Created host: TestHost_PrintServer", "✅ Created host: TestHost_BackupServer"], "duration_seconds": 38.063902139663696, "success_rate": 0.0}, "networks": {"phase_name": "phase1_networks", "total_objects": 3, "created": 3, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created network: TestNetwork_LAN", "✅ Created network: TestNetwork_DMZ", "✅ Created network: TestNetwork_Management"], "duration_seconds": 24.722725868225098, "success_rate": 0.0}, "services": {"phase_name": "phase1_services", "total_objects": 4, "created": 4, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created service: TestService_HTTP_8080", "✅ Created service: TestService_HTTPS_8443", "✅ Created service: TestService_Database", "✅ Created service: TestService_FTP"], "duration_seconds": 26.82383894920349, "success_rate": 0.0}, "object_groups": {"phase_name": "phase1_object_groups", "total_objects": 2, "created": 2, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created object_group: TestGroup_Servers", "✅ Created object_group: TestGroup_Networks"], "duration_seconds": 35.037697076797485, "success_rate": 0.0}, "service_groups": {"phase_name": "phase1_service_groups", "total_objects": 1, "created": 1, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created service_group: TestGroup_WebServices"], "duration_seconds": 12.20233702659607, "success_rate": 0.0}, "access_rules": {"phase_name": "phase1_access_rules", "total_objects": 2, "created": 0, "updated": 0, "failed": 2, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule_AllowWeb - Failed to get or create Access Control Policy after multiple attempts", "❌ Failed to create access_rule: TestRule_AllowDatabase - Failed to get or create Access Control Policy after multiple attempts"], "duration_seconds": 152.36607003211975, "success_rate": 0.0}}, "totals": {"total_objects": 17, "created": 15, "updated": 0, "failed": 2, "skipped": 0, "success_rate": 88.23529411764706}, "phantom_objects": []}