#!/usr/bin/env python3
"""
Clean up test environment and test 100% success rate migration
"""

import json
import sys
import os
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def cleanup_test_objects():
    """Clean up all test objects to start fresh"""
    print("🧹 Cleaning up test environment...")
    
    try:
        import fmcapi
        from dotenv import load_dotenv
        
        # Load environment variables
        load_dotenv()
        
        # Connect to FMC
        fmc_host = os.getenv('FMC_HOST', '').replace('https://', '').replace('http://', '')
        fmc = fmcapi.FMC(
            host=fmc_host,
            username=os.getenv('FMC_USERNAME'),
            password=os.getenv('FMC_PASSWORD'),
            autodeploy=False
        )
        
        with fmc as fmc_conn:
            print("🔗 Connected to FMC")
            
            # Clean up Access Control Policies (except default ones)
            print("🗑️ Cleaning up Access Control Policies...")
            try:
                acp_obj = fmcapi.AccessPolicies(fmc=fmc_conn)
                acp_obj.get()
                
                if hasattr(acp_obj, 'items') and acp_obj.items:
                    for policy in acp_obj.items:
                        policy_name = policy.get('name', '').lower()
                        if 'migration' in policy_name or 'asa' in policy_name or 'test' in policy_name:
                            try:
                                delete_acp = fmcapi.AccessPolicies(fmc=fmc_conn)
                                delete_acp.id = policy.get('id')
                                delete_acp.delete()
                                print(f"   ✅ Deleted ACP: {policy.get('name')}")
                            except Exception as e:
                                print(f"   ⚠️ Could not delete ACP {policy.get('name')}: {e}")
            except Exception as e:
                print(f"   ⚠️ Error cleaning ACPs: {e}")
            
            # Clean up test objects by name pattern
            object_types = [
                (fmcapi.Hosts, "Host objects"),
                (fmcapi.Networks, "Network objects"),
                (fmcapi.ProtocolPortObjects, "Protocol port objects"),
                (fmcapi.NetworkGroups, "Network groups"),
                (fmcapi.PortObjectGroups, "Port object groups")
            ]
            
            for obj_class, description in object_types:
                print(f"🗑️ Cleaning up {description}...")
                try:
                    obj = obj_class(fmc=fmc_conn)
                    obj.get()
                    
                    if hasattr(obj, 'items') and obj.items:
                        for item in obj.items:
                            item_name = item.get('name', '').lower()
                            if 'test' in item_name or item_name.startswith('test'):
                                try:
                                    delete_obj = obj_class(fmc=fmc_conn)
                                    delete_obj.id = item.get('id')
                                    delete_obj.delete()
                                    print(f"   ✅ Deleted: {item.get('name')}")
                                except Exception as e:
                                    print(f"   ⚠️ Could not delete {item.get('name')}: {e}")
                except Exception as e:
                    print(f"   ⚠️ Error cleaning {description}: {e}")
            
            print("✅ Cleanup completed")
            return True
            
    except Exception as e:
        print(f"❌ Cleanup failed: {e}")
        return False

def create_minimal_test_config():
    """Create a minimal test configuration for 100% success testing"""
    test_config = {
        "metadata": {
            "source": "100% Success Rate Test - Minimal",
            "timestamp": "2025-08-04",
            "total_objects": {
                "host_objects": 2,
                "network_objects": 1,
                "service_objects": 1,
                "access_rules": 1
            }
        },
        "api_calls": {
            "host_objects": {
                "endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/hosts",
                "method": "POST",
                "data": [
                    {
                        "name": "TestHost_Server1",
                        "type": "Host",
                        "value": "**************",
                        "description": "Test server 1",
                        "overridable": False
                    },
                    {
                        "name": "TestHost_Server2",
                        "type": "Host",
                        "value": "**************",
                        "description": "Test server 2",
                        "overridable": False
                    }
                ]
            },
            "network_objects": {
                "endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/networks",
                "method": "POST",
                "data": [
                    {
                        "name": "TestNetwork_Subnet",
                        "type": "Network",
                        "value": "*************/24",
                        "description": "Test subnet",
                        "overridable": False
                    }
                ]
            },
            "service_objects": {
                "endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/object/protocolportobjects",
                "method": "POST",
                "data": [
                    {
                        "name": "TestService_HTTP",
                        "type": "ProtocolPortObject",
                        "protocol": "TCP",
                        "port": "80",
                        "description": "Test HTTP service"
                    }
                ]
            },
            "access_rules": {
                "endpoint": "/api/fmc_config/v1/domain/{domain_uuid}/policy/accesspolicies/{acp_id}/accessrules",
                "method": "POST",
                "data": [
                    {
                        "name": "TestRule_AllowHTTP",
                        "type": "AccessRule",
                        "action": "ALLOW",
                        "enabled": True,
                        "sourceNetworks": [
                            {"name": "TestNetwork_Subnet", "type": "Network"}
                        ],
                        "destinationNetworks": [
                            {"name": "TestHost_Server1", "type": "Host"}
                        ],
                        "destinationPorts": [
                            {"name": "TestService_HTTP", "type": "ProtocolPortObject"}
                        ],
                        "logBegin": False,
                        "logEnd": True
                    }
                ]
            }
        }
    }
    
    return test_config

def run_100_percent_test():
    """Run the 100% success rate test with clean environment"""
    print("🚀 Starting 100% Success Rate Test (Clean Environment)...")
    
    # Clean up first
    if not cleanup_test_objects():
        print("❌ Cleanup failed, aborting test")
        return False
    
    # Wait a moment for cleanup to complete
    time.sleep(2)
    
    # Create minimal test configuration
    test_config = create_minimal_test_config()
    
    # Save test configuration
    config_file = "test_minimal_100_percent.json"
    with open(config_file, 'w') as f:
        json.dump(test_config, f, indent=2)
    
    print(f"📁 Created minimal test configuration: {config_file}")
    
    try:
        from fmc_migration_v2 import FMCMigrationEngine
        
        # Initialize migration engine
        engine = FMCMigrationEngine(
            verify_ssl=False,
            overwrite=True,
            quiet=False,
            use_bulk_api=True
        )
        
        print("🔍 Running pre-migration validation...")
        pre_validation = engine.pre_migration_validation(config_file)
        
        if not pre_validation.get('success', False):
            print("❌ Pre-migration validation failed!")
            return False
        
        print("✅ Pre-migration validation passed!")
        
        print("🚀 Starting migration...")
        results = engine.run_full_migration(config_file)
        
        # Analyze results
        total_objects = sum(r.total_objects for r in results.values())
        total_created = sum(r.created for r in results.values())
        total_updated = sum(r.updated for r in results.values())
        total_failed = sum(r.failed for r in results.values())
        total_skipped = sum(r.skipped for r in results.values())
        
        success_rate = ((total_created + total_updated + total_skipped) / total_objects * 100) if total_objects > 0 else 0
        
        print("\n" + "="*80)
        print("🎯 100% SUCCESS RATE TEST RESULTS (CLEAN ENVIRONMENT)")
        print("="*80)
        print(f"📊 Total Objects: {total_objects}")
        print(f"✅ Created: {total_created}")
        print(f"🔄 Updated: {total_updated}")
        print(f"❌ Failed: {total_failed}")
        print(f"⏭️ Skipped: {total_skipped}")
        print(f"🎯 Success Rate: {success_rate:.1f}%")
        
        # Show detailed results by phase
        for phase_name, result in results.items():
            print(f"\n📋 {phase_name}:")
            print(f"   Objects: {result.total_objects}")
            print(f"   Created: {result.created}")
            print(f"   Updated: {result.updated}")
            print(f"   Failed: {result.failed}")
            print(f"   Success Rate: {result.success_rate:.1f}%")
            
            if result.failed > 0:
                print(f"   ❌ Failures:")
                for detail in result.details:
                    if "Failed" in detail or "❌" in detail:
                        print(f"      • {detail}")
        
        # Check if we achieved 100% success
        if success_rate >= 100.0 and total_failed == 0:
            print("\n🎉 SUCCESS! Achieved 100% migration success rate!")
            return True
        else:
            print(f"\n⚠️ Did not achieve 100% success rate. Current: {success_rate:.1f}%")
            print(f"   Failed objects: {total_failed}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up test file
        if os.path.exists(config_file):
            os.remove(config_file)

if __name__ == "__main__":
    success = run_100_percent_test()
    sys.exit(0 if success else 1)
