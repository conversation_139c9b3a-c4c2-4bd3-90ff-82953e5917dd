{"session_id": "migration_1754344505", "connection_type": "fmcapi", "timestamp": "2025-08-04T14:56:19.517682", "results": {"hosts": {"phase_name": "phase1_hosts", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost1", "✅ Updated host: TestHost2", "✅ Updated host: TestHost3"], "duration_seconds": 23.196524143218994, "success_rate": 0.0}, "networks": {"phase_name": "phase1_networks", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork1", "✅ Updated network: TestNetwork2"], "duration_seconds": 17.114325046539307, "success_rate": 0.0}, "services": {"phase_name": "phase1_services", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestHTTP", "✅ Updated service: TestHTTPS", "✅ Updated service: TestSSH"], "duration_seconds": 9.569133996963501, "success_rate": 0.0}, "object_groups": {"phase_name": "phase1_object_groups", "total_objects": 1, "created": 0, "updated": 1, "failed": 0, "skipped": 0, "details": ["✅ Updated object_group: TestNetworkGroup"], "duration_seconds": 3.3345048427581787, "success_rate": 0.0}, "service_groups": {"phase_name": "phase1_service_groups", "total_objects": 1, "created": 0, "updated": 1, "failed": 0, "skipped": 0, "details": ["✅ Updated service_group: TestWebServices"], "duration_seconds": 3.1218998432159424, "success_rate": 0.0}, "access_rules": {"phase_name": "phase1_access_rules", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule1 - No Access Control Policy found - cannot create access rules"], "duration_seconds": 9.49411916732788, "success_rate": 0.0}}, "totals": {"total_objects": 11, "created": 0, "updated": 10, "failed": 1, "skipped": 0, "success_rate": 90.9090909090909}, "phantom_objects": []}