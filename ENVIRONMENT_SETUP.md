# Environment Setup Guide

## Overview
The FMC Migration Toolkit now supports environment variables for secure credential management. This allows you to easily switch between different FMC environments without modifying code.

## Quick Setup

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Environment Files
Three environment files are provided:

- **`.env`** - Default environment (currently set to DevNet)
- **`.env.devnet`** - DevNet Sandbox credentials
- **`.env.production`** - Production FMC credentials

### 3. Usage Examples

#### Use DevNet Sandbox (Default)
```bash
# Uses .env file automatically
python fmc_migration_v2.py fmc_migration_config.json --overwrite
```

#### Use DevNet Sandbox (Explicit)
```bash
python fmc_migration_v2.py fmc_migration_config.json --overwrite --env .env.devnet
```

#### Use Production Environment
```bash
python fmc_migration_v2.py fmc_migration_config.json --overwrite --env .env.production
```

#### Test with DevNet
```bash
python test_minimal_migration.py
```

## Environment Variables

### Required Variables
- `FMC_HOST` - FMC server URL (e.g., https://fmcrestapisandbox.cisco.com)
- `FMC_USERNAME` - FMC username
- `FMC_PASSWORD` - FMC password

### Optional Variables
- `FMC_VERIFY_SSL` - SSL verification (default: false)
- `FMC_AUTODEPLOY` - Auto-deploy changes (default: false)

## Security Notes

### ⚠️ Important Security Considerations

1. **Never commit .env files to version control**
   ```bash
   # Add to .gitignore
   echo ".env*" >> .gitignore
   ```

2. **Use different passwords for different environments**

3. **Rotate credentials regularly**

4. **Consider using more secure credential management for production**

## Credentials Reference

### DevNet Sandbox
- **URL**: https://fmcrestapisandbox.cisco.com
- **Username**: taylorsm
- **Password**: xwF27btUk2L#5U8*
- **Purpose**: Development and testing

### Production Environment
- **URL**: https://*************
- **Username**: admin
- **Password**: !Techn0l0gy01!
- **Purpose**: Production migrations (via Citrix/RDP)

## Troubleshooting

### Missing python-dotenv
```bash
pip install python-dotenv
```

### Environment not loading
1. Check file exists: `ls -la .env*`
2. Check file format (no spaces around =)
3. Use explicit --env flag

### Connection issues
1. Verify credentials in environment file
2. Test with curl:
   ```bash
   curl -k -X POST https://fmcrestapisandbox.cisco.com/api/fmc_platform/v1/auth/generatetoken \
     -H "Content-Type: application/json" \
     -d '{"grant_type": "password", "username": "taylorsm", "password": "xwF27btUk2L#5U8*"}'
   ```

## Migration from Hardcoded Credentials

If you have existing scripts with hardcoded credentials:

1. **Create environment file**:
   ```bash
   cp .env.production .env.custom
   # Edit .env.custom with your credentials
   ```

2. **Update script calls**:
   ```bash
   # Old way
   python fmc_migration_v2.py config.json --overwrite
   
   # New way
   python fmc_migration_v2.py config.json --overwrite --env .env.custom
   ```

3. **Remove hardcoded credentials** from scripts

## Benefits

✅ **Security**: Credentials not in code  
✅ **Flexibility**: Easy environment switching  
✅ **Development**: Fast DevNet testing  
✅ **Production**: Secure production deployment  
✅ **Team**: Different team members can use different credentials  
