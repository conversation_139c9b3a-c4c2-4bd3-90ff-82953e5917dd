#!/usr/bin/env python3
"""
FMC Migration Testing Framework
Comprehensive testing framework for validating FMC migration functionality.
"""

import json
import sys
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from fmc_migration_v2 import FMCMigrationEngine

class MigrationTestFramework:
    """Testing framework for FMC migration"""
    
    def __init__(self, fmc_host: str, username: str, password: str):
        self.fmc_host = fmc_host
        self.username = username
        self.password = password
        self.test_results = []
        
    def run_test(self, test_name: str, test_func, *args, **kwargs) -> bool:
        """Run a single test and record results"""
        print(f"🧪 Running test: {test_name}")
        start_time = time.time()
        
        try:
            result = test_func(*args, **kwargs)
            duration = time.time() - start_time
            
            if result:
                print(f"✅ PASSED: {test_name} ({duration:.2f}s)")
                self.test_results.append({
                    'name': test_name,
                    'status': 'PASSED',
                    'duration': duration,
                    'timestamp': datetime.now().isoformat()
                })
                return True
            else:
                print(f"❌ FAILED: {test_name} ({duration:.2f}s)")
                self.test_results.append({
                    'name': test_name,
                    'status': 'FAILED',
                    'duration': duration,
                    'timestamp': datetime.now().isoformat()
                })
                return False
                
        except Exception as e:
            duration = time.time() - start_time
            print(f"💥 ERROR: {test_name} ({duration:.2f}s) - {e}")
            self.test_results.append({
                'name': test_name,
                'status': 'ERROR',
                'duration': duration,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            return False
    
    def test_connection(self) -> bool:
        """Test FMC connection"""
        try:
            engine = FMCMigrationEngine(
                fmc_host=self.fmc_host,
                username=self.username,
                password=self.password,
                verify_ssl=False,
                quiet=True
            )
            return True
        except Exception as e:
            print(f"Connection failed: {e}")
            return False
    
    def test_minimal_config(self) -> bool:
        """Test migration with minimal configuration"""
        config_file = "minimal_validation_config.json"
        
        if not os.path.exists(config_file):
            print(f"Minimal config file not found: {config_file}")
            return False
        
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            engine = FMCMigrationEngine(
                fmc_host=self.fmc_host,
                username=self.username,
                password=self.password,
                verify_ssl=False,
                overwrite=False,
                quiet=True
            )
            
            results = engine.migrate_configuration(config)
            
            # Check if any objects were successfully processed
            total_success = 0
            total_objects = 0
            
            for phase_name, result in results.items():
                if hasattr(result, 'total_objects'):
                    total_objects += result.total_objects
                    total_success += result.created + result.updated
            
            # Consider test passed if at least 80% success rate
            success_rate = (total_success / total_objects * 100) if total_objects > 0 else 0
            return success_rate >= 80
            
        except Exception as e:
            print(f"Minimal config test failed: {e}")
            return False
    
    def test_data_validation(self) -> bool:
        """Test data validation functionality"""
        try:
            # Test with known good config
            good_config = {
                "api_calls": {
                    "host_objects": {
                        "data": [
                            {
                                "name": "TestHost",
                                "type": "Host",
                                "value": "***********",
                                "description": "Test host"
                            }
                        ]
                    }
                }
            }
            
            # Save temporary config
            temp_file = "temp_test_config.json"
            with open(temp_file, 'w') as f:
                json.dump(good_config, f)
            
            # Run validation
            from validate_config_data import validate_configuration
            result = validate_configuration(temp_file)
            
            # Clean up
            os.remove(temp_file)
            
            return result
            
        except Exception as e:
            print(f"Data validation test failed: {e}")
            return False
    
    def test_object_creation(self) -> bool:
        """Test individual object creation"""
        try:
            from fmc_migration_v2 import HostObject
            
            # Create a mock FMC connection for testing
            class MockFMC:
                def __enter__(self):
                    return self
                def __exit__(self, *args):
                    pass
            
            # Test object creation
            host_obj = HostObject(
                MockFMC(),
                name="TestHost",
                value="*************",
                description="Test host object"
            )
            
            # Validate data
            issues = host_obj.validate_data()
            return len(issues) == 0
            
        except Exception as e:
            print(f"Object creation test failed: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and return summary"""
        print("=" * 80)
        print("🧪 FMC Migration Testing Framework")
        print("=" * 80)
        print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔗 FMC Host: {self.fmc_host}")
        print(f"👤 Username: {self.username}")
        print()
        
        # Run tests
        tests = [
            ("Connection Test", self.test_connection),
            ("Data Validation Test", self.test_data_validation),
            ("Object Creation Test", self.test_object_creation),
            ("Minimal Config Test", self.test_minimal_config),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            if self.run_test(test_name, test_func):
                passed += 1
            print()
        
        # Summary
        print("=" * 80)
        print("📊 Test Summary")
        print("=" * 80)
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed / total * 100):.1f}%")
        
        # Detailed results
        print()
        print("📋 Detailed Results:")
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'PASSED' else "❌" if result['status'] == 'FAILED' else "💥"
            print(f"  {status_icon} {result['name']}: {result['status']} ({result['duration']:.2f}s)")
            if 'error' in result:
                print(f"     Error: {result['error']}")
        
        return {
            'total_tests': total,
            'passed': passed,
            'failed': total - passed,
            'success_rate': passed / total * 100,
            'results': self.test_results
        }

def main():
    """Main function"""
    # Configuration
    fmc_host = "https://*************"  # Update this
    username = "admin"                   # Update this
    password = "!Techn0l0gy01!"         # Update this
    
    print("FMC Migration Testing Framework")
    print("This will run comprehensive tests to validate the migration system.")
    print()
    print("⚠️  WARNING: This will attempt to connect to FMC and may create test objects.")
    print("   Make sure you're testing against a development/test FMC instance.")
    print()
    
    response = input("Do you want to proceed with testing? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Testing cancelled by user.")
        return
    
    # Run tests
    framework = MigrationTestFramework(fmc_host, username, password)
    summary = framework.run_all_tests()
    
    # Save results
    results_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print()
    print(f"📄 Test results saved to: {results_file}")
    
    # Exit with appropriate code
    sys.exit(0 if summary['success_rate'] >= 75 else 1)

if __name__ == "__main__":
    main()
