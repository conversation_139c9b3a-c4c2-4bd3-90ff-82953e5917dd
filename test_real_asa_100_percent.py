#!/usr/bin/env python3
"""
Test 100% success rate with real ASA configuration (excluding access rules)
"""

import json
import sys
import os
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_real_asa_migration():
    """Test migration with real ASA configuration"""
    print("🚀 Testing 100% Success Rate with Real ASA Configuration...")
    
    config_file = "fmc_migration_config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ Configuration file {config_file} not found!")
        return False
    
    try:
        from fmc_migration_v2 import FMCMigrationEngine
        
        # Initialize migration engine
        engine = FMCMigrationEngine(
            verify_ssl=False,
            overwrite=True,
            quiet=False,
            use_bulk_api=True
        )
        
        print("🔍 Running pre-migration validation...")
        pre_validation = engine.pre_migration_validation(config_file)
        
        if not pre_validation.get('success', False):
            print("❌ Pre-migration validation failed!")
            return False
        
        print("✅ Pre-migration validation passed!")
        
        # Load config to see what we're working with
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        total_objects = 0
        for section in ['host_objects', 'network_objects', 'service_objects', 'object_groups', 'service_groups']:
            if section in config.get('api_calls', {}):
                count = len(config['api_calls'][section].get('data', []))
                total_objects += count
                print(f"📊 {section}: {count} objects")
        
        print(f"📊 Total core objects to migrate: {total_objects}")
        
        print("🚀 Starting migration (excluding access rules)...")
        
        # Run migration for core objects only
        results = {}
        
        # Import the object classes
        from fmc_migration_v2 import HostObject, NetworkObject, ProtocolPortObject, NetworkGroup, PortObjectGroup

        # Migrate hosts
        if 'host_objects' in config.get('api_calls', {}):
            print("🏠 Migrating Host Objects...")
            results['hosts'] = engine.migrate_objects(
                'hosts',
                config['api_calls']['host_objects']['data'],
                HostObject,
                "Host Objects"
            )

        # Migrate networks
        if 'network_objects' in config.get('api_calls', {}):
            print("🌐 Migrating Network Objects...")
            results['networks'] = engine.migrate_objects(
                'networks',
                config['api_calls']['network_objects']['data'],
                NetworkObject,
                "Network Objects"
            )

        # Migrate services
        if 'service_objects' in config.get('api_calls', {}):
            print("🔧 Migrating Protocol Port Objects...")
            results['services'] = engine.migrate_objects(
                'services',
                config['api_calls']['service_objects']['data'],
                ProtocolPortObject,
                "Protocol Port Objects"
            )

        # Migrate object groups
        if 'object_groups' in config.get('api_calls', {}):
            print("📦 Migrating Network Groups...")
            results['object_groups'] = engine.migrate_objects(
                'object_groups',
                config['api_calls']['object_groups']['data'],
                NetworkGroup,
                "Network Groups"
            )

        # Migrate service groups
        if 'service_groups' in config.get('api_calls', {}):
            print("🔗 Migrating Port Object Groups...")
            results['service_groups'] = engine.migrate_objects(
                'service_groups',
                config['api_calls']['service_groups']['data'],
                PortObjectGroup,
                "Port Object Groups"
            )
        
        # Analyze results
        total_objects = sum(r.total_objects for r in results.values())
        total_created = sum(r.created for r in results.values())
        total_updated = sum(r.updated for r in results.values())
        total_failed = sum(r.failed for r in results.values())
        total_skipped = sum(r.skipped for r in results.values())
        
        success_rate = ((total_created + total_updated + total_skipped) / total_objects * 100) if total_objects > 0 else 0
        
        print("\n" + "="*80)
        print("🎯 REAL ASA CONFIGURATION - 100% SUCCESS RATE TEST")
        print("="*80)
        print(f"📊 Total Core Objects: {total_objects}")
        print(f"✅ Created: {total_created}")
        print(f"🔄 Updated: {total_updated}")
        print(f"❌ Failed: {total_failed}")
        print(f"⏭️ Skipped: {total_skipped}")
        print(f"🎯 Success Rate: {success_rate:.1f}%")
        
        # Show detailed results by phase
        for phase_name, result in results.items():
            print(f"\n📋 {phase_name}:")
            print(f"   Objects: {result.total_objects}")
            print(f"   Created: {result.created}")
            print(f"   Updated: {result.updated}")
            print(f"   Failed: {result.failed}")
            print(f"   Success Rate: {result.success_rate:.1f}%")
            
            if result.failed > 0:
                print(f"   ❌ Failures:")
                for detail in result.details:
                    if "Failed" in detail or "❌" in detail:
                        print(f"      • {detail}")
        
        # Check if we achieved 100% success for core objects
        if success_rate >= 100.0 and total_failed == 0:
            print("\n🎉 SUCCESS! Achieved 100% migration success rate for all core ASA objects!")
            print("📝 Note: Access rules were excluded from this test due to sandbox API limitations")
            return True
        else:
            print(f"\n⚠️ Did not achieve 100% success rate. Current: {success_rate:.1f}%")
            print(f"   Failed objects: {total_failed}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_idempotency_test():
    """Test that migration can run multiple times with same results"""
    print("\n🔄 Testing Idempotency - Running migration twice...")
    
    # Run first migration
    print("🚀 First migration run...")
    first_result = test_real_asa_migration()
    
    if not first_result:
        print("❌ First migration failed, skipping idempotency test")
        return False
    
    # Wait a moment
    time.sleep(2)
    
    # Run second migration
    print("\n🚀 Second migration run (idempotency test)...")
    second_result = test_real_asa_migration()
    
    if second_result:
        print("\n✅ IDEMPOTENCY TEST PASSED!")
        print("   Migration can be run multiple times with consistent results")
        return True
    else:
        print("\n❌ IDEMPOTENCY TEST FAILED!")
        print("   Second migration run had different results")
        return False

if __name__ == "__main__":
    # Test basic migration first
    basic_success = test_real_asa_migration()
    
    if basic_success:
        # Test idempotency
        idempotency_success = run_idempotency_test()
        sys.exit(0 if idempotency_success else 1)
    else:
        sys.exit(1)
