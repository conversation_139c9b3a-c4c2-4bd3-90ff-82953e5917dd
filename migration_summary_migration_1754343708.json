{"session_id": "migration_1754343708", "connection_type": "fmcapi", "timestamp": "2025-08-04T14:43:25.648354", "results": {"hosts": {"phase_name": "phase1_hosts", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost1", "✅ Updated host: TestHost2", "✅ Updated host: TestHost3"], "duration_seconds": 22.75806713104248, "success_rate": 0.0}, "networks": {"phase_name": "phase1_networks", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork1", "✅ Updated network: TestNetwork2"], "duration_seconds": 16.493091106414795, "success_rate": 0.0}, "services": {"phase_name": "phase1_services", "total_objects": 3, "created": 0, "updated": 0, "failed": 3, "skipped": 0, "details": ["❌ Failed to update service: TestHTTP - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: TestHTTPS - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: TestSSH - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType"], "duration_seconds": 18.183600902557373, "success_rate": 0.0}, "object_groups": {"phase_name": "phase1_object_groups", "total_objects": 1, "created": 1, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created object_group: TestNetworkGroup"], "duration_seconds": 15.315642833709717, "success_rate": 0.0}, "service_groups": {"phase_name": "phase1_service_groups", "total_objects": 1, "created": 1, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created service_group: TestWebServices"], "duration_seconds": 12.58578896522522, "success_rate": 0.0}, "access_rules": {"phase_name": "phase1_access_rules", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule1 - fmcapi post() failed - no ID returned. Result: False"], "duration_seconds": 9.204065084457397, "success_rate": 0.0}}, "totals": {"total_objects": 11, "created": 2, "updated": 5, "failed": 4, "skipped": 0, "success_rate": 63.63636363636363}, "phantom_objects": []}