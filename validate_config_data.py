#!/usr/bin/env python3
"""
Configuration Data Validator
Validates the FMC migration configuration data to identify potential issues
before attempting migration.
"""

import json
import sys
import ipaddress
from typing import Dict, List, Any
from datetime import datetime

def validate_host_objects(hosts: List[Dict]) -> List[str]:
    """Validate host object data"""
    issues = []
    
    for i, host in enumerate(hosts):
        prefix = f"Host {i+1} ({host.get('name', 'UNKNOWN')})"
        
        # Check required fields
        if not host.get('name'):
            issues.append(f"{prefix}: Missing name")
        if not host.get('value'):
            issues.append(f"{prefix}: Missing value (IP address)")
        if not host.get('type'):
            issues.append(f"{prefix}: Missing type")
        
        # Validate IP address
        if host.get('value'):
            try:
                ipaddress.ip_address(host['value'])
            except ValueError:
                issues.append(f"{prefix}: Invalid IP address '{host['value']}'")
        
        # Check for duplicate names
        name = host.get('name')
        if name:
            duplicates = [h for h in hosts if h.get('name') == name]
            if len(duplicates) > 1:
                issues.append(f"{prefix}: Duplicate name '{name}'")
    
    return issues

def validate_network_objects(networks: List[Dict]) -> List[str]:
    """Validate network object data"""
    issues = []
    
    for i, network in enumerate(networks):
        prefix = f"Network {i+1} ({network.get('name', 'UNKNOWN')})"
        
        # Check required fields
        if not network.get('name'):
            issues.append(f"{prefix}: Missing name")
        if not network.get('value'):
            issues.append(f"{prefix}: Missing value (network CIDR)")
        if not network.get('type'):
            issues.append(f"{prefix}: Missing type")
        
        # Validate network CIDR or FQDN
        if network.get('value'):
            obj_type = network.get('type', 'Network')
            if obj_type == 'FQDN':
                # For FQDN objects, just check that it looks like a domain name
                value = network['value']
                if not ('.' in value and any(c.isalpha() for c in value)):
                    issues.append(f"{prefix}: Invalid FQDN '{value}'")
            else:
                # For Network objects, validate CIDR
                try:
                    ipaddress.ip_network(network['value'], strict=False)
                except ValueError:
                    issues.append(f"{prefix}: Invalid network CIDR '{network['value']}'")

        # Check for duplicate names
        name = network.get('name')
        if name:
            duplicates = [n for n in networks if n.get('name') == name]
            if len(duplicates) > 1:
                issues.append(f"{prefix}: Duplicate name '{name}'")
    
    return issues

def validate_service_objects(services: List[Dict]) -> List[str]:
    """Validate service object data"""
    issues = []
    
    for i, service in enumerate(services):
        prefix = f"Service {i+1} ({service.get('name', 'UNKNOWN')})"
        
        # Check required fields
        if not service.get('name'):
            issues.append(f"{prefix}: Missing name")
        if not service.get('protocol'):
            issues.append(f"{prefix}: Missing protocol")
        if not service.get('port'):
            issues.append(f"{prefix}: Missing port")
        
        # Validate protocol
        protocol = service.get('protocol', '').upper()
        if protocol and protocol not in ['TCP', 'UDP', 'ICMP']:
            issues.append(f"{prefix}: Invalid protocol '{protocol}' (should be TCP, UDP, or ICMP)")
        
        # Validate port
        port = service.get('port')
        if port:
            try:
                port_num = int(port)
                if not (1 <= port_num <= 65535):
                    issues.append(f"{prefix}: Invalid port number {port_num} (must be 1-65535)")
            except ValueError:
                # Port might be a service name like "www", which is valid for some cases
                if port not in ['www', 'https', 'ssh', 'ftp', 'telnet', 'smtp', 'dns', 'snmp']:
                    issues.append(f"{prefix}: Unrecognized port value '{port}' (use number or standard service name)")
        
        # Check for duplicate names
        name = service.get('name')
        if name:
            duplicates = [s for s in services if s.get('name') == name]
            if len(duplicates) > 1:
                issues.append(f"{prefix}: Duplicate name '{name}'")
    
    return issues

def validate_object_groups(groups: List[Dict], available_objects: Dict) -> List[str]:
    """Validate object group data"""
    issues = []
    
    for i, group in enumerate(groups):
        prefix = f"Group {i+1} ({group.get('name', 'UNKNOWN')})"
        
        # Check required fields
        if not group.get('name'):
            issues.append(f"{prefix}: Missing name")
        if not group.get('objects'):
            issues.append(f"{prefix}: Missing objects list")
        
        # Validate object references
        objects = group.get('objects', [])
        for j, obj_ref in enumerate(objects):
            if not obj_ref.get('name'):
                issues.append(f"{prefix}, Object {j+1}: Missing object name")
            else:
                obj_name = obj_ref['name']
                obj_type = obj_ref.get('type', '')
                
                # Check if referenced object exists
                found = False
                for obj_category in available_objects.values():
                    if obj_name in obj_category:
                        found = True
                        break
                
                if not found:
                    issues.append(f"{prefix}: References non-existent object '{obj_name}'")
    
    return issues

def validate_configuration(config_file: str) -> bool:
    """Validate the entire configuration file"""
    print("=" * 80)
    print("🔍 FMC Configuration Data Validator")
    print("=" * 80)
    print(f"📅 Validation Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📄 Config File: {config_file}")
    print()
    
    # Load configuration
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        print("✅ Configuration file loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return False
    
    all_issues = []
    
    # Validate host objects
    if 'api_calls' in config and 'host_objects' in config['api_calls']:
        hosts = config['api_calls']['host_objects'].get('data', [])
        print(f"🔍 Validating {len(hosts)} host objects...")
        host_issues = validate_host_objects(hosts)
        all_issues.extend(host_issues)
        if host_issues:
            print(f"⚠️  Found {len(host_issues)} host object issues")
        else:
            print("✅ Host objects validation passed")
    
    # Validate network objects
    if 'api_calls' in config and 'network_objects' in config['api_calls']:
        networks = config['api_calls']['network_objects'].get('data', [])
        print(f"🔍 Validating {len(networks)} network objects...")
        network_issues = validate_network_objects(networks)
        all_issues.extend(network_issues)
        if network_issues:
            print(f"⚠️  Found {len(network_issues)} network object issues")
        else:
            print("✅ Network objects validation passed")
    
    # Validate service objects
    if 'api_calls' in config and 'service_objects' in config['api_calls']:
        services = config['api_calls']['service_objects'].get('data', [])
        print(f"🔍 Validating {len(services)} service objects...")
        service_issues = validate_service_objects(services)
        all_issues.extend(service_issues)
        if service_issues:
            print(f"⚠️  Found {len(service_issues)} service object issues")
        else:
            print("✅ Service objects validation passed")
    
    # Validate object groups
    if 'api_calls' in config and 'object_groups' in config['api_calls']:
        groups = config['api_calls']['object_groups'].get('data', [])
        available_objects = config.get('object_lookup', {})
        print(f"🔍 Validating {len(groups)} object groups...")
        group_issues = validate_object_groups(groups, available_objects)
        all_issues.extend(group_issues)
        if group_issues:
            print(f"⚠️  Found {len(group_issues)} object group issues")
        else:
            print("✅ Object groups validation passed")
    
    print()
    print("=" * 80)
    
    if all_issues:
        print(f"❌ Validation FAILED - Found {len(all_issues)} issues:")
        print()
        for i, issue in enumerate(all_issues[:20], 1):  # Show first 20 issues
            print(f"{i:3d}. {issue}")
        
        if len(all_issues) > 20:
            print(f"... and {len(all_issues) - 20} more issues")
        
        print()
        print("💡 Fix these issues before attempting migration")
        return False
    else:
        print("🎉 Validation PASSED - Configuration data looks good!")
        print("✅ Ready for migration")
        return True

def main():
    """Main function"""
    config_file = "fmc_migration_config.json"
    
    if len(sys.argv) > 1:
        config_file = sys.argv[1]
    
    success = validate_configuration(config_file)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
