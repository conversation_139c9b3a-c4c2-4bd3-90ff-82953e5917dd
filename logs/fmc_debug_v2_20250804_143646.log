2025-08-04 14:36:46,169 | INFO | ================================================================================
2025-08-04 14:36:46,170 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 14:36:46,170 | INFO | Session ID: migration_1754343406
2025-08-04 14:36:46,170 | INFO | Connection Type: fmcapi
2025-08-04 14:36:46,170 | INFO | 🔍 Connection Diagnostic:
2025-08-04 14:36:46,170 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 14:36:46,170 | INFO |    • fmcapi Available: True
2025-08-04 14:36:46,170 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 14:36:48,799 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 14:36:48,799 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 14:36:48,799 | INFO | 🔍 Connection diagnostic complete
2025-08-04 14:36:48,799 | INFO | ================================================================================
2025-08-04 14:36:48,799 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:36:48,799 | INFO | No API delay settings found to optimize
2025-08-04 14:36:48,799 | DEBUG | Processing hosts 1: TestHost1
2025-08-04 14:36:48,799 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:36:48,799 | DEBUG | Object data: {'name': 'TestHost1', 'type': 'Host', 'value': '************', 'description': 'Test host object 1', 'overridable': False}
2025-08-04 14:36:52,459 | DEBUG | GET result for TestHost1: success=True, message='None'
2025-08-04 14:36:55,928 | DEBUG | PUT result for TestHost1: success=True, message='None'
2025-08-04 14:36:55,929 | DEBUG | Processing hosts 2: TestHost2
2025-08-04 14:36:55,929 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:36:55,929 | DEBUG | Object data: {'name': 'TestHost2', 'type': 'Host', 'value': '************', 'description': 'Test host object 2', 'overridable': False}
2025-08-04 14:36:59,736 | DEBUG | GET result for TestHost2: success=True, message='None'
2025-08-04 14:37:03,098 | DEBUG | PUT result for TestHost2: success=True, message='None'
2025-08-04 14:37:03,098 | DEBUG | Processing hosts 3: TestHost3
2025-08-04 14:37:03,098 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:37:03,099 | DEBUG | Object data: {'name': 'TestHost3', 'type': 'Host', 'value': '**********', 'description': 'Test host object 3', 'overridable': False}
2025-08-04 14:37:07,045 | DEBUG | GET result for TestHost3: success=True, message='None'
2025-08-04 14:37:10,267 | DEBUG | PUT result for TestHost3: success=True, message='None'
2025-08-04 14:37:10,270 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343406_phase1_hosts.json
2025-08-04 14:37:10,270 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 14:37:10,271 | INFO | No API delay settings found to optimize
2025-08-04 14:37:10,271 | DEBUG | Processing networks 1: TestNetwork1
2025-08-04 14:37:10,271 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:37:10,271 | DEBUG | Object data: {'name': 'TestNetwork1', 'type': 'Network', 'value': '*************/24', 'description': 'Test network object 1', 'overridable': False}
2025-08-04 14:37:14,732 | DEBUG | GET result for TestNetwork1: success=False, message='Object 'TestNetwork1' not found in FMC (will be created)'
2025-08-04 14:37:17,847 | DEBUG | POST result for TestNetwork1: success=True, message='None'
2025-08-04 14:37:17,848 | DEBUG | Processing networks 2: TestNetwork2
2025-08-04 14:37:17,848 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:37:17,848 | DEBUG | Object data: {'name': 'TestNetwork2', 'type': 'Network', 'value': '*********/16', 'description': 'Test network object 2', 'overridable': False}
2025-08-04 14:37:22,383 | DEBUG | GET result for TestNetwork2: success=False, message='Object 'TestNetwork2' not found in FMC (will be created)'
2025-08-04 14:37:25,522 | DEBUG | POST result for TestNetwork2: success=True, message='None'
2025-08-04 14:37:25,523 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343406_phase1_networks.json
2025-08-04 14:37:25,523 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:37:25,523 | INFO | No API delay settings found to optimize
2025-08-04 14:37:25,523 | DEBUG | Processing services 1: TestHTTP
2025-08-04 14:37:25,523 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:37:25,523 | DEBUG | Object data: {'name': 'TestHTTP', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '80', 'description': 'Test HTTP service'}
2025-08-04 14:37:28,538 | DEBUG | GET result for TestHTTP: success=False, message='Object 'TestHTTP' not found in FMC (will be created)'
2025-08-04 14:37:31,772 | DEBUG | POST result for TestHTTP: success=True, message='None'
2025-08-04 14:37:31,772 | DEBUG | Processing services 2: TestHTTPS
2025-08-04 14:37:31,772 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:37:31,772 | DEBUG | Object data: {'name': 'TestHTTPS', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '443', 'description': 'Test HTTPS service'}
2025-08-04 14:37:34,816 | DEBUG | GET result for TestHTTPS: success=False, message='Object 'TestHTTPS' not found in FMC (will be created)'
2025-08-04 14:37:37,971 | DEBUG | POST result for TestHTTPS: success=True, message='None'
2025-08-04 14:37:37,972 | DEBUG | Processing services 3: TestSSH
2025-08-04 14:37:37,972 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:37:37,972 | DEBUG | Object data: {'name': 'TestSSH', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '22', 'description': 'Test SSH service'}
2025-08-04 14:37:40,934 | DEBUG | GET result for TestSSH: success=False, message='Object 'TestSSH' not found in FMC (will be created)'
2025-08-04 14:37:44,070 | DEBUG | POST result for TestSSH: success=True, message='None'
2025-08-04 14:37:44,071 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343406_phase1_services.json
2025-08-04 14:37:44,071 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:37:44,071 | INFO | No API delay settings found to optimize
2025-08-04 14:37:44,071 | DEBUG | Processing object_groups 1: TestNetworkGroup
2025-08-04 14:37:44,071 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 14:37:44,072 | DEBUG | Object data: {'name': 'TestNetworkGroup', 'type': 'NetworkGroup', 'description': 'Test network group', 'objects': [{'name': 'TestNetwork1', 'type': 'NetworkObject'}, {'name': 'TestHost1', 'type': 'HostObject'}]}
2025-08-04 14:37:47,347 | DEBUG | GET result for TestNetworkGroup: success=False, message='Object 'TestNetworkGroup' not found in FMC (will be created)'
2025-08-04 14:37:50,049 | DEBUG | POST result for TestNetworkGroup: success=False, message='fmcapi post() failed - no ID returned.'
2025-08-04 14:37:50,049 | ERROR | Creation failed for TestNetworkGroup: fmcapi post() failed - no ID returned.
2025-08-04 14:37:50,050 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343406_phase1_object_groups.json
2025-08-04 14:37:50,050 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:37:50,050 | INFO | No API delay settings found to optimize
2025-08-04 14:37:50,050 | DEBUG | Processing service_groups 1: TestWebServices
2025-08-04 14:37:50,050 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 14:37:50,050 | DEBUG | Object data: {'name': 'TestWebServices', 'type': 'PortObjectGroup', 'description': 'Test web services group', 'objects': [{'name': 'TestHTTP', 'type': 'ProtocolPortObject'}, {'name': 'TestHTTPS', 'type': 'ProtocolPortObject'}]}
2025-08-04 14:37:53,070 | DEBUG | GET result for TestWebServices: success=False, message='Object 'TestWebServices' not found in FMC (will be created)'
2025-08-04 14:37:56,024 | DEBUG | POST result for TestWebServices: success=False, message='fmcapi post() failed - no ID returned.'
2025-08-04 14:37:56,024 | ERROR | Creation failed for TestWebServices: fmcapi post() failed - no ID returned.
2025-08-04 14:37:56,024 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343406_phase1_service_groups.json
2025-08-04 14:37:56,024 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:37:56,024 | INFO | No API delay settings found to optimize
2025-08-04 14:37:56,024 | DEBUG | Processing access_rules 1: TestRule1
2025-08-04 14:37:56,024 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'description', 'sourceNetworks', 'destinationNetworks', 'destinationPorts']
2025-08-04 14:37:56,024 | DEBUG | Object data: {'name': 'TestRule1', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'description': 'Test access rule', 'sourceNetworks': {'objects': [{'name': 'TestHost1', 'type': 'Host'}]}, 'destinationNetworks': {'objects': [{'name': 'TestNetwork1', 'type': 'Network'}]}, 'destinationPorts': {'objects': [{'name': 'TestHTTP', 'type': 'ProtocolPortObject'}]}}
2025-08-04 14:37:58,753 | DEBUG | GET result for TestRule1: success=False, message='Object 'TestRule1' not found in FMC (will be created)'
2025-08-04 14:38:01,453 | DEBUG | POST result for TestRule1: success=False, message='fmcapi post() failed - no ID returned. Result: False'
2025-08-04 14:38:01,454 | ERROR | Creation failed for TestRule1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:38:01,455 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343406_phase1_access_rules.json
2025-08-04 14:38:01,455 | INFO | ================================================================================
2025-08-04 14:38:01,455 | INFO | MIGRATION SUMMARY
2025-08-04 14:38:01,455 | INFO | ================================================================================
2025-08-04 14:38:01,455 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 14:38:01,455 | INFO |    • Total Objects: 11
2025-08-04 14:38:01,455 | INFO |    • Created: 5
2025-08-04 14:38:01,455 | INFO |    • Updated: 3
2025-08-04 14:38:01,455 | INFO |    • Failed: 3
2025-08-04 14:38:01,455 | INFO |    • Skipped: 0
2025-08-04 14:38:01,455 | INFO |    • Success Rate: 72.7%
2025-08-04 14:38:01,455 | INFO | ================================================================================
2025-08-04 14:38:01,456 | INFO | [FILE] Summary saved: migration_summary_migration_1754343406.json
