2025-08-04 15:14:58,764 | INFO | ================================================================================
2025-08-04 15:14:58,764 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 15:14:58,764 | INFO | Session ID: migration_1754345698
2025-08-04 15:14:58,764 | INFO | Connection Type: fmcapi
2025-08-04 15:14:58,764 | INFO | 🔍 Connection Diagnostic:
2025-08-04 15:14:58,764 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 15:14:58,764 | INFO |    • fmcapi Available: True
2025-08-04 15:14:58,764 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 15:15:01,408 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 15:15:01,409 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 15:15:01,409 | INFO | 🔍 Connection diagnostic complete
2025-08-04 15:15:01,409 | INFO | ================================================================================
2025-08-04 15:15:01,409 | INFO | 🔍 Running pre-migration validation...
2025-08-04 15:15:05,069 | INFO | ================================================================================
2025-08-04 15:15:05,070 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 15:15:05,070 | INFO | ================================================================================
2025-08-04 15:15:05,070 | INFO | [OK] Overall Status: PASSED
2025-08-04 15:15:05,070 | INFO | [OK] Configuration: 17 objects found
2025-08-04 15:15:05,070 | INFO |    • host_objects: 5
2025-08-04 15:15:05,070 | INFO |    • network_objects: 3
2025-08-04 15:15:05,070 | INFO |    • service_objects: 4
2025-08-04 15:15:05,070 | INFO |    • object_groups: 2
2025-08-04 15:15:05,070 | INFO |    • service_groups: 1
2025-08-04 15:15:05,070 | INFO |    • access_rules: 2
2025-08-04 15:15:05,070 | INFO | [OK] Connection: fmcapi - API accessible
2025-08-04 15:15:05,070 | INFO | ================================================================================
2025-08-04 15:15:05,070 | INFO | BULK_API_ENABLED: Processing 5 objects with bulk API
2025-08-04 15:15:05,070 | INFO | No API delay settings found to optimize
2025-08-04 15:15:05,070 | DEBUG | Processing hosts 1: TestHost_WebServer
2025-08-04 15:15:05,070 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:15:05,070 | DEBUG | Object data: {'name': 'TestHost_WebServer', 'type': 'Host', 'value': '*************', 'description': 'Test web server host', 'overridable': False}
2025-08-04 15:15:08,766 | DEBUG | GET result for TestHost_WebServer: success=True, message='None'
2025-08-04 15:15:12,436 | DEBUG | PUT result for TestHost_WebServer: success=True, message='None'
2025-08-04 15:15:12,436 | DEBUG | Processing hosts 2: TestHost_DatabaseServer
2025-08-04 15:15:12,436 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:15:12,436 | DEBUG | Object data: {'name': 'TestHost_DatabaseServer', 'type': 'Host', 'value': '*************', 'description': 'Test database server host', 'overridable': False}
2025-08-04 15:15:16,278 | DEBUG | GET result for TestHost_DatabaseServer: success=True, message='None'
2025-08-04 15:15:19,747 | DEBUG | PUT result for TestHost_DatabaseServer: success=True, message='None'
2025-08-04 15:15:19,747 | DEBUG | Processing hosts 3: TestHost_FileServer
2025-08-04 15:15:19,747 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:15:19,747 | DEBUG | Object data: {'name': 'TestHost_FileServer', 'type': 'Host', 'value': '*************', 'description': 'Test file server host', 'overridable': False}
2025-08-04 15:15:23,745 | DEBUG | GET result for TestHost_FileServer: success=True, message='None'
2025-08-04 15:15:27,278 | DEBUG | PUT result for TestHost_FileServer: success=True, message='None'
2025-08-04 15:15:31,119 | DEBUG | GET result for TestHost_PrintServer: success=True, message='None'
2025-08-04 15:15:34,407 | DEBUG | PUT result for TestHost_PrintServer: success=True, message='None'
2025-08-04 15:15:38,448 | DEBUG | GET result for TestHost_BackupServer: success=True, message='None'
2025-08-04 15:15:41,728 | DEBUG | PUT result for TestHost_BackupServer: success=True, message='None'
2025-08-04 15:15:41,729 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345698_phase1_hosts.json
2025-08-04 15:15:41,729 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 15:15:41,729 | INFO | No API delay settings found to optimize
2025-08-04 15:15:41,729 | DEBUG | Processing networks 1: TestNetwork_LAN
2025-08-04 15:15:41,729 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:15:41,729 | DEBUG | Object data: {'name': 'TestNetwork_LAN', 'type': 'Network', 'value': '***********/24', 'description': 'Test LAN network', 'overridable': False}
2025-08-04 15:15:46,168 | DEBUG | GET result for TestNetwork_LAN: success=True, message='None'
2025-08-04 15:15:49,677 | DEBUG | PUT result for TestNetwork_LAN: success=True, message='None'
2025-08-04 15:15:49,677 | DEBUG | Processing networks 2: TestNetwork_DMZ
2025-08-04 15:15:49,677 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:15:49,677 | DEBUG | Object data: {'name': 'TestNetwork_DMZ', 'type': 'Network', 'value': '********/24', 'description': 'Test DMZ network', 'overridable': False}
2025-08-04 15:15:54,219 | DEBUG | GET result for TestNetwork_DMZ: success=True, message='None'
2025-08-04 15:15:57,735 | DEBUG | PUT result for TestNetwork_DMZ: success=True, message='None'
2025-08-04 15:15:57,735 | DEBUG | Processing networks 3: TestNetwork_Management
2025-08-04 15:15:57,735 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:15:57,735 | DEBUG | Object data: {'name': 'TestNetwork_Management', 'type': 'Network', 'value': '**********/24', 'description': 'Test management network', 'overridable': False}
2025-08-04 15:16:02,274 | DEBUG | GET result for TestNetwork_Management: success=True, message='None'
2025-08-04 15:16:05,749 | DEBUG | PUT result for TestNetwork_Management: success=True, message='None'
2025-08-04 15:16:05,750 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345698_phase1_networks.json
2025-08-04 15:16:05,750 | INFO | BULK_API_ENABLED: Processing 4 objects with bulk API
2025-08-04 15:16:05,750 | INFO | No API delay settings found to optimize
2025-08-04 15:16:05,750 | DEBUG | Processing services 1: TestService_HTTP_8080
2025-08-04 15:16:05,751 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 15:16:05,751 | DEBUG | Object data: {'name': 'TestService_HTTP_8080', 'type': 'ProtocolPortObject', 'protocol': 'TCP', 'port': '8080', 'description': 'Test HTTP service on port 8080'}
2025-08-04 15:16:08,933 | DEBUG | GET result for TestService_HTTP_8080: success=True, message='None'
2025-08-04 15:16:08,933 | DEBUG | PUT result for TestService_HTTP_8080: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 15:16:08,933 | DEBUG | Processing services 2: TestService_HTTPS_8443
2025-08-04 15:16:08,933 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 15:16:08,933 | DEBUG | Object data: {'name': 'TestService_HTTPS_8443', 'type': 'ProtocolPortObject', 'protocol': 'TCP', 'port': '8443', 'description': 'Test HTTPS service on port 8443'}
2025-08-04 15:16:11,873 | DEBUG | GET result for TestService_HTTPS_8443: success=True, message='None'
2025-08-04 15:16:11,873 | DEBUG | PUT result for TestService_HTTPS_8443: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 15:16:11,873 | DEBUG | Processing services 3: TestService_Database
2025-08-04 15:16:11,873 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 15:16:11,873 | DEBUG | Object data: {'name': 'TestService_Database', 'type': 'ProtocolPortObject', 'protocol': 'TCP', 'port': '3306', 'description': 'Test MySQL database service'}
2025-08-04 15:16:14,862 | DEBUG | GET result for TestService_Database: success=True, message='None'
2025-08-04 15:16:14,862 | DEBUG | PUT result for TestService_Database: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 15:16:17,854 | DEBUG | GET result for TestService_FTP: success=True, message='None'
2025-08-04 15:16:17,854 | DEBUG | PUT result for TestService_FTP: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 15:16:17,855 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345698_phase1_services.json
2025-08-04 15:16:17,856 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:16:17,856 | INFO | No API delay settings found to optimize
2025-08-04 15:16:17,856 | DEBUG | Processing object_groups 1: TestGroup_Servers
2025-08-04 15:16:17,856 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 15:16:17,856 | DEBUG | Object data: {'name': 'TestGroup_Servers', 'type': 'NetworkGroup', 'description': 'Test server group', 'objects': [{'name': 'TestHost_WebServer', 'type': 'Host'}, {'name': 'TestHost_DatabaseServer', 'type': 'Host'}, {'name': 'TestHost_FileServer', 'type': 'Host'}]}
2025-08-04 15:16:21,170 | DEBUG | GET result for TestGroup_Servers: success=True, message='None'
2025-08-04 15:16:21,170 | DEBUG | PUT result for TestGroup_Servers: success=True, message='Network group exists and is correct (no update needed)'
2025-08-04 15:16:21,170 | DEBUG | Processing object_groups 2: TestGroup_Networks
2025-08-04 15:16:21,170 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 15:16:21,170 | DEBUG | Object data: {'name': 'TestGroup_Networks', 'type': 'NetworkGroup', 'description': 'Test network group', 'objects': [{'name': 'TestNetwork_LAN', 'type': 'Network'}, {'name': 'TestNetwork_DMZ', 'type': 'Network'}]}
2025-08-04 15:16:24,490 | DEBUG | GET result for TestGroup_Networks: success=True, message='None'
2025-08-04 15:16:24,490 | DEBUG | PUT result for TestGroup_Networks: success=True, message='Network group exists and is correct (no update needed)'
2025-08-04 15:16:24,491 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345698_phase1_object_groups.json
2025-08-04 15:16:24,491 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 15:16:24,491 | INFO | No API delay settings found to optimize
2025-08-04 15:16:24,491 | DEBUG | Processing service_groups 1: TestGroup_WebServices
2025-08-04 15:16:24,491 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 15:16:24,491 | DEBUG | Object data: {'name': 'TestGroup_WebServices', 'type': 'PortObjectGroup', 'description': 'Test web services group', 'objects': [{'name': 'TestService_HTTP_8080', 'type': 'ProtocolPortObject'}, {'name': 'TestService_HTTPS_8443', 'type': 'ProtocolPortObject'}]}
2025-08-04 15:16:27,485 | DEBUG | GET result for TestGroup_WebServices: success=True, message='None'
2025-08-04 15:16:27,485 | DEBUG | PUT result for TestGroup_WebServices: success=True, message='Port object group exists and is correct (no update needed)'
2025-08-04 15:16:27,486 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345698_phase1_service_groups.json
2025-08-04 15:16:30,203 | INFO | Attempting to get or create Access Control Policy...
2025-08-04 15:16:30,203 | DEBUG | Fetching existing Access Control Policies...
2025-08-04 15:16:32,027 | DEBUG | ACP object after get(): <class 'fmcapi.api_objects.policy_services.accesspolicies.AccessPolicies'>
2025-08-04 15:16:32,027 | WARNING | No existing Access Control Policies found
2025-08-04 15:16:32,027 | INFO | Creating new Access Control Policy...
2025-08-04 15:16:32,027 | DEBUG | Posting new Access Control Policy...
2025-08-04 15:16:33,024 | DEBUG | Post result: {'metadata': {'inherit': False, 'lockingStatus': {'status': 'UNLOCKED'}, 'domain': {'name': 'Global', 'id': 'e276abec-e0f2-11e3-8169-6d9ed49b625f', 'type': 'Domain'}}, 'type': 'AccessPolicy', 'links': {'self': 'https://fmcrestapisandbox.cisco.com/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/policy/accesspolicies/005056BF-7B88-0ed3-0000-017187327177'}, 'rules': {'refType': 'list', 'type': 'AccessRule', 'links': {'self': 'https://fmcrestapisandbox.cisco.com/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/policy/accesspolicies/005056BF-7B88-0ed3-0000-017187327177/accessrules'}}, 'name': 'ASA Migration ACP 1754345792', 'description': 'Created automatically for ASA to FMC migration', 'id': '005056BF-7B88-0ed3-0000-017187327177'}
2025-08-04 15:16:33,024 | INFO | Successfully created new Access Control Policy: ASA Migration ACP 1754345792 (ID: 005056BF-7B88-0ed3-0000-017187327177)
2025-08-04 15:16:33,024 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:16:33,024 | INFO | No API delay settings found to optimize
2025-08-04 15:16:33,024 | DEBUG | Processing access_rules 1: TestRule_AllowWeb
2025-08-04 15:16:33,024 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'sourceNetworks', 'destinationNetworks', 'destinationPorts', 'logBegin', 'logEnd', 'acp_id']
2025-08-04 15:16:33,024 | DEBUG | Object data: {'name': 'TestRule_AllowWeb', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'sourceNetworks': [{'name': 'TestNetwork_LAN', 'type': 'Network'}], 'destinationNetworks': [{'name': 'TestHost_WebServer', 'type': 'Host'}], 'destinationPorts': [{'name': 'TestService_HTTP_8080', 'type': 'ProtocolPortObject'}], 'logBegin': False, 'logEnd': True, 'acp_id': '005056BF-7B88-0ed3-0000-017187327177'}
2025-08-04 15:16:38,052 | DEBUG | GET result for TestRule_AllowWeb: success=False, message='No Access Control Policy found - cannot manage access rules'
2025-08-04 15:17:11,775 | DEBUG | POST result for TestRule_AllowWeb: success=False, message='Failed to create access rule: super(): no arguments'
2025-08-04 15:17:11,775 | ERROR | Creation failed for TestRule_AllowWeb: Failed to create access rule: super(): no arguments
2025-08-04 15:17:11,775 | DEBUG | Processing access_rules 2: TestRule_AllowDatabase
2025-08-04 15:17:11,775 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'sourceNetworks', 'destinationNetworks', 'destinationPorts', 'logBegin', 'logEnd', 'acp_id']
2025-08-04 15:17:11,775 | DEBUG | Object data: {'name': 'TestRule_AllowDatabase', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'sourceNetworks': [{'name': 'TestHost_WebServer', 'type': 'Host'}], 'destinationNetworks': [{'name': 'TestHost_DatabaseServer', 'type': 'Host'}], 'destinationPorts': [{'name': 'TestService_Database', 'type': 'ProtocolPortObject'}], 'logBegin': False, 'logEnd': True, 'acp_id': '005056BF-7B88-0ed3-0000-017187327177'}
2025-08-04 15:17:16,909 | DEBUG | GET result for TestRule_AllowDatabase: success=False, message='No Access Control Policy found - cannot manage access rules'
2025-08-04 15:17:50,439 | DEBUG | POST result for TestRule_AllowDatabase: success=False, message='Failed to create access rule: super(): no arguments'
2025-08-04 15:17:50,439 | ERROR | Creation failed for TestRule_AllowDatabase: Failed to create access rule: super(): no arguments
2025-08-04 15:17:50,440 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345698_phase1_access_rules.json
2025-08-04 15:17:50,440 | INFO | ================================================================================
2025-08-04 15:17:50,440 | INFO | MIGRATION SUMMARY
2025-08-04 15:17:50,440 | INFO | ================================================================================
2025-08-04 15:17:50,440 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 15:17:50,441 | INFO |    • Total Objects: 17
2025-08-04 15:17:50,441 | INFO |    • Created: 0
2025-08-04 15:17:50,441 | INFO |    • Updated: 15
2025-08-04 15:17:50,441 | INFO |    • Failed: 2
2025-08-04 15:17:50,441 | INFO |    • Skipped: 0
2025-08-04 15:17:50,441 | INFO |    • Success Rate: 88.2%
2025-08-04 15:17:50,441 | INFO | ================================================================================
2025-08-04 15:17:50,441 | INFO | [FILE] Summary saved: migration_summary_migration_1754345698.json
