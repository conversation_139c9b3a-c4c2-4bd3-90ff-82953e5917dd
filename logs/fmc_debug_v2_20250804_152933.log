2025-08-04 15:29:33,718 | INFO | ================================================================================
2025-08-04 15:29:33,718 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 15:29:33,718 | INFO | Session ID: migration_1754346573
2025-08-04 15:29:33,718 | INFO | Connection Type: fmcapi
2025-08-04 15:29:33,718 | INFO | 🔍 Connection Diagnostic:
2025-08-04 15:29:33,718 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 15:29:33,718 | INFO |    • fmcapi Available: True
2025-08-04 15:29:33,718 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 15:29:36,297 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 15:29:36,297 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 15:29:36,297 | INFO | 🔍 Connection diagnostic complete
2025-08-04 15:29:36,298 | INFO | ================================================================================
2025-08-04 15:29:36,298 | INFO | 🔍 Running pre-migration validation...
2025-08-04 15:29:40,002 | INFO | ================================================================================
2025-08-04 15:29:40,002 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 15:29:40,002 | INFO | ================================================================================
2025-08-04 15:29:40,002 | INFO | [OK] Overall Status: PASSED
2025-08-04 15:29:40,002 | INFO | [OK] Configuration: 5 objects found
2025-08-04 15:29:40,002 | INFO |    • host_objects: 2
2025-08-04 15:29:40,002 | INFO |    • network_objects: 1
2025-08-04 15:29:40,003 | INFO |    • service_objects: 1
2025-08-04 15:29:40,003 | INFO |    • access_rules: 1
2025-08-04 15:29:40,003 | INFO | [OK] Connection: fmcapi - API accessible
2025-08-04 15:29:40,003 | INFO | ================================================================================
2025-08-04 15:29:40,003 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:29:40,003 | INFO | No API delay settings found to optimize
2025-08-04 15:29:40,003 | DEBUG | Processing hosts 1: TestHost_Server1
2025-08-04 15:29:40,003 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:29:40,003 | DEBUG | Object data: {'name': 'TestHost_Server1', 'type': 'Host', 'value': '**************', 'description': 'Test server 1', 'overridable': False}
2025-08-04 15:29:43,705 | DEBUG | GET result for TestHost_Server1: success=False, message='Object 'TestHost_Server1' not found in FMC (will be created)'
2025-08-04 15:29:47,077 | DEBUG | POST result for TestHost_Server1: success=True, message='None'
2025-08-04 15:29:47,077 | DEBUG | Processing hosts 2: TestHost_Server2
2025-08-04 15:29:47,077 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:29:47,077 | DEBUG | Object data: {'name': 'TestHost_Server2', 'type': 'Host', 'value': '**************', 'description': 'Test server 2', 'overridable': False}
2025-08-04 15:29:50,951 | DEBUG | GET result for TestHost_Server2: success=False, message='Object 'TestHost_Server2' not found in FMC (will be created)'
2025-08-04 15:29:54,081 | DEBUG | POST result for TestHost_Server2: success=True, message='None'
2025-08-04 15:29:54,082 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754346573_phase1_hosts.json
2025-08-04 15:29:54,082 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 15:29:54,082 | INFO | No API delay settings found to optimize
2025-08-04 15:29:54,082 | DEBUG | Processing networks 1: TestNetwork_Subnet
2025-08-04 15:29:54,082 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:29:54,082 | DEBUG | Object data: {'name': 'TestNetwork_Subnet', 'type': 'Network', 'value': '*************/24', 'description': 'Test subnet', 'overridable': False}
2025-08-04 15:29:58,437 | DEBUG | GET result for TestNetwork_Subnet: success=False, message='Object 'TestNetwork_Subnet' not found in FMC (will be created)'
2025-08-04 15:30:01,630 | DEBUG | POST result for TestNetwork_Subnet: success=True, message='None'
2025-08-04 15:30:01,631 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754346573_phase1_networks.json
2025-08-04 15:30:01,632 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 15:30:01,632 | INFO | No API delay settings found to optimize
2025-08-04 15:30:01,632 | DEBUG | Processing services 1: TestService_HTTP
2025-08-04 15:30:01,632 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 15:30:01,632 | DEBUG | Object data: {'name': 'TestService_HTTP', 'type': 'ProtocolPortObject', 'protocol': 'TCP', 'port': '80', 'description': 'Test HTTP service'}
2025-08-04 15:30:04,612 | DEBUG | GET result for TestService_HTTP: success=False, message='Object 'TestService_HTTP' not found in FMC (will be created)'
2025-08-04 15:30:07,759 | DEBUG | POST result for TestService_HTTP: success=True, message='None'
2025-08-04 15:30:07,761 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754346573_phase1_services.json
2025-08-04 15:30:10,508 | INFO | Attempting to get or create Access Control Policy...
2025-08-04 15:30:10,509 | DEBUG | Fetching existing Access Control Policies...
2025-08-04 15:30:13,126 | DEBUG | ACP object after get(): <class 'fmcapi.api_objects.policy_services.accesspolicies.AccessPolicies'>
2025-08-04 15:30:13,126 | WARNING | No existing Access Control Policies found
2025-08-04 15:30:13,126 | INFO | Creating new Access Control Policy...
2025-08-04 15:30:13,126 | DEBUG | Posting new Access Control Policy...
2025-08-04 15:30:13,985 | DEBUG | Post result: {'metadata': {'inherit': False, 'lockingStatus': {'status': 'UNLOCKED'}, 'domain': {'name': 'Global', 'id': 'e276abec-e0f2-11e3-8169-6d9ed49b625f', 'type': 'Domain'}}, 'type': 'AccessPolicy', 'links': {'self': 'https://fmcrestapisandbox.cisco.com/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/policy/accesspolicies/005056BF-7B88-0ed3-0000-017187340206'}, 'rules': {'refType': 'list', 'type': 'AccessRule', 'links': {'self': 'https://fmcrestapisandbox.cisco.com/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/policy/accesspolicies/005056BF-7B88-0ed3-0000-017187340206/accessrules'}}, 'name': 'ASA Migration ACP 1754346613', 'description': 'Created automatically for ASA to FMC migration', 'id': '005056BF-7B88-0ed3-0000-017187340206'}
2025-08-04 15:30:13,985 | INFO | Successfully created new Access Control Policy: ASA Migration ACP 1754346613 (ID: 005056BF-7B88-0ed3-0000-017187340206)
2025-08-04 15:30:13,985 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 15:30:13,986 | INFO | No API delay settings found to optimize
2025-08-04 15:30:13,986 | DEBUG | Processing access_rules 1: TestRule_AllowHTTP
2025-08-04 15:30:13,986 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'sourceNetworks', 'destinationNetworks', 'destinationPorts', 'logBegin', 'logEnd', 'acp_id']
2025-08-04 15:30:13,986 | DEBUG | Object data: {'name': 'TestRule_AllowHTTP', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'sourceNetworks': [{'name': 'TestNetwork_Subnet', 'type': 'Network'}], 'destinationNetworks': [{'name': 'TestHost_Server1', 'type': 'Host'}], 'destinationPorts': [{'name': 'TestService_HTTP', 'type': 'ProtocolPortObject'}], 'logBegin': False, 'logEnd': True, 'acp_id': '005056BF-7B88-0ed3-0000-017187340206'}
2025-08-04 15:30:19,548 | DEBUG | GET result for TestRule_AllowHTTP: success=False, message='No Access Control Policy found - cannot manage access rules'
2025-08-04 15:30:25,717 | DEBUG | POST result for TestRule_AllowHTTP: success=False, message='Access rule creation failed - no ID returned. Result: None'
2025-08-04 15:30:25,717 | ERROR | Creation failed for TestRule_AllowHTTP: Access rule creation failed - no ID returned. Result: None
2025-08-04 15:30:25,718 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754346573_phase1_access_rules.json
2025-08-04 15:30:25,719 | INFO | ================================================================================
2025-08-04 15:30:25,719 | INFO | MIGRATION SUMMARY
2025-08-04 15:30:25,719 | INFO | ================================================================================
2025-08-04 15:30:25,719 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 15:30:25,719 | INFO |    • Total Objects: 5
2025-08-04 15:30:25,719 | INFO |    • Created: 4
2025-08-04 15:30:25,719 | INFO |    • Updated: 0
2025-08-04 15:30:25,719 | INFO |    • Failed: 1
2025-08-04 15:30:25,719 | INFO |    • Skipped: 0
2025-08-04 15:30:25,719 | INFO |    • Success Rate: 80.0%
2025-08-04 15:30:25,719 | INFO | ================================================================================
2025-08-04 15:30:25,719 | INFO | [FILE] Summary saved: migration_summary_migration_1754346573.json
