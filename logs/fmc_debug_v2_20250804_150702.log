2025-08-04 15:07:02,168 | INFO | ================================================================================
2025-08-04 15:07:02,168 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 15:07:02,168 | INFO | Session ID: migration_1754345222
2025-08-04 15:07:02,168 | INFO | Connection Type: fmcapi
2025-08-04 15:07:02,168 | INFO | 🔍 Connection Diagnostic:
2025-08-04 15:07:02,168 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 15:07:02,168 | INFO |    • fmcapi Available: True
2025-08-04 15:07:02,168 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 15:07:04,909 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 15:07:04,909 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 15:07:04,909 | INFO | 🔍 Connection diagnostic complete
2025-08-04 15:07:04,909 | INFO | ================================================================================
2025-08-04 15:07:04,909 | INFO | 🔍 Running pre-migration validation...
2025-08-04 15:07:08,804 | INFO | ================================================================================
2025-08-04 15:07:08,804 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 15:07:08,804 | INFO | ================================================================================
2025-08-04 15:07:08,804 | INFO | [OK] Overall Status: PASSED
2025-08-04 15:07:08,804 | INFO | [OK] Configuration: 17 objects found
2025-08-04 15:07:08,804 | INFO |    • host_objects: 5
2025-08-04 15:07:08,805 | INFO |    • network_objects: 3
2025-08-04 15:07:08,805 | INFO |    • service_objects: 4
2025-08-04 15:07:08,805 | INFO |    • object_groups: 2
2025-08-04 15:07:08,805 | INFO |    • service_groups: 1
2025-08-04 15:07:08,805 | INFO |    • access_rules: 2
2025-08-04 15:07:08,805 | INFO | [OK] Connection: fmcapi - API accessible
2025-08-04 15:07:08,805 | INFO | ================================================================================
2025-08-04 15:07:08,805 | INFO | BULK_API_ENABLED: Processing 5 objects with bulk API
2025-08-04 15:07:08,805 | INFO | No API delay settings found to optimize
2025-08-04 15:07:08,805 | DEBUG | Processing hosts 1: TestHost_WebServer
2025-08-04 15:07:08,805 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:07:08,806 | DEBUG | Object data: {'name': 'TestHost_WebServer', 'type': 'Host', 'value': '*************', 'description': 'Test web server host', 'overridable': False}
2025-08-04 15:07:12,675 | DEBUG | GET result for TestHost_WebServer: success=False, message='Object 'TestHost_WebServer' not found in FMC (will be created)'
2025-08-04 15:07:16,380 | DEBUG | POST result for TestHost_WebServer: success=True, message='None'
2025-08-04 15:07:16,380 | DEBUG | Processing hosts 2: TestHost_DatabaseServer
2025-08-04 15:07:16,380 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:07:16,380 | DEBUG | Object data: {'name': 'TestHost_DatabaseServer', 'type': 'Host', 'value': '*************', 'description': 'Test database server host', 'overridable': False}
2025-08-04 15:07:20,587 | DEBUG | GET result for TestHost_DatabaseServer: success=False, message='Object 'TestHost_DatabaseServer' not found in FMC (will be created)'
2025-08-04 15:07:24,054 | DEBUG | POST result for TestHost_DatabaseServer: success=True, message='None'
2025-08-04 15:07:24,054 | DEBUG | Processing hosts 3: TestHost_FileServer
2025-08-04 15:07:24,054 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:07:24,054 | DEBUG | Object data: {'name': 'TestHost_FileServer', 'type': 'Host', 'value': '*************', 'description': 'Test file server host', 'overridable': False}
2025-08-04 15:07:28,140 | DEBUG | GET result for TestHost_FileServer: success=False, message='Object 'TestHost_FileServer' not found in FMC (will be created)'
2025-08-04 15:07:31,565 | DEBUG | POST result for TestHost_FileServer: success=True, message='None'
2025-08-04 15:07:35,674 | DEBUG | GET result for TestHost_PrintServer: success=False, message='Object 'TestHost_PrintServer' not found in FMC (will be created)'
2025-08-04 15:07:39,372 | DEBUG | POST result for TestHost_PrintServer: success=True, message='None'
2025-08-04 15:07:43,493 | DEBUG | GET result for TestHost_BackupServer: success=False, message='Object 'TestHost_BackupServer' not found in FMC (will be created)'
2025-08-04 15:07:46,869 | DEBUG | POST result for TestHost_BackupServer: success=True, message='None'
2025-08-04 15:07:46,870 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345222_phase1_hosts.json
2025-08-04 15:07:46,870 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 15:07:46,871 | INFO | No API delay settings found to optimize
2025-08-04 15:07:46,871 | DEBUG | Processing networks 1: TestNetwork_LAN
2025-08-04 15:07:46,871 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:07:46,871 | DEBUG | Object data: {'name': 'TestNetwork_LAN', 'type': 'Network', 'value': '***********/24', 'description': 'Test LAN network', 'overridable': False}
2025-08-04 15:07:51,708 | DEBUG | GET result for TestNetwork_LAN: success=False, message='Object 'TestNetwork_LAN' not found in FMC (will be created)'
2025-08-04 15:07:55,188 | DEBUG | POST result for TestNetwork_LAN: success=True, message='None'
2025-08-04 15:07:55,189 | DEBUG | Processing networks 2: TestNetwork_DMZ
2025-08-04 15:07:55,189 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:07:55,189 | DEBUG | Object data: {'name': 'TestNetwork_DMZ', 'type': 'Network', 'value': '********/24', 'description': 'Test DMZ network', 'overridable': False}
2025-08-04 15:07:59,821 | DEBUG | GET result for TestNetwork_DMZ: success=False, message='Object 'TestNetwork_DMZ' not found in FMC (will be created)'
2025-08-04 15:08:03,289 | DEBUG | POST result for TestNetwork_DMZ: success=True, message='None'
2025-08-04 15:08:03,289 | DEBUG | Processing networks 3: TestNetwork_Management
2025-08-04 15:08:03,289 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:08:03,289 | DEBUG | Object data: {'name': 'TestNetwork_Management', 'type': 'Network', 'value': '**********/24', 'description': 'Test management network', 'overridable': False}
2025-08-04 15:08:08,127 | DEBUG | GET result for TestNetwork_Management: success=False, message='Object 'TestNetwork_Management' not found in FMC (will be created)'
2025-08-04 15:08:11,593 | DEBUG | POST result for TestNetwork_Management: success=True, message='None'
2025-08-04 15:08:11,594 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345222_phase1_networks.json
2025-08-04 15:08:11,594 | INFO | BULK_API_ENABLED: Processing 4 objects with bulk API
2025-08-04 15:08:11,594 | INFO | No API delay settings found to optimize
2025-08-04 15:08:11,594 | DEBUG | Processing services 1: TestService_HTTP_8080
2025-08-04 15:08:11,594 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 15:08:11,594 | DEBUG | Object data: {'name': 'TestService_HTTP_8080', 'type': 'ProtocolPortObject', 'protocol': 'TCP', 'port': '8080', 'description': 'Test HTTP service on port 8080'}
2025-08-04 15:08:14,800 | DEBUG | GET result for TestService_HTTP_8080: success=False, message='Object 'TestService_HTTP_8080' not found in FMC (will be created)'
2025-08-04 15:08:18,268 | DEBUG | POST result for TestService_HTTP_8080: success=True, message='None'
2025-08-04 15:08:18,269 | DEBUG | Processing services 2: TestService_HTTPS_8443
2025-08-04 15:08:18,269 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 15:08:18,269 | DEBUG | Object data: {'name': 'TestService_HTTPS_8443', 'type': 'ProtocolPortObject', 'protocol': 'TCP', 'port': '8443', 'description': 'Test HTTPS service on port 8443'}
2025-08-04 15:08:21,561 | DEBUG | GET result for TestService_HTTPS_8443: success=False, message='Object 'TestService_HTTPS_8443' not found in FMC (will be created)'
2025-08-04 15:08:24,982 | DEBUG | POST result for TestService_HTTPS_8443: success=True, message='None'
2025-08-04 15:08:24,982 | DEBUG | Processing services 3: TestService_Database
2025-08-04 15:08:24,982 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 15:08:24,982 | DEBUG | Object data: {'name': 'TestService_Database', 'type': 'ProtocolPortObject', 'protocol': 'TCP', 'port': '3306', 'description': 'Test MySQL database service'}
2025-08-04 15:08:28,092 | DEBUG | GET result for TestService_Database: success=False, message='Object 'TestService_Database' not found in FMC (will be created)'
2025-08-04 15:08:31,499 | DEBUG | POST result for TestService_Database: success=True, message='None'
2025-08-04 15:08:34,788 | DEBUG | GET result for TestService_FTP: success=False, message='Object 'TestService_FTP' not found in FMC (will be created)'
2025-08-04 15:08:38,417 | DEBUG | POST result for TestService_FTP: success=True, message='None'
2025-08-04 15:08:38,419 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345222_phase1_services.json
2025-08-04 15:08:38,419 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:08:38,419 | INFO | No API delay settings found to optimize
2025-08-04 15:08:38,419 | DEBUG | Processing object_groups 1: TestGroup_Servers
2025-08-04 15:08:38,419 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 15:08:38,419 | DEBUG | Object data: {'name': 'TestGroup_Servers', 'type': 'NetworkGroup', 'description': 'Test server group', 'objects': [{'name': 'TestHost_WebServer', 'type': 'Host'}, {'name': 'TestHost_DatabaseServer', 'type': 'Host'}, {'name': 'TestHost_FileServer', 'type': 'Host'}]}
2025-08-04 15:08:42,251 | DEBUG | GET result for TestGroup_Servers: success=False, message='Object 'TestGroup_Servers' not found in FMC (will be created)'
2025-08-04 15:08:57,591 | DEBUG | POST result for TestGroup_Servers: success=True, message='None'
2025-08-04 15:08:57,591 | DEBUG | Processing object_groups 2: TestGroup_Networks
2025-08-04 15:08:57,591 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 15:08:57,591 | DEBUG | Object data: {'name': 'TestGroup_Networks', 'type': 'NetworkGroup', 'description': 'Test network group', 'objects': [{'name': 'TestNetwork_LAN', 'type': 'Network'}, {'name': 'TestNetwork_DMZ', 'type': 'Network'}]}
2025-08-04 15:09:01,132 | DEBUG | GET result for TestGroup_Networks: success=False, message='Object 'TestGroup_Networks' not found in FMC (will be created)'
2025-08-04 15:09:13,457 | DEBUG | POST result for TestGroup_Networks: success=True, message='None'
2025-08-04 15:09:13,458 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345222_phase1_object_groups.json
2025-08-04 15:09:13,458 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 15:09:13,458 | INFO | No API delay settings found to optimize
2025-08-04 15:09:13,458 | DEBUG | Processing service_groups 1: TestGroup_WebServices
2025-08-04 15:09:13,458 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 15:09:13,458 | DEBUG | Object data: {'name': 'TestGroup_WebServices', 'type': 'PortObjectGroup', 'description': 'Test web services group', 'objects': [{'name': 'TestService_HTTP_8080', 'type': 'ProtocolPortObject'}, {'name': 'TestService_HTTPS_8443', 'type': 'ProtocolPortObject'}]}
2025-08-04 15:09:16,475 | DEBUG | GET result for TestGroup_WebServices: success=False, message='Object 'TestGroup_WebServices' not found in FMC (will be created)'
2025-08-04 15:09:25,660 | DEBUG | POST result for TestGroup_WebServices: success=True, message='None'
2025-08-04 15:09:25,661 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345222_phase1_service_groups.json
2025-08-04 15:09:28,393 | INFO | Attempting to get or create Access Control Policy...
2025-08-04 15:09:28,393 | DEBUG | Fetching existing Access Control Policies...
2025-08-04 15:09:30,082 | DEBUG | ACP object after get(): <class 'fmcapi.api_objects.policy_services.accesspolicies.AccessPolicies'>
2025-08-04 15:09:30,082 | WARNING | No existing Access Control Policies found
2025-08-04 15:09:30,082 | INFO | Creating new Access Control Policy...
2025-08-04 15:09:30,082 | DEBUG | Posting new Access Control Policy...
2025-08-04 15:09:30,983 | DEBUG | Post result: {'metadata': {'inherit': False, 'lockingStatus': {'status': 'UNLOCKED'}, 'domain': {'name': 'Global', 'id': 'e276abec-e0f2-11e3-8169-6d9ed49b625f', 'type': 'Domain'}}, 'type': 'AccessPolicy', 'links': {'self': 'https://fmcrestapisandbox.cisco.com/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/policy/accesspolicies/005056BF-7B88-0ed3-0000-017187313775'}, 'rules': {'refType': 'list', 'type': 'AccessRule', 'links': {'self': 'https://fmcrestapisandbox.cisco.com/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/policy/accesspolicies/005056BF-7B88-0ed3-0000-017187313775/accessrules'}}, 'name': 'ASA Migration Access Control Policy', 'description': 'Created automatically for ASA to FMC migration', 'id': '005056BF-7B88-0ed3-0000-017187313775'}
2025-08-04 15:09:30,983 | INFO | Successfully created new Access Control Policy: ASA Migration Access Control Policy (ID: 005056BF-7B88-0ed3-0000-017187313775)
2025-08-04 15:09:30,983 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:09:30,983 | INFO | No API delay settings found to optimize
2025-08-04 15:09:30,983 | DEBUG | Processing access_rules 1: TestRule_AllowWeb
2025-08-04 15:09:30,983 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'sourceNetworks', 'destinationNetworks', 'destinationPorts', 'logBegin', 'logEnd', 'acp_id']
2025-08-04 15:09:30,983 | DEBUG | Object data: {'name': 'TestRule_AllowWeb', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'sourceNetworks': [{'name': 'TestNetwork_LAN', 'type': 'Network'}], 'destinationNetworks': [{'name': 'TestHost_WebServer', 'type': 'Host'}], 'destinationPorts': [{'name': 'TestService_HTTP_8080', 'type': 'ProtocolPortObject'}], 'logBegin': False, 'logEnd': True, 'acp_id': '005056BF-7B88-0ed3-0000-017187313775'}
2025-08-04 15:09:35,679 | DEBUG | GET result for TestRule_AllowWeb: success=False, message='No Access Control Policy found - cannot manage access rules'
2025-08-04 15:10:47,866 | DEBUG | POST result for TestRule_AllowWeb: success=False, message='Failed to get or create Access Control Policy after multiple attempts'
2025-08-04 15:10:47,866 | ERROR | Creation failed for TestRule_AllowWeb: Failed to get or create Access Control Policy after multiple attempts
2025-08-04 15:10:47,866 | DEBUG | Processing access_rules 2: TestRule_AllowDatabase
2025-08-04 15:10:47,866 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'sourceNetworks', 'destinationNetworks', 'destinationPorts', 'logBegin', 'logEnd', 'acp_id']
2025-08-04 15:10:47,866 | DEBUG | Object data: {'name': 'TestRule_AllowDatabase', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'sourceNetworks': [{'name': 'TestHost_WebServer', 'type': 'Host'}], 'destinationNetworks': [{'name': 'TestHost_DatabaseServer', 'type': 'Host'}], 'destinationPorts': [{'name': 'TestService_Database', 'type': 'ProtocolPortObject'}], 'logBegin': False, 'logEnd': True, 'acp_id': '005056BF-7B88-0ed3-0000-017187313775'}
2025-08-04 15:10:52,648 | DEBUG | GET result for TestRule_AllowDatabase: success=False, message='No Access Control Policy found - cannot manage access rules'
2025-08-04 15:12:03,349 | DEBUG | POST result for TestRule_AllowDatabase: success=False, message='Failed to get or create Access Control Policy after multiple attempts'
2025-08-04 15:12:03,349 | ERROR | Creation failed for TestRule_AllowDatabase: Failed to get or create Access Control Policy after multiple attempts
2025-08-04 15:12:03,350 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345222_phase1_access_rules.json
2025-08-04 15:12:03,350 | INFO | ================================================================================
2025-08-04 15:12:03,351 | INFO | MIGRATION SUMMARY
2025-08-04 15:12:03,351 | INFO | ================================================================================
2025-08-04 15:12:03,351 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 15:12:03,351 | INFO |    • Total Objects: 17
2025-08-04 15:12:03,351 | INFO |    • Created: 15
2025-08-04 15:12:03,351 | INFO |    • Updated: 0
2025-08-04 15:12:03,351 | INFO |    • Failed: 2
2025-08-04 15:12:03,351 | INFO |    • Skipped: 0
2025-08-04 15:12:03,351 | INFO |    • Success Rate: 88.2%
2025-08-04 15:12:03,351 | INFO | ================================================================================
2025-08-04 15:12:03,352 | INFO | [FILE] Summary saved: migration_summary_migration_1754345222.json
