2025-08-04 14:36:46,169 | INFO | ================================================================================
2025-08-04 14:36:46,170 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 14:36:46,170 | INFO | Session ID: migration_1754343406
2025-08-04 14:36:46,170 | INFO | Connection Type: fmcapi
2025-08-04 14:36:46,170 | INFO | 🔍 Connection Diagnostic:
2025-08-04 14:36:46,170 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 14:36:46,170 | INFO |    • fmcapi Available: True
2025-08-04 14:36:46,170 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 14:36:48,799 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 14:36:48,799 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 14:36:48,799 | INFO | 🔍 Connection diagnostic complete
2025-08-04 14:36:48,799 | INFO | ================================================================================
2025-08-04 14:36:48,799 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:36:48,799 | INFO | No API delay settings found to optimize
2025-08-04 14:37:10,270 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343406_phase1_hosts.json
2025-08-04 14:37:10,270 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 14:37:10,271 | INFO | No API delay settings found to optimize
2025-08-04 14:37:25,523 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343406_phase1_networks.json
2025-08-04 14:37:25,523 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:37:25,523 | INFO | No API delay settings found to optimize
2025-08-04 14:37:44,071 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343406_phase1_services.json
2025-08-04 14:37:44,071 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:37:44,071 | INFO | No API delay settings found to optimize
2025-08-04 14:37:50,049 | ERROR | Creation failed for TestNetworkGroup: fmcapi post() failed - no ID returned.
2025-08-04 14:37:50,050 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343406_phase1_object_groups.json
2025-08-04 14:37:50,050 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:37:50,050 | INFO | No API delay settings found to optimize
2025-08-04 14:37:56,024 | ERROR | Creation failed for TestWebServices: fmcapi post() failed - no ID returned.
2025-08-04 14:37:56,024 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343406_phase1_service_groups.json
2025-08-04 14:37:56,024 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:37:56,024 | INFO | No API delay settings found to optimize
2025-08-04 14:38:01,454 | ERROR | Creation failed for TestRule1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:38:01,455 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343406_phase1_access_rules.json
2025-08-04 14:38:01,455 | INFO | ================================================================================
2025-08-04 14:38:01,455 | INFO | MIGRATION SUMMARY
2025-08-04 14:38:01,455 | INFO | ================================================================================
2025-08-04 14:38:01,455 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 14:38:01,455 | INFO |    • Total Objects: 11
2025-08-04 14:38:01,455 | INFO |    • Created: 5
2025-08-04 14:38:01,455 | INFO |    • Updated: 3
2025-08-04 14:38:01,455 | INFO |    • Failed: 3
2025-08-04 14:38:01,455 | INFO |    • Skipped: 0
2025-08-04 14:38:01,455 | INFO |    • Success Rate: 72.7%
2025-08-04 14:38:01,455 | INFO | ================================================================================
2025-08-04 14:38:01,456 | INFO | [FILE] Summary saved: migration_summary_migration_1754343406.json
