2025-08-04 16:26:08,696 | INFO | ================================================================================
2025-08-04 16:26:08,696 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 16:26:08,696 | INFO | Session ID: migration_1754349968
2025-08-04 16:26:08,696 | INFO | Connection Type: fmcapi
2025-08-04 16:26:08,696 | INFO | 🔍 Connection Diagnostic:
2025-08-04 16:26:08,696 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 16:26:08,696 | INFO |    • fmcapi Available: True
2025-08-04 16:26:08,696 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 16:26:11,498 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 16:26:11,498 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 16:26:11,498 | INFO | 🔍 Connection diagnostic complete
2025-08-04 16:26:11,498 | INFO | ================================================================================
2025-08-04 16:26:11,498 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 16:26:11,498 | INFO | No API delay settings found to optimize
2025-08-04 16:26:19,184 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754349968_phase1_hosts.json
2025-08-04 16:26:19,184 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 16:26:19,184 | INFO | No API delay settings found to optimize
2025-08-04 16:26:39,684 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754349968_phase1_services.json
