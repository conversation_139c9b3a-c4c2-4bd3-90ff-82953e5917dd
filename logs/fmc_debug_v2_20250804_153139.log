2025-08-04 15:31:39,648 | INFO | ================================================================================
2025-08-04 15:31:39,648 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 15:31:39,648 | INFO | Session ID: migration_1754346699
2025-08-04 15:31:39,648 | INFO | Connection Type: fmcapi
2025-08-04 15:31:39,648 | INFO | 🔍 Connection Diagnostic:
2025-08-04 15:31:39,648 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 15:31:39,648 | INFO |    • fmcapi Available: True
2025-08-04 15:31:39,648 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 15:31:42,193 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 15:31:42,193 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 15:31:42,193 | INFO | 🔍 Connection diagnostic complete
2025-08-04 15:31:42,193 | INFO | ================================================================================
2025-08-04 15:31:42,193 | INFO | 🔍 Running pre-migration validation...
2025-08-04 15:31:45,927 | INFO | ================================================================================
2025-08-04 15:31:45,927 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 15:31:45,927 | INFO | ================================================================================
2025-08-04 15:31:45,927 | INFO | [OK] Overall Status: PASSED
2025-08-04 15:31:45,927 | INFO | [OK] Configuration: 5 objects found
2025-08-04 15:31:45,927 | INFO |    • host_objects: 2
2025-08-04 15:31:45,927 | INFO |    • network_objects: 1
2025-08-04 15:31:45,927 | INFO |    • service_objects: 1
2025-08-04 15:31:45,927 | INFO |    • access_rules: 1
2025-08-04 15:31:45,927 | INFO | [OK] Connection: fmcapi - API accessible
2025-08-04 15:31:45,927 | INFO | ================================================================================
2025-08-04 15:31:45,927 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:31:45,927 | INFO | No API delay settings found to optimize
2025-08-04 15:31:45,927 | DEBUG | Processing hosts 1: TestHost_Server1
2025-08-04 15:31:45,927 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:31:45,927 | DEBUG | Object data: {'name': 'TestHost_Server1', 'type': 'Host', 'value': '**************', 'description': 'Test server 1', 'overridable': False}
2025-08-04 15:31:49,633 | DEBUG | GET result for TestHost_Server1: success=True, message='None'
2025-08-04 15:31:53,028 | DEBUG | PUT result for TestHost_Server1: success=True, message='None'
2025-08-04 15:31:53,028 | DEBUG | Processing hosts 2: TestHost_Server2
2025-08-04 15:31:53,028 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:31:53,028 | DEBUG | Object data: {'name': 'TestHost_Server2', 'type': 'Host', 'value': '**************', 'description': 'Test server 2', 'overridable': False}
2025-08-04 15:31:56,999 | DEBUG | GET result for TestHost_Server2: success=True, message='None'
2025-08-04 15:32:00,232 | DEBUG | PUT result for TestHost_Server2: success=True, message='None'
2025-08-04 15:32:00,233 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754346699_phase1_hosts.json
2025-08-04 15:32:00,233 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 15:32:00,233 | INFO | No API delay settings found to optimize
2025-08-04 15:32:00,233 | DEBUG | Processing networks 1: TestNetwork_Subnet
2025-08-04 15:32:00,233 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:32:00,233 | DEBUG | Object data: {'name': 'TestNetwork_Subnet', 'type': 'Network', 'value': '*************/24', 'description': 'Test subnet', 'overridable': False}
2025-08-04 15:32:04,708 | DEBUG | GET result for TestNetwork_Subnet: success=True, message='None'
2025-08-04 15:32:08,024 | DEBUG | PUT result for TestNetwork_Subnet: success=True, message='None'
2025-08-04 15:32:08,024 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754346699_phase1_networks.json
2025-08-04 15:32:08,024 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 15:32:08,024 | INFO | No API delay settings found to optimize
2025-08-04 15:32:08,024 | DEBUG | Processing services 1: TestService_HTTP
2025-08-04 15:32:08,024 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 15:32:08,024 | DEBUG | Object data: {'name': 'TestService_HTTP', 'type': 'ProtocolPortObject', 'protocol': 'TCP', 'port': '80', 'description': 'Test HTTP service'}
2025-08-04 15:32:11,023 | DEBUG | GET result for TestService_HTTP: success=True, message='None'
2025-08-04 15:32:11,023 | DEBUG | PUT result for TestService_HTTP: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 15:32:11,024 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754346699_phase1_services.json
2025-08-04 15:32:13,565 | INFO | Attempting to get or create Access Control Policy...
2025-08-04 15:32:13,565 | DEBUG | Fetching existing Access Control Policies...
2025-08-04 15:32:16,279 | DEBUG | ACP object after get(): <class 'fmcapi.api_objects.policy_services.accesspolicies.AccessPolicies'>
2025-08-04 15:32:16,280 | WARNING | No existing Access Control Policies found
2025-08-04 15:32:16,280 | INFO | Creating new Access Control Policy...
2025-08-04 15:32:16,280 | DEBUG | Posting new Access Control Policy...
2025-08-04 15:32:17,148 | DEBUG | Post result: {'metadata': {'inherit': False, 'lockingStatus': {'status': 'UNLOCKED'}, 'domain': {'name': 'Global', 'id': 'e276abec-e0f2-11e3-8169-6d9ed49b625f', 'type': 'Domain'}}, 'type': 'AccessPolicy', 'links': {'self': 'https://fmcrestapisandbox.cisco.com/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/policy/accesspolicies/005056BF-7B88-0ed3-0000-017187342806'}, 'rules': {'refType': 'list', 'type': 'AccessRule', 'links': {'self': 'https://fmcrestapisandbox.cisco.com/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/policy/accesspolicies/005056BF-7B88-0ed3-0000-017187342806/accessrules'}}, 'name': 'ASA Migration ACP 1754346736', 'description': 'Created automatically for ASA to FMC migration', 'id': '005056BF-7B88-0ed3-0000-017187342806'}
2025-08-04 15:32:17,148 | INFO | Successfully created new Access Control Policy: ASA Migration ACP 1754346736 (ID: 005056BF-7B88-0ed3-0000-017187342806)
2025-08-04 15:32:17,148 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 15:32:17,148 | INFO | No API delay settings found to optimize
2025-08-04 15:32:17,148 | DEBUG | Processing access_rules 1: TestRule_AllowHTTP
2025-08-04 15:32:17,149 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'sourceNetworks', 'destinationNetworks', 'destinationPorts', 'logBegin', 'logEnd', 'acp_id']
2025-08-04 15:32:17,149 | DEBUG | Object data: {'name': 'TestRule_AllowHTTP', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'sourceNetworks': [{'name': 'TestNetwork_Subnet', 'type': 'Network'}], 'destinationNetworks': [{'name': 'TestHost_Server1', 'type': 'Host'}], 'destinationPorts': [{'name': 'TestService_HTTP', 'type': 'ProtocolPortObject'}], 'logBegin': False, 'logEnd': True, 'acp_id': '005056BF-7B88-0ed3-0000-017187342806'}
2025-08-04 15:32:22,960 | DEBUG | GET result for TestRule_AllowHTTP: success=False, message='No Access Control Policy found - cannot manage access rules'
2025-08-04 15:32:29,461 | DEBUG | POST result for TestRule_AllowHTTP: success=False, message='Access rule creation failed - no ID returned. Result: None'
2025-08-04 15:32:29,461 | ERROR | Creation failed for TestRule_AllowHTTP: Access rule creation failed - no ID returned. Result: None
2025-08-04 15:32:29,461 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754346699_phase1_access_rules.json
2025-08-04 15:32:29,461 | INFO | ================================================================================
2025-08-04 15:32:29,461 | INFO | MIGRATION SUMMARY
2025-08-04 15:32:29,461 | INFO | ================================================================================
2025-08-04 15:32:29,461 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 15:32:29,461 | INFO |    • Total Objects: 5
2025-08-04 15:32:29,461 | INFO |    • Created: 0
2025-08-04 15:32:29,461 | INFO |    • Updated: 4
2025-08-04 15:32:29,461 | INFO |    • Failed: 1
2025-08-04 15:32:29,461 | INFO |    • Skipped: 0
2025-08-04 15:32:29,461 | INFO |    • Success Rate: 80.0%
2025-08-04 15:32:29,461 | INFO | ================================================================================
2025-08-04 15:32:29,461 | INFO | [FILE] Summary saved: migration_summary_migration_1754346699.json
