2025-08-04 14:58:42,272 | INFO | ================================================================================
2025-08-04 14:58:42,272 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 14:58:42,272 | INFO | Session ID: migration_1754344722
2025-08-04 14:58:42,272 | INFO | Connection Type: fmcapi
2025-08-04 14:58:42,272 | INFO | 🔍 Connection Diagnostic:
2025-08-04 14:58:42,272 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 14:58:42,272 | INFO |    • fmcapi Available: True
2025-08-04 14:58:42,272 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 14:58:45,037 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 14:58:45,037 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 14:58:45,037 | INFO | 🔍 Connection diagnostic complete
2025-08-04 14:58:45,037 | INFO | ================================================================================
2025-08-04 14:58:45,038 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:58:45,038 | INFO | No API delay settings found to optimize
2025-08-04 14:58:45,038 | DEBUG | Processing hosts 1: TestHost1
2025-08-04 14:58:45,038 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:58:45,038 | DEBUG | Object data: {'name': 'TestHost1', 'type': 'Host', 'value': '************', 'description': 'Test host object 1', 'overridable': False}
2025-08-04 14:58:48,979 | DEBUG | GET result for TestHost1: success=True, message='None'
2025-08-04 14:58:53,122 | DEBUG | PUT result for TestHost1: success=True, message='None'
2025-08-04 14:58:53,122 | DEBUG | Processing hosts 2: TestHost2
2025-08-04 14:58:53,123 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:58:53,123 | DEBUG | Object data: {'name': 'TestHost2', 'type': 'Host', 'value': '************', 'description': 'Test host object 2', 'overridable': False}
2025-08-04 14:58:57,336 | DEBUG | GET result for TestHost2: success=True, message='None'
2025-08-04 14:59:00,801 | DEBUG | PUT result for TestHost2: success=True, message='None'
2025-08-04 14:59:00,801 | DEBUG | Processing hosts 3: TestHost3
2025-08-04 14:59:00,801 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:59:00,801 | DEBUG | Object data: {'name': 'TestHost3', 'type': 'Host', 'value': '**********', 'description': 'Test host object 3', 'overridable': False}
2025-08-04 14:59:04,851 | DEBUG | GET result for TestHost3: success=True, message='None'
2025-08-04 14:59:08,326 | DEBUG | PUT result for TestHost3: success=True, message='None'
2025-08-04 14:59:08,326 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344722_phase1_hosts.json
2025-08-04 14:59:08,326 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 14:59:08,326 | INFO | No API delay settings found to optimize
2025-08-04 14:59:08,326 | DEBUG | Processing networks 1: TestNetwork1
2025-08-04 14:59:08,326 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:59:08,326 | DEBUG | Object data: {'name': 'TestNetwork1', 'type': 'Network', 'value': '*************/24', 'description': 'Test network object 1', 'overridable': False}
2025-08-04 14:59:13,269 | DEBUG | GET result for TestNetwork1: success=True, message='None'
2025-08-04 14:59:17,045 | DEBUG | PUT result for TestNetwork1: success=True, message='None'
2025-08-04 14:59:17,046 | DEBUG | Processing networks 2: TestNetwork2
2025-08-04 14:59:17,046 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:59:17,046 | DEBUG | Object data: {'name': 'TestNetwork2', 'type': 'Network', 'value': '*********/16', 'description': 'Test network object 2', 'overridable': False}
2025-08-04 14:59:21,983 | DEBUG | GET result for TestNetwork2: success=True, message='None'
2025-08-04 14:59:25,477 | DEBUG | PUT result for TestNetwork2: success=True, message='None'
2025-08-04 14:59:25,478 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344722_phase1_networks.json
2025-08-04 14:59:25,478 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:59:25,478 | INFO | No API delay settings found to optimize
2025-08-04 14:59:25,478 | DEBUG | Processing services 1: TestHTTP
2025-08-04 14:59:25,478 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:59:25,478 | DEBUG | Object data: {'name': 'TestHTTP', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '80', 'description': 'Test HTTP service'}
2025-08-04 14:59:28,556 | DEBUG | GET result for TestHTTP: success=True, message='None'
2025-08-04 14:59:28,556 | DEBUG | PUT result for TestHTTP: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 14:59:28,556 | DEBUG | Processing services 2: TestHTTPS
2025-08-04 14:59:28,556 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:59:28,556 | DEBUG | Object data: {'name': 'TestHTTPS', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '443', 'description': 'Test HTTPS service'}
2025-08-04 14:59:31,613 | DEBUG | GET result for TestHTTPS: success=True, message='None'
2025-08-04 14:59:31,613 | DEBUG | PUT result for TestHTTPS: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 14:59:31,613 | DEBUG | Processing services 3: TestSSH
2025-08-04 14:59:31,613 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:59:31,613 | DEBUG | Object data: {'name': 'TestSSH', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '22', 'description': 'Test SSH service'}
2025-08-04 14:59:34,804 | DEBUG | GET result for TestSSH: success=True, message='None'
2025-08-04 14:59:34,804 | DEBUG | PUT result for TestSSH: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 14:59:34,805 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344722_phase1_services.json
2025-08-04 14:59:34,805 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:59:34,805 | INFO | No API delay settings found to optimize
2025-08-04 14:59:34,805 | DEBUG | Processing object_groups 1: TestNetworkGroup
2025-08-04 14:59:34,805 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 14:59:34,805 | DEBUG | Object data: {'name': 'TestNetworkGroup', 'type': 'NetworkGroup', 'description': 'Test network group', 'objects': [{'name': 'TestNetwork1', 'type': 'NetworkObject'}, {'name': 'TestHost1', 'type': 'HostObject'}]}
2025-08-04 14:59:38,350 | DEBUG | GET result for TestNetworkGroup: success=True, message='None'
2025-08-04 14:59:38,350 | DEBUG | PUT result for TestNetworkGroup: success=True, message='Network group exists and is correct (no update needed)'
2025-08-04 14:59:38,351 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344722_phase1_object_groups.json
2025-08-04 14:59:38,351 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:59:38,351 | INFO | No API delay settings found to optimize
2025-08-04 14:59:38,351 | DEBUG | Processing service_groups 1: TestWebServices
2025-08-04 14:59:38,351 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 14:59:38,351 | DEBUG | Object data: {'name': 'TestWebServices', 'type': 'PortObjectGroup', 'description': 'Test web services group', 'objects': [{'name': 'TestHTTP', 'type': 'ProtocolPortObject'}, {'name': 'TestHTTPS', 'type': 'ProtocolPortObject'}]}
2025-08-04 14:59:41,368 | DEBUG | GET result for TestWebServices: success=True, message='None'
2025-08-04 14:59:41,369 | DEBUG | PUT result for TestWebServices: success=True, message='Port object group exists and is correct (no update needed)'
2025-08-04 14:59:41,369 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344722_phase1_service_groups.json
2025-08-04 14:59:46,795 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:59:46,795 | INFO | No API delay settings found to optimize
2025-08-04 14:59:46,795 | DEBUG | Processing access_rules 1: TestRule1
2025-08-04 14:59:46,795 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'description', 'sourceNetworks', 'destinationNetworks', 'destinationPorts', 'acp_id']
2025-08-04 14:59:46,795 | DEBUG | Object data: {'name': 'TestRule1', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'description': 'Test access rule', 'sourceNetworks': {'objects': [{'name': 'TestHost1', 'type': 'Host'}]}, 'destinationNetworks': {'objects': [{'name': 'TestNetwork1', 'type': 'Network'}]}, 'destinationPorts': {'objects': [{'name': 'TestHTTP', 'type': 'ProtocolPortObject'}]}, 'acp_id': '005056BF-7B88-0ed3-0000-017187312297'}
2025-08-04 14:59:51,869 | DEBUG | GET result for TestRule1: success=False, message='No Access Control Policy found - cannot manage access rules'
2025-08-04 14:59:56,668 | DEBUG | POST result for TestRule1: success=False, message='No Access Control Policy found - cannot create access rules'
2025-08-04 14:59:56,668 | ERROR | Creation failed for TestRule1: No Access Control Policy found - cannot create access rules
2025-08-04 14:59:56,668 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344722_phase1_access_rules.json
2025-08-04 14:59:56,668 | INFO | ================================================================================
2025-08-04 14:59:56,669 | INFO | MIGRATION SUMMARY
2025-08-04 14:59:56,669 | INFO | ================================================================================
2025-08-04 14:59:56,669 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 14:59:56,669 | INFO |    • Total Objects: 11
2025-08-04 14:59:56,669 | INFO |    • Created: 0
2025-08-04 14:59:56,669 | INFO |    • Updated: 10
2025-08-04 14:59:56,669 | INFO |    • Failed: 1
2025-08-04 14:59:56,669 | INFO |    • Skipped: 0
2025-08-04 14:59:56,669 | INFO |    • Success Rate: 90.9%
2025-08-04 14:59:56,669 | INFO | ================================================================================
2025-08-04 14:59:56,669 | INFO | [FILE] Summary saved: migration_summary_migration_1754344722.json
