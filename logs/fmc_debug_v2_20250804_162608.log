2025-08-04 16:26:08,696 | INFO | ================================================================================
2025-08-04 16:26:08,696 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 16:26:08,696 | INFO | Session ID: migration_1754349968
2025-08-04 16:26:08,696 | INFO | Connection Type: fmcapi
2025-08-04 16:26:08,696 | INFO | 🔍 Connection Diagnostic:
2025-08-04 16:26:08,696 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 16:26:08,696 | INFO |    • fmcapi Available: True
2025-08-04 16:26:08,696 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 16:26:11,498 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 16:26:11,498 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 16:26:11,498 | INFO | 🔍 Connection diagnostic complete
2025-08-04 16:26:11,498 | INFO | ================================================================================
2025-08-04 16:26:11,498 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 16:26:11,498 | INFO | No API delay settings found to optimize
2025-08-04 16:26:11,498 | DEBUG | Processing hosts 1: NexTalkSec
2025-08-04 16:26:11,498 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 16:26:11,498 | DEBUG | Object data: {'name': 'NexTalkSec', 'type': 'Host', 'value': '**************', 'description': 'Migrated from ASA - Test fix for duplicate handling', 'overridable': False}
2025-08-04 16:26:15,426 | DEBUG | GET result for NexTalkSec: success=True, message='None'
2025-08-04 16:26:19,180 | DEBUG | PUT result for NexTalkSec: success=True, message='None'
2025-08-04 16:26:19,184 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754349968_phase1_hosts.json
2025-08-04 16:26:19,184 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 16:26:19,184 | INFO | No API delay settings found to optimize
2025-08-04 16:26:19,184 | DEBUG | Processing services 1: obj-tcp-eq-80
2025-08-04 16:26:19,184 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 16:26:19,184 | DEBUG | Object data: {'name': 'obj-tcp-eq-80', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': 'www', 'description': 'Migrated from ASA - Test fix for named port'}
2025-08-04 16:26:22,650 | DEBUG | GET result for obj-tcp-eq-80: success=False, message='Object 'obj-tcp-eq-80' not found in FMC (will be created)'
2025-08-04 16:26:26,138 | DEBUG | POST result for obj-tcp-eq-80: success=True, message='None'
2025-08-04 16:26:26,139 | DEBUG | Processing services 2: obj-tcp-eq-23
2025-08-04 16:26:26,139 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 16:26:26,139 | DEBUG | Object data: {'name': 'obj-tcp-eq-23', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': 'telnet', 'description': 'Migrated from ASA - Test fix for named port'}
2025-08-04 16:26:29,516 | DEBUG | GET result for obj-tcp-eq-23: success=False, message='Object 'obj-tcp-eq-23' not found in FMC (will be created)'
2025-08-04 16:26:33,013 | DEBUG | POST result for obj-tcp-eq-23: success=True, message='None'
2025-08-04 16:26:33,013 | DEBUG | Processing services 3: obj-tcp-eq-5631
2025-08-04 16:26:33,013 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 16:26:33,013 | DEBUG | Object data: {'name': 'obj-tcp-eq-5631', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': 'pcanywhere-data', 'description': 'Migrated from ASA - Test fix for named port'}
2025-08-04 16:26:36,175 | DEBUG | GET result for obj-tcp-eq-5631: success=False, message='Object 'obj-tcp-eq-5631' not found in FMC (will be created)'
2025-08-04 16:26:39,682 | DEBUG | POST result for obj-tcp-eq-5631: success=True, message='None'
2025-08-04 16:26:39,684 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754349968_phase1_services.json
