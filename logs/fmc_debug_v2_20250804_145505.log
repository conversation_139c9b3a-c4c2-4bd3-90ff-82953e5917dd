2025-08-04 14:55:05,319 | INFO | ================================================================================
2025-08-04 14:55:05,319 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 14:55:05,319 | INFO | Session ID: migration_1754344505
2025-08-04 14:55:05,319 | INFO | Connection Type: fmcapi
2025-08-04 14:55:05,319 | INFO | 🔍 Connection Diagnostic:
2025-08-04 14:55:05,320 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 14:55:05,320 | INFO |    • fmcapi Available: True
2025-08-04 14:55:05,320 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 14:55:08,098 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 14:55:08,098 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 14:55:08,098 | INFO | 🔍 Connection diagnostic complete
2025-08-04 14:55:08,098 | INFO | ================================================================================
2025-08-04 14:55:08,099 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:55:08,099 | INFO | No API delay settings found to optimize
2025-08-04 14:55:08,099 | DEBUG | Processing hosts 1: TestHost1
2025-08-04 14:55:08,099 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:55:08,100 | DEBUG | Object data: {'name': 'TestHost1', 'type': 'Host', 'value': '************', 'description': 'Test host object 1', 'overridable': False}
2025-08-04 14:55:12,027 | DEBUG | GET result for TestHost1: success=True, message='None'
2025-08-04 14:55:16,021 | DEBUG | PUT result for TestHost1: success=True, message='None'
2025-08-04 14:55:16,021 | DEBUG | Processing hosts 2: TestHost2
2025-08-04 14:55:16,021 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:55:16,021 | DEBUG | Object data: {'name': 'TestHost2', 'type': 'Host', 'value': '************', 'description': 'Test host object 2', 'overridable': False}
2025-08-04 14:55:20,196 | DEBUG | GET result for TestHost2: success=True, message='None'
2025-08-04 14:55:23,695 | DEBUG | PUT result for TestHost2: success=True, message='None'
2025-08-04 14:55:23,695 | DEBUG | Processing hosts 3: TestHost3
2025-08-04 14:55:23,695 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:55:23,695 | DEBUG | Object data: {'name': 'TestHost3', 'type': 'Host', 'value': '**********', 'description': 'Test host object 3', 'overridable': False}
2025-08-04 14:55:27,882 | DEBUG | GET result for TestHost3: success=True, message='None'
2025-08-04 14:55:31,295 | DEBUG | PUT result for TestHost3: success=True, message='None'
2025-08-04 14:55:31,300 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344505_phase1_hosts.json
2025-08-04 14:55:31,300 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 14:55:31,301 | INFO | No API delay settings found to optimize
2025-08-04 14:55:31,301 | DEBUG | Processing networks 1: TestNetwork1
2025-08-04 14:55:31,301 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:55:31,301 | DEBUG | Object data: {'name': 'TestNetwork1', 'type': 'Network', 'value': '*************/24', 'description': 'Test network object 1', 'overridable': False}
2025-08-04 14:55:36,230 | DEBUG | GET result for TestNetwork1: success=True, message='None'
2025-08-04 14:55:40,278 | DEBUG | PUT result for TestNetwork1: success=True, message='None'
2025-08-04 14:55:40,278 | DEBUG | Processing networks 2: TestNetwork2
2025-08-04 14:55:40,278 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:55:40,278 | DEBUG | Object data: {'name': 'TestNetwork2', 'type': 'Network', 'value': '*********/16', 'description': 'Test network object 2', 'overridable': False}
2025-08-04 14:55:44,952 | DEBUG | GET result for TestNetwork2: success=True, message='None'
2025-08-04 14:55:48,415 | DEBUG | PUT result for TestNetwork2: success=True, message='None'
2025-08-04 14:55:48,416 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344505_phase1_networks.json
2025-08-04 14:55:48,416 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:55:48,416 | INFO | No API delay settings found to optimize
2025-08-04 14:55:48,416 | DEBUG | Processing services 1: TestHTTP
2025-08-04 14:55:48,416 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:55:48,416 | DEBUG | Object data: {'name': 'TestHTTP', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '80', 'description': 'Test HTTP service'}
2025-08-04 14:55:51,788 | DEBUG | GET result for TestHTTP: success=True, message='None'
2025-08-04 14:55:51,788 | DEBUG | PUT result for TestHTTP: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 14:55:51,788 | DEBUG | Processing services 2: TestHTTPS
2025-08-04 14:55:51,788 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:55:51,788 | DEBUG | Object data: {'name': 'TestHTTPS', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '443', 'description': 'Test HTTPS service'}
2025-08-04 14:55:54,853 | DEBUG | GET result for TestHTTPS: success=True, message='None'
2025-08-04 14:55:54,853 | DEBUG | PUT result for TestHTTPS: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 14:55:54,854 | DEBUG | Processing services 3: TestSSH
2025-08-04 14:55:54,854 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:55:54,854 | DEBUG | Object data: {'name': 'TestSSH', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '22', 'description': 'Test SSH service'}
2025-08-04 14:55:57,985 | DEBUG | GET result for TestSSH: success=True, message='None'
2025-08-04 14:55:57,985 | DEBUG | PUT result for TestSSH: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 14:55:57,986 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344505_phase1_services.json
2025-08-04 14:55:57,986 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:55:57,986 | INFO | No API delay settings found to optimize
2025-08-04 14:55:57,986 | DEBUG | Processing object_groups 1: TestNetworkGroup
2025-08-04 14:55:57,986 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 14:55:57,986 | DEBUG | Object data: {'name': 'TestNetworkGroup', 'type': 'NetworkGroup', 'description': 'Test network group', 'objects': [{'name': 'TestNetwork1', 'type': 'NetworkObject'}, {'name': 'TestHost1', 'type': 'HostObject'}]}
2025-08-04 14:56:01,320 | DEBUG | GET result for TestNetworkGroup: success=True, message='None'
2025-08-04 14:56:01,320 | DEBUG | PUT result for TestNetworkGroup: success=True, message='Network group exists and is correct (no update needed)'
2025-08-04 14:56:01,321 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344505_phase1_object_groups.json
2025-08-04 14:56:01,322 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:56:01,322 | INFO | No API delay settings found to optimize
2025-08-04 14:56:01,322 | DEBUG | Processing service_groups 1: TestWebServices
2025-08-04 14:56:01,322 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 14:56:01,322 | DEBUG | Object data: {'name': 'TestWebServices', 'type': 'PortObjectGroup', 'description': 'Test web services group', 'objects': [{'name': 'TestHTTP', 'type': 'ProtocolPortObject'}, {'name': 'TestHTTPS', 'type': 'ProtocolPortObject'}]}
2025-08-04 14:56:04,443 | DEBUG | GET result for TestWebServices: success=True, message='None'
2025-08-04 14:56:04,443 | DEBUG | PUT result for TestWebServices: success=True, message='Port object group exists and is correct (no update needed)'
2025-08-04 14:56:04,445 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344505_phase1_service_groups.json
2025-08-04 14:56:10,021 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:56:10,021 | INFO | No API delay settings found to optimize
2025-08-04 14:56:10,021 | DEBUG | Processing access_rules 1: TestRule1
2025-08-04 14:56:10,021 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'description', 'sourceNetworks', 'destinationNetworks', 'destinationPorts', 'acp_id']
2025-08-04 14:56:10,021 | DEBUG | Object data: {'name': 'TestRule1', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'description': 'Test access rule', 'sourceNetworks': {'objects': [{'name': 'TestHost1', 'type': 'Host'}]}, 'destinationNetworks': {'objects': [{'name': 'TestNetwork1', 'type': 'Network'}]}, 'destinationPorts': {'objects': [{'name': 'TestHTTP', 'type': 'ProtocolPortObject'}]}, 'acp_id': '005056BF-7B88-0ed3-0000-017187311047'}
2025-08-04 14:56:14,746 | DEBUG | GET result for TestRule1: success=False, message='No Access Control Policy found - cannot manage access rules'
2025-08-04 14:56:19,515 | DEBUG | POST result for TestRule1: success=False, message='No Access Control Policy found - cannot create access rules'
2025-08-04 14:56:19,515 | ERROR | Creation failed for TestRule1: No Access Control Policy found - cannot create access rules
2025-08-04 14:56:19,516 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344505_phase1_access_rules.json
2025-08-04 14:56:19,517 | INFO | ================================================================================
2025-08-04 14:56:19,517 | INFO | MIGRATION SUMMARY
2025-08-04 14:56:19,517 | INFO | ================================================================================
2025-08-04 14:56:19,517 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 14:56:19,517 | INFO |    • Total Objects: 11
2025-08-04 14:56:19,517 | INFO |    • Created: 0
2025-08-04 14:56:19,517 | INFO |    • Updated: 10
2025-08-04 14:56:19,517 | INFO |    • Failed: 1
2025-08-04 14:56:19,517 | INFO |    • Skipped: 0
2025-08-04 14:56:19,517 | INFO |    • Success Rate: 90.9%
2025-08-04 14:56:19,517 | INFO | ================================================================================
2025-08-04 14:56:19,518 | INFO | [FILE] Summary saved: migration_summary_migration_1754344505.json
