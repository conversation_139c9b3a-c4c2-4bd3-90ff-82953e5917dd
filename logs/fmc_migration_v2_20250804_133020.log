2025-08-04 13:30:20,571 | INFO | ================================================================================
2025-08-04 13:30:20,571 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 13:30:20,571 | INFO | Session ID: migration_1754339420
2025-08-04 13:30:20,571 | INFO | Connection Type: fmcapi
2025-08-04 13:30:20,571 | INFO | 🔍 Connection Diagnostic:
2025-08-04 13:30:20,571 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 13:30:20,571 | INFO |    • fmcapi Available: True
2025-08-04 13:30:20,572 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 13:30:25,577 | WARNING |    • fmcapi object creation failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x10233ba10>, 'Connection to ************* timed out. (connect timeout=5)'))
2025-08-04 13:30:25,578 | INFO | 🔍 Connection diagnostic complete
2025-08-04 13:30:25,578 | INFO | ================================================================================
2025-08-04 13:30:25,578 | INFO | 🔍 Running pre-migration validation...
2025-08-04 13:30:30,586 | INFO | ================================================================================
2025-08-04 13:30:30,586 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 13:30:30,586 | INFO | ================================================================================
2025-08-04 13:30:30,587 | INFO | [OK] Overall Status: PASSED
2025-08-04 13:30:30,587 | INFO | [OK] Configuration: 1122 objects found
2025-08-04 13:30:30,587 | INFO |    • host_objects: 629
2025-08-04 13:30:30,587 | INFO |    • network_objects: 63
2025-08-04 13:30:30,587 | INFO |    • service_objects: 29
2025-08-04 13:30:30,587 | INFO |    • object_groups: 111
2025-08-04 13:30:30,587 | INFO |    • service_groups: 66
2025-08-04 13:30:30,587 | INFO |    • access_rules: 224
2025-08-04 13:30:30,587 | WARNING | [WARN]  Connection: fmcapi - API test inconclusive
2025-08-04 13:30:30,587 | WARNING | [WARN]  Warnings (1):
2025-08-04 13:30:30,587 | WARNING |    • FMC API connection test inconclusive
2025-08-04 13:30:30,587 | INFO | ================================================================================
2025-08-04 13:30:30,594 | INFO | BULK_API_ENABLED: Processing 629 objects with bulk API
2025-08-04 13:30:30,595 | INFO | No API delay settings found to optimize
2025-08-04 13:30:30,595 | INFO | BULK_API_START: Beginning bulk operation for 629 hosts
2025-08-04 13:30:30,596 | INFO | BULK_API_COMPLETE: 629 objects in 0.00s (468018.0 obj/s)
2025-08-04 13:30:30,596 | ERROR | Bulk operation failed for RadSaratoga: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,596 | ERROR | Bulk operation failed for RadAmsMem: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,596 | ERROR | Bulk operation failed for RadStMarys: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,596 | ERROR | Bulk operation failed for RadSeton: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,596 | ERROR | Bulk operation failed for RadBellevue: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,605 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339420_phase1_hosts.json
2025-08-04 13:30:30,605 | INFO | BULK_API_ENABLED: Processing 63 objects with bulk API
2025-08-04 13:30:30,605 | INFO | No API delay settings found to optimize
2025-08-04 13:30:30,605 | INFO | BULK_API_START: Beginning bulk operation for 63 networks
2025-08-04 13:30:30,605 | INFO | BULK_API_COMPLETE: 63 objects in 0.00s (484846.2 obj/s)
2025-08-04 13:30:30,605 | ERROR | Bulk operation failed for TeleMedVT3: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,605 | ERROR | Bulk operation failed for TelemedVT4: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,605 | ERROR | Bulk operation failed for TelemedVT5: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,605 | ERROR | Bulk operation failed for TeleMedVT1: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,605 | ERROR | Bulk operation failed for Medent.VPN.net: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,606 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339420_phase1_networks.json
2025-08-04 13:30:30,607 | INFO | BULK_API_ENABLED: Processing 29 objects with bulk API
2025-08-04 13:30:30,607 | INFO | No API delay settings found to optimize
2025-08-04 13:30:30,607 | INFO | BULK_API_START: Beginning bulk operation for 29 services
2025-08-04 13:30:30,607 | INFO | BULK_API_COMPLETE: 29 objects in 0.00s (453861.3 obj/s)
2025-08-04 13:30:30,607 | ERROR | Bulk operation failed for obj-tcp-eq-80: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,607 | ERROR | Bulk operation failed for obj-tcp-eq-15002: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,607 | ERROR | Bulk operation failed for obj-tcp-eq-15331: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,607 | ERROR | Bulk operation failed for obj-tcp-eq-3389: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,607 | ERROR | Bulk operation failed for obj-tcp-eq-2222: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,608 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339420_phase1_services.json
2025-08-04 13:30:30,608 | INFO | BULK_API_ENABLED: Processing 111 objects with bulk API
2025-08-04 13:30:30,608 | INFO | No API delay settings found to optimize
2025-08-04 13:30:30,608 | INFO | BULK_API_START: Beginning bulk operation for 111 object_groups
2025-08-04 13:30:30,608 | INFO | BULK_API_COMPLETE: 111 objects in 0.00s (630850.6 obj/s)
2025-08-04 13:30:30,608 | ERROR | Bulk operation failed for Medivators: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,608 | ERROR | Bulk operation failed for NUVODIA.INTERNAL.GROUP.NEW: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,608 | ERROR | Bulk operation failed for NUVODIA.INTERNAL.PEER.NET1: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,608 | ERROR | Bulk operation failed for DM_INLINE_NETWORK_4: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,608 | ERROR | Bulk operation failed for DM_INLINE_NETWORK_6: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,609 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339420_phase1_object_groups.json
2025-08-04 13:30:30,609 | INFO | BULK_API_ENABLED: Processing 66 objects with bulk API
2025-08-04 13:30:30,609 | INFO | No API delay settings found to optimize
2025-08-04 13:30:30,610 | INFO | BULK_API_START: Beginning bulk operation for 66 service_groups
2025-08-04 13:30:30,610 | INFO | BULK_API_COMPLETE: 66 objects in 0.00s (607070.3 obj/s)
2025-08-04 13:30:30,610 | ERROR | Bulk operation failed for PaceGlobalgrp: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,610 | ERROR | Bulk operation failed for timeservice: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,610 | ERROR | Bulk operation failed for timeserviceUDP: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,610 | ERROR | Bulk operation failed for QUEST: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,610 | ERROR | Bulk operation failed for citrixXML: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,611 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339420_phase1_service_groups.json
2025-08-04 13:30:30,611 | INFO | BULK_API_ENABLED: Processing 224 objects with bulk API
2025-08-04 13:30:30,611 | INFO | No API delay settings found to optimize
2025-08-04 13:30:30,611 | INFO | BULK_API_START: Beginning bulk operation for 224 access_rules
2025-08-04 13:30:30,611 | INFO | BULK_API_COMPLETE: 224 objects in 0.00s (652900.7 obj/s)
2025-08-04 13:30:30,611 | ERROR | Bulk operation failed for inside_access_in_rule_1: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,612 | ERROR | Bulk operation failed for inside_access_in_rule_2: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,612 | ERROR | Bulk operation failed for inside_access_in_rule_3: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,612 | ERROR | Bulk operation failed for inside_access_in_rule_4: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,612 | ERROR | Bulk operation failed for inside_access_in_rule_5: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:30:30,614 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339420_phase1_access_rules.json
2025-08-04 13:30:30,614 | INFO | ================================================================================
2025-08-04 13:30:30,615 | INFO | MIGRATION SUMMARY
2025-08-04 13:30:30,615 | INFO | ================================================================================
2025-08-04 13:30:30,615 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 13:30:30,615 | INFO |    • Total Objects: 1122
2025-08-04 13:30:30,615 | INFO |    • Created: 0
2025-08-04 13:30:30,615 | INFO |    • Updated: 0
2025-08-04 13:30:30,615 | INFO |    • Failed: 1122
2025-08-04 13:30:30,615 | INFO |    • Skipped: 0
2025-08-04 13:30:30,615 | INFO |    • Success Rate: 0.0%
2025-08-04 13:30:30,615 | INFO | ================================================================================
2025-08-04 13:30:30,615 | INFO | [FILE] Summary saved: migration_summary_migration_1754339420.json
2025-08-04 13:30:30,615 | WARNING | [WARN]  Migration completed with 1122 failures
