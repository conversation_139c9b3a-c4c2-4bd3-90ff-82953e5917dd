2025-08-04 14:41:48,284 | INFO | ================================================================================
2025-08-04 14:41:48,284 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 14:41:48,284 | INFO | Session ID: migration_1754343708
2025-08-04 14:41:48,284 | INFO | Connection Type: fmcapi
2025-08-04 14:41:48,284 | INFO | 🔍 Connection Diagnostic:
2025-08-04 14:41:48,284 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 14:41:48,285 | INFO |    • fmcapi Available: True
2025-08-04 14:41:48,285 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 14:41:51,098 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 14:41:51,098 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 14:41:51,098 | INFO | 🔍 Connection diagnostic complete
2025-08-04 14:41:51,098 | INFO | ================================================================================
2025-08-04 14:41:51,099 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:41:51,099 | INFO | No API delay settings found to optimize
2025-08-04 14:42:13,858 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343708_phase1_hosts.json
2025-08-04 14:42:13,858 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 14:42:13,858 | INFO | No API delay settings found to optimize
2025-08-04 14:42:30,352 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343708_phase1_networks.json
2025-08-04 14:42:30,352 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:42:30,352 | INFO | No API delay settings found to optimize
2025-08-04 14:42:36,669 | ERROR | Update failed for TestHTTP: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:42:42,558 | ERROR | Update failed for TestHTTPS: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:42:48,535 | ERROR | Update failed for TestSSH: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:42:48,536 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343708_phase1_services.json
2025-08-04 14:42:48,536 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:42:48,536 | INFO | No API delay settings found to optimize
2025-08-04 14:43:03,853 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343708_phase1_object_groups.json
2025-08-04 14:43:03,853 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:43:03,853 | INFO | No API delay settings found to optimize
2025-08-04 14:43:16,439 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343708_phase1_service_groups.json
2025-08-04 14:43:16,439 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:43:16,440 | INFO | No API delay settings found to optimize
2025-08-04 14:43:25,643 | ERROR | Creation failed for TestRule1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:25,645 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343708_phase1_access_rules.json
2025-08-04 14:43:25,645 | INFO | ================================================================================
2025-08-04 14:43:25,645 | INFO | MIGRATION SUMMARY
2025-08-04 14:43:25,645 | INFO | ================================================================================
2025-08-04 14:43:25,646 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 14:43:25,646 | INFO |    • Total Objects: 11
2025-08-04 14:43:25,647 | INFO |    • Created: 2
2025-08-04 14:43:25,647 | INFO |    • Updated: 5
2025-08-04 14:43:25,647 | INFO |    • Failed: 4
2025-08-04 14:43:25,647 | INFO |    • Skipped: 0
2025-08-04 14:43:25,647 | INFO |    • Success Rate: 63.6%
2025-08-04 14:43:25,648 | INFO | ================================================================================
2025-08-04 14:43:25,649 | INFO | [FILE] Summary saved: migration_summary_migration_1754343708.json
