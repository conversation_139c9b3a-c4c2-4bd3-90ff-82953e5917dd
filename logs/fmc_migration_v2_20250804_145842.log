2025-08-04 14:58:42,272 | INFO | ================================================================================
2025-08-04 14:58:42,272 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 14:58:42,272 | INFO | Session ID: migration_1754344722
2025-08-04 14:58:42,272 | INFO | Connection Type: fmcapi
2025-08-04 14:58:42,272 | INFO | 🔍 Connection Diagnostic:
2025-08-04 14:58:42,272 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 14:58:42,272 | INFO |    • fmcapi Available: True
2025-08-04 14:58:42,272 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 14:58:45,037 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 14:58:45,037 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 14:58:45,037 | INFO | 🔍 Connection diagnostic complete
2025-08-04 14:58:45,037 | INFO | ================================================================================
2025-08-04 14:58:45,038 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:58:45,038 | INFO | No API delay settings found to optimize
2025-08-04 14:59:08,326 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344722_phase1_hosts.json
2025-08-04 14:59:08,326 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 14:59:08,326 | INFO | No API delay settings found to optimize
2025-08-04 14:59:25,478 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344722_phase1_networks.json
2025-08-04 14:59:25,478 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:59:25,478 | INFO | No API delay settings found to optimize
2025-08-04 14:59:34,805 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344722_phase1_services.json
2025-08-04 14:59:34,805 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:59:34,805 | INFO | No API delay settings found to optimize
2025-08-04 14:59:38,351 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344722_phase1_object_groups.json
2025-08-04 14:59:38,351 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:59:38,351 | INFO | No API delay settings found to optimize
2025-08-04 14:59:41,369 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344722_phase1_service_groups.json
2025-08-04 14:59:46,795 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:59:46,795 | INFO | No API delay settings found to optimize
2025-08-04 14:59:56,668 | ERROR | Creation failed for TestRule1: No Access Control Policy found - cannot create access rules
2025-08-04 14:59:56,668 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344722_phase1_access_rules.json
2025-08-04 14:59:56,668 | INFO | ================================================================================
2025-08-04 14:59:56,669 | INFO | MIGRATION SUMMARY
2025-08-04 14:59:56,669 | INFO | ================================================================================
2025-08-04 14:59:56,669 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 14:59:56,669 | INFO |    • Total Objects: 11
2025-08-04 14:59:56,669 | INFO |    • Created: 0
2025-08-04 14:59:56,669 | INFO |    • Updated: 10
2025-08-04 14:59:56,669 | INFO |    • Failed: 1
2025-08-04 14:59:56,669 | INFO |    • Skipped: 0
2025-08-04 14:59:56,669 | INFO |    • Success Rate: 90.9%
2025-08-04 14:59:56,669 | INFO | ================================================================================
2025-08-04 14:59:56,669 | INFO | [FILE] Summary saved: migration_summary_migration_1754344722.json
