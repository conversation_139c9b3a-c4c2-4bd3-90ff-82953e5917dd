2025-08-04 15:37:55,085 | INFO | ================================================================================
2025-08-04 15:37:55,085 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 15:37:55,085 | INFO | Session ID: migration_1754347075
2025-08-04 15:37:55,085 | INFO | Connection Type: fmcapi
2025-08-04 15:37:55,085 | INFO | 🔍 Connection Diagnostic:
2025-08-04 15:37:55,085 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 15:37:55,085 | INFO |    • fmcapi Available: True
2025-08-04 15:37:55,085 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 15:37:57,829 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 15:37:57,829 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 15:37:57,829 | INFO | 🔍 Connection diagnostic complete
2025-08-04 15:37:57,829 | INFO | ================================================================================
2025-08-04 15:37:57,829 | INFO | BULK_API_ENABLED: Processing 100 objects with bulk API
2025-08-04 15:37:57,829 | INFO | No API delay settings found to optimize
2025-08-04 15:37:57,829 | INFO | BULK_API_START: Beginning bulk operation for 100 hosts
2025-08-04 15:37:57,830 | DEBUG | Using fmcapi bulk_post for 100 objects
2025-08-04 15:38:03,172 | DEBUG | bulk_post failed: HTTPSConnectionPool(host='fmcrestapisandbox.cisco.com', port=443): Read timed out. (read timeout=5), falling back to individual creation
2025-08-04 15:38:03,173 | DEBUG | Bulk create failed with 400, processing 100 objects individually
2025-08-04 15:47:54,982 | INFO | BULK_API_COMPLETE: 100 objects in 597.15s (0.2 obj/s)
2025-08-04 15:47:54,982 | ERROR | Bulk operation failed for XENAPP02: Create failed: Data validation failed: No data provided
2025-08-04 15:47:54,982 | ERROR | Bulk operation failed for NLHNAS11: Create failed: Data validation failed: No data provided
2025-08-04 15:47:54,982 | ERROR | Bulk operation failed for MAGIC_A-iDRAC: Create failed: Data validation failed: No data provided
2025-08-04 15:47:54,982 | ERROR | Bulk operation failed for MAGIC_E-iDRAC: Create failed: Data validation failed: No data provided
2025-08-04 15:47:54,982 | ERROR | Bulk operation failed for MAGIC_D-NEWiSCSI: Create failed: Data validation failed: No data provided
2025-08-04 15:47:54,986 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754347075_phase1_hosts.json
2025-08-04 15:47:54,986 | INFO | BULK_API_ENABLED: Processing 20 objects with bulk API
2025-08-04 15:47:54,986 | INFO | No API delay settings found to optimize
2025-08-04 15:47:54,986 | INFO | BULK_API_START: Beginning bulk operation for 20 networks
2025-08-04 15:47:54,987 | DEBUG | Using fmcapi bulk_post for 20 objects
2025-08-04 15:47:55,320 | DEBUG | Bulk create failed with 400, processing 20 objects individually
2025-08-04 15:49:24,636 | INFO | BULK_API_COMPLETE: 20 objects in 89.65s (0.2 obj/s)
2025-08-04 15:49:24,636 | ERROR | Bulk operation failed for TeleMedVT3: Create failed: Data validation failed: No data provided
2025-08-04 15:49:24,636 | ERROR | Bulk operation failed for TelemedVT4: Create failed: Data validation failed: No data provided
2025-08-04 15:49:24,636 | ERROR | Bulk operation failed for TelemedVT5: Create failed: Data validation failed: No data provided
2025-08-04 15:49:24,636 | ERROR | Bulk operation failed for TeleMedVT1: Create failed: Data validation failed: No data provided
2025-08-04 15:49:24,637 | ERROR | Bulk operation failed for Medent.VPN.net: Create failed: Data validation failed: No data provided
2025-08-04 15:49:24,637 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754347075_phase1_networks.json
2025-08-04 15:49:24,638 | INFO | BULK_API_ENABLED: Processing 10 objects with bulk API
2025-08-04 15:49:24,638 | INFO | No API delay settings found to optimize
2025-08-04 15:49:24,638 | INFO | BULK_API_START: Beginning bulk operation for 10 services
2025-08-04 15:49:24,638 | DEBUG | Using fmcapi bulk_post for 10 objects
2025-08-04 15:49:24,836 | DEBUG | Bulk create failed with 400, processing 10 objects individually
2025-08-04 15:49:53,983 | INFO | BULK_API_COMPLETE: 10 objects in 29.34s (0.3 obj/s)
2025-08-04 15:49:53,983 | ERROR | Bulk operation failed for obj-tcp-eq-80: Create failed: Data validation failed: No data provided
2025-08-04 15:49:53,983 | ERROR | Bulk operation failed for obj-tcp-eq-15002: Create failed: Data validation failed: No data provided
2025-08-04 15:49:53,983 | ERROR | Bulk operation failed for obj-tcp-eq-15331: Create failed: Data validation failed: No data provided
2025-08-04 15:49:53,983 | ERROR | Bulk operation failed for obj-tcp-eq-3389: Create failed: Data validation failed: No data provided
2025-08-04 15:49:53,983 | ERROR | Bulk operation failed for obj-tcp-eq-2222: Create failed: Data validation failed: No data provided
2025-08-04 15:49:53,983 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754347075_phase1_services.json
