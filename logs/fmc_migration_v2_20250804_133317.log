2025-08-04 13:33:17,829 | INFO | ================================================================================
2025-08-04 13:33:17,829 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 13:33:17,829 | INFO | Session ID: migration_1754339597
2025-08-04 13:33:17,829 | INFO | Connection Type: fmcapi
2025-08-04 13:33:17,829 | INFO | 🔍 Connection Diagnostic:
2025-08-04 13:33:17,829 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 13:33:17,829 | INFO |    • fmcapi Available: True
2025-08-04 13:33:17,829 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 13:33:22,836 | WARNING |    • fmcapi object creation failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x104c5fa10>, 'Connection to ************* timed out. (connect timeout=5)'))
2025-08-04 13:33:22,836 | INFO | 🔍 Connection diagnostic complete
2025-08-04 13:33:22,836 | INFO | ================================================================================
2025-08-04 13:33:22,836 | INFO | 🔍 Running pre-migration validation...
2025-08-04 13:33:27,846 | INFO | ================================================================================
2025-08-04 13:33:27,847 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 13:33:27,847 | INFO | ================================================================================
2025-08-04 13:33:27,847 | INFO | [OK] Overall Status: PASSED
2025-08-04 13:33:27,847 | INFO | [OK] Configuration: 1122 objects found
2025-08-04 13:33:27,847 | INFO |    • host_objects: 629
2025-08-04 13:33:27,847 | INFO |    • network_objects: 63
2025-08-04 13:33:27,847 | INFO |    • service_objects: 29
2025-08-04 13:33:27,847 | INFO |    • object_groups: 111
2025-08-04 13:33:27,847 | INFO |    • service_groups: 66
2025-08-04 13:33:27,847 | INFO |    • access_rules: 224
2025-08-04 13:33:27,847 | WARNING | [WARN]  Connection: fmcapi - API test inconclusive
2025-08-04 13:33:27,847 | WARNING | [WARN]  Warnings (1):
2025-08-04 13:33:27,847 | WARNING |    • FMC API connection test inconclusive
2025-08-04 13:33:27,848 | INFO | ================================================================================
2025-08-04 13:33:27,857 | INFO | BULK_API_ENABLED: Processing 629 objects with bulk API
2025-08-04 13:33:27,858 | INFO | No API delay settings found to optimize
2025-08-04 13:33:27,858 | INFO | BULK_API_START: Beginning bulk operation for 629 hosts
2025-08-04 13:33:27,858 | WARNING | FMC connection not properly established (serverVersion is None). Falling back to individual operations.
2025-08-04 13:33:27,858 | INFO | Bulk operation returned empty results, falling back to individual calls
2025-08-04 13:33:37,867 | ERROR | Creation failed for RadSaratoga: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x1053d0690>, 'Connection to ************* timed out. (connect timeout=5)'))
2025-08-04 13:33:47,874 | ERROR | Creation failed for RadAmsMem: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x1053d0410>, 'Connection to ************* timed out. (connect timeout=5)'))
2025-08-04 13:33:57,882 | ERROR | Creation failed for RadStMarys: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x1053d11d0>, 'Connection to ************* timed out. (connect timeout=5)'))
2025-08-04 13:34:07,891 | ERROR | Creation failed for RadSeton: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x1053d1810>, 'Connection to ************* timed out. (connect timeout=5)'))
2025-08-04 13:34:17,900 | ERROR | Creation failed for RadBellevue: fmcapi post() failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x1053d1e50>, 'Connection to ************* timed out. (connect timeout=5)'))
