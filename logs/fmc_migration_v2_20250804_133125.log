2025-08-04 13:31:25,877 | INFO | ================================================================================
2025-08-04 13:31:25,877 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 13:31:25,877 | INFO | Session ID: migration_1754339485
2025-08-04 13:31:25,877 | INFO | Connection Type: fmcapi
2025-08-04 13:31:25,877 | INFO | 🔍 Connection Diagnostic:
2025-08-04 13:31:25,877 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 13:31:25,877 | INFO |    • fmcapi Available: True
2025-08-04 13:31:25,877 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 13:31:30,883 | WARNING |    • fmcapi object creation failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x102af7a10>, 'Connection to ************* timed out. (connect timeout=5)'))
2025-08-04 13:31:30,884 | INFO | 🔍 Connection diagnostic complete
2025-08-04 13:31:30,884 | INFO | ================================================================================
2025-08-04 13:31:30,884 | INFO | 🔍 Running pre-migration validation...
2025-08-04 13:31:35,895 | INFO | ================================================================================
2025-08-04 13:31:35,895 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 13:31:35,895 | INFO | ================================================================================
2025-08-04 13:31:35,895 | INFO | [OK] Overall Status: PASSED
2025-08-04 13:31:35,895 | INFO | [OK] Configuration: 1122 objects found
2025-08-04 13:31:35,895 | INFO |    • host_objects: 629
2025-08-04 13:31:35,895 | INFO |    • network_objects: 63
2025-08-04 13:31:35,896 | INFO |    • service_objects: 29
2025-08-04 13:31:35,896 | INFO |    • object_groups: 111
2025-08-04 13:31:35,896 | INFO |    • service_groups: 66
2025-08-04 13:31:35,897 | INFO |    • access_rules: 224
2025-08-04 13:31:35,897 | WARNING | [WARN]  Connection: fmcapi - API test inconclusive
2025-08-04 13:31:35,897 | WARNING | [WARN]  Warnings (1):
2025-08-04 13:31:35,897 | WARNING |    • FMC API connection test inconclusive
2025-08-04 13:31:35,897 | INFO | ================================================================================
2025-08-04 13:31:35,905 | INFO | BULK_API_ENABLED: Processing 629 objects with bulk API
2025-08-04 13:31:35,905 | INFO | No API delay settings found to optimize
2025-08-04 13:31:35,906 | INFO | BULK_API_START: Beginning bulk operation for 629 hosts
2025-08-04 13:31:35,909 | ERROR | Detailed bulk operation error: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
Traceback: Traceback (most recent call last):
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 1949, in _perform_direct_bulk_create
    temp_fmc_obj = temp_obj._get_fmcapi_object(self.fmc)
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 539, in _get_fmcapi_object
    return fmcapi.Hosts(fmc=fmc_conn)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/object_services/hosts.py", line 29, in __init__
    super().__init__(fmc, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/apiclasstemplate.py", line 56, in __init__
    if self.fmc.serverVersion < self.FIRST_SUPPORTED_FMC_VERSION:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: '<' not supported between instances of 'NoneType' and 'str'

2025-08-04 13:31:35,910 | ERROR | Detailed bulk operation error: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
Traceback: Traceback (most recent call last):
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 1949, in _perform_direct_bulk_create
    temp_fmc_obj = temp_obj._get_fmcapi_object(self.fmc)
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 539, in _get_fmcapi_object
    return fmcapi.Hosts(fmc=fmc_conn)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/object_services/hosts.py", line 29, in __init__
    super().__init__(fmc, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/apiclasstemplate.py", line 56, in __init__
    if self.fmc.serverVersion < self.FIRST_SUPPORTED_FMC_VERSION:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: '<' not supported between instances of 'NoneType' and 'str'

2025-08-04 13:31:35,911 | INFO | BULK_API_COMPLETE: 629 objects in 0.01s (120637.3 obj/s)
2025-08-04 13:31:35,911 | ERROR | Bulk operation failed for RadSaratoga: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,911 | ERROR | Bulk operation failed for RadAmsMem: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,911 | ERROR | Bulk operation failed for RadStMarys: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,911 | ERROR | Bulk operation failed for RadSeton: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,911 | ERROR | Bulk operation failed for RadBellevue: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,918 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339485_phase1_hosts.json
2025-08-04 13:31:35,918 | INFO | BULK_API_ENABLED: Processing 63 objects with bulk API
2025-08-04 13:31:35,918 | INFO | No API delay settings found to optimize
2025-08-04 13:31:35,918 | INFO | BULK_API_START: Beginning bulk operation for 63 networks
2025-08-04 13:31:35,919 | ERROR | Detailed bulk operation error: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
Traceback: Traceback (most recent call last):
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 1949, in _perform_direct_bulk_create
    temp_fmc_obj = temp_obj._get_fmcapi_object(self.fmc)
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 558, in _get_fmcapi_object
    return fmcapi.Networks(fmc=fmc_conn)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/object_services/networks.py", line 28, in __init__
    super().__init__(fmc, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/apiclasstemplate.py", line 56, in __init__
    if self.fmc.serverVersion < self.FIRST_SUPPORTED_FMC_VERSION:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: '<' not supported between instances of 'NoneType' and 'str'

2025-08-04 13:31:35,919 | INFO | BULK_API_COMPLETE: 63 objects in 0.00s (66947.3 obj/s)
2025-08-04 13:31:35,919 | ERROR | Bulk operation failed for TeleMedVT3: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,919 | ERROR | Bulk operation failed for TelemedVT4: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,919 | ERROR | Bulk operation failed for TelemedVT5: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,919 | ERROR | Bulk operation failed for TeleMedVT1: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,919 | ERROR | Bulk operation failed for Medent.VPN.net: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,920 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339485_phase1_networks.json
2025-08-04 13:31:35,920 | INFO | BULK_API_ENABLED: Processing 29 objects with bulk API
2025-08-04 13:31:35,920 | INFO | No API delay settings found to optimize
2025-08-04 13:31:35,920 | INFO | BULK_API_START: Beginning bulk operation for 29 services
2025-08-04 13:31:35,921 | ERROR | Detailed bulk operation error: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
Traceback: Traceback (most recent call last):
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 1949, in _perform_direct_bulk_create
    temp_fmc_obj = temp_obj._get_fmcapi_object(self.fmc)
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 579, in _get_fmcapi_object
    return fmcapi.ProtocolPortObjects(fmc=fmc_conn)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/object_services/protocolportobjects.py", line 27, in __init__
    super().__init__(fmc, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/apiclasstemplate.py", line 56, in __init__
    if self.fmc.serverVersion < self.FIRST_SUPPORTED_FMC_VERSION:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: '<' not supported between instances of 'NoneType' and 'str'

2025-08-04 13:31:35,921 | INFO | BULK_API_COMPLETE: 29 objects in 0.00s (38724.9 obj/s)
2025-08-04 13:31:35,921 | ERROR | Bulk operation failed for obj-tcp-eq-80: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,921 | ERROR | Bulk operation failed for obj-tcp-eq-15002: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,921 | ERROR | Bulk operation failed for obj-tcp-eq-15331: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,921 | ERROR | Bulk operation failed for obj-tcp-eq-3389: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,921 | ERROR | Bulk operation failed for obj-tcp-eq-2222: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,922 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339485_phase1_services.json
2025-08-04 13:31:35,922 | INFO | BULK_API_ENABLED: Processing 111 objects with bulk API
2025-08-04 13:31:35,922 | INFO | No API delay settings found to optimize
2025-08-04 13:31:35,922 | INFO | BULK_API_START: Beginning bulk operation for 111 object_groups
2025-08-04 13:31:35,923 | ERROR | Detailed bulk operation error: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
Traceback: Traceback (most recent call last):
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 1949, in _perform_direct_bulk_create
    temp_fmc_obj = temp_obj._get_fmcapi_object(self.fmc)
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 598, in _get_fmcapi_object
    return fmcapi.NetworkGroups(fmc=fmc_conn)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/object_services/networkgroups.py", line 33, in __init__
    super().__init__(fmc, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/apiclasstemplate.py", line 56, in __init__
    if self.fmc.serverVersion < self.FIRST_SUPPORTED_FMC_VERSION:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: '<' not supported between instances of 'NoneType' and 'str'

2025-08-04 13:31:35,923 | INFO | BULK_API_COMPLETE: 111 objects in 0.00s (124416.8 obj/s)
2025-08-04 13:31:35,923 | ERROR | Bulk operation failed for Medivators: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,923 | ERROR | Bulk operation failed for NUVODIA.INTERNAL.GROUP.NEW: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,923 | ERROR | Bulk operation failed for NUVODIA.INTERNAL.PEER.NET1: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,923 | ERROR | Bulk operation failed for DM_INLINE_NETWORK_4: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,923 | ERROR | Bulk operation failed for DM_INLINE_NETWORK_6: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,925 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339485_phase1_object_groups.json
2025-08-04 13:31:35,925 | INFO | BULK_API_ENABLED: Processing 66 objects with bulk API
2025-08-04 13:31:35,925 | INFO | No API delay settings found to optimize
2025-08-04 13:31:35,925 | INFO | BULK_API_START: Beginning bulk operation for 66 service_groups
2025-08-04 13:31:35,926 | ERROR | Detailed bulk operation error: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
Traceback: Traceback (most recent call last):
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 1949, in _perform_direct_bulk_create
    temp_fmc_obj = temp_obj._get_fmcapi_object(self.fmc)
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 617, in _get_fmcapi_object
    return fmcapi.PortObjectGroups(fmc=fmc_conn)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/object_services/portobjectgroups.py", line 32, in __init__
    super().__init__(fmc, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/apiclasstemplate.py", line 56, in __init__
    if self.fmc.serverVersion < self.FIRST_SUPPORTED_FMC_VERSION:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: '<' not supported between instances of 'NoneType' and 'str'

2025-08-04 13:31:35,926 | INFO | BULK_API_COMPLETE: 66 objects in 0.00s (100773.2 obj/s)
2025-08-04 13:31:35,926 | ERROR | Bulk operation failed for PaceGlobalgrp: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,926 | ERROR | Bulk operation failed for timeservice: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,926 | ERROR | Bulk operation failed for timeserviceUDP: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,926 | ERROR | Bulk operation failed for QUEST: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,926 | ERROR | Bulk operation failed for citrixXML: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,927 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339485_phase1_service_groups.json
2025-08-04 13:31:35,927 | INFO | BULK_API_ENABLED: Processing 224 objects with bulk API
2025-08-04 13:31:35,927 | INFO | No API delay settings found to optimize
2025-08-04 13:31:35,927 | INFO | BULK_API_START: Beginning bulk operation for 224 access_rules
2025-08-04 13:31:35,928 | ERROR | Detailed bulk operation error: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
Traceback: Traceback (most recent call last):
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 1949, in _perform_direct_bulk_create
    temp_fmc_obj = temp_obj._get_fmcapi_object(self.fmc)
  File "/Users/<USER>/projects/clearwater/cisco-fmc/fmc_migration_v2.py", line 647, in _get_fmcapi_object
    return fmcapi.AccessRules(fmc=fmc_conn)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/policy_services/accessrules.py", line 176, in __init__
    super().__init__(fmc, **kwargs)
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/Users/<USER>/projects/clearwater/cisco-fmc/venv/lib/python3.13/site-packages/fmcapi/api_objects/apiclasstemplate.py", line 56, in __init__
    if self.fmc.serverVersion < self.FIRST_SUPPORTED_FMC_VERSION:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: '<' not supported between instances of 'NoneType' and 'str'

2025-08-04 13:31:35,928 | INFO | BULK_API_COMPLETE: 224 objects in 0.00s (243211.0 obj/s)
2025-08-04 13:31:35,928 | ERROR | Bulk operation failed for inside_access_in_rule_1: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,928 | ERROR | Bulk operation failed for inside_access_in_rule_2: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,928 | ERROR | Bulk operation failed for inside_access_in_rule_3: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,928 | ERROR | Bulk operation failed for inside_access_in_rule_4: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,928 | ERROR | Bulk operation failed for inside_access_in_rule_5: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:31:35,930 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339485_phase1_access_rules.json
2025-08-04 13:31:35,930 | INFO | ================================================================================
2025-08-04 13:31:35,930 | INFO | MIGRATION SUMMARY
2025-08-04 13:31:35,931 | INFO | ================================================================================
2025-08-04 13:31:35,931 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 13:31:35,931 | INFO |    • Total Objects: 1122
2025-08-04 13:31:35,931 | INFO |    • Created: 0
2025-08-04 13:31:35,931 | INFO |    • Updated: 0
2025-08-04 13:31:35,931 | INFO |    • Failed: 1122
2025-08-04 13:31:35,931 | INFO |    • Skipped: 0
2025-08-04 13:31:35,931 | INFO |    • Success Rate: 0.0%
2025-08-04 13:31:35,931 | INFO | ================================================================================
2025-08-04 13:31:35,931 | INFO | [FILE] Summary saved: migration_summary_migration_1754339485.json
2025-08-04 13:31:35,931 | WARNING | [WARN]  Migration completed with 1122 failures
