2025-08-04 15:18:30,281 | INFO | ================================================================================
2025-08-04 15:18:30,281 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 15:18:30,281 | INFO | Session ID: migration_1754345910
2025-08-04 15:18:30,281 | INFO | Connection Type: fmcapi
2025-08-04 15:18:30,281 | INFO | 🔍 Connection Diagnostic:
2025-08-04 15:18:30,281 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 15:18:30,281 | INFO |    • fmcapi Available: True
2025-08-04 15:18:30,281 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 15:18:33,024 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 15:18:33,024 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 15:18:33,024 | INFO | 🔍 Connection diagnostic complete
2025-08-04 15:18:33,024 | INFO | ================================================================================
2025-08-04 15:18:33,025 | INFO | 🔍 Running pre-migration validation...
2025-08-04 15:18:36,843 | INFO | ================================================================================
2025-08-04 15:18:36,843 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 15:18:36,843 | INFO | ================================================================================
2025-08-04 15:18:36,843 | INFO | [OK] Overall Status: PASSED
2025-08-04 15:18:36,843 | INFO | [OK] Configuration: 17 objects found
2025-08-04 15:18:36,843 | INFO |    • host_objects: 5
2025-08-04 15:18:36,843 | INFO |    • network_objects: 3
2025-08-04 15:18:36,843 | INFO |    • service_objects: 4
2025-08-04 15:18:36,843 | INFO |    • object_groups: 2
2025-08-04 15:18:36,843 | INFO |    • service_groups: 1
2025-08-04 15:18:36,843 | INFO |    • access_rules: 2
2025-08-04 15:18:36,843 | INFO | [OK] Connection: fmcapi - API accessible
2025-08-04 15:18:36,843 | INFO | ================================================================================
2025-08-04 15:18:36,844 | INFO | BULK_API_ENABLED: Processing 5 objects with bulk API
2025-08-04 15:18:36,844 | INFO | No API delay settings found to optimize
2025-08-04 15:19:16,313 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345910_phase1_hosts.json
2025-08-04 15:19:16,313 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 15:19:16,313 | INFO | No API delay settings found to optimize
2025-08-04 15:19:40,795 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345910_phase1_networks.json
2025-08-04 15:19:40,795 | INFO | BULK_API_ENABLED: Processing 4 objects with bulk API
2025-08-04 15:19:40,795 | INFO | No API delay settings found to optimize
2025-08-04 15:19:52,323 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345910_phase1_services.json
2025-08-04 15:19:52,323 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:19:52,323 | INFO | No API delay settings found to optimize
2025-08-04 15:19:58,594 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345910_phase1_object_groups.json
2025-08-04 15:19:58,595 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 15:19:58,595 | INFO | No API delay settings found to optimize
2025-08-04 15:20:01,440 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345910_phase1_service_groups.json
2025-08-04 15:20:04,014 | INFO | Attempting to get or create Access Control Policy...
2025-08-04 15:20:06,203 | WARNING | No existing Access Control Policies found
2025-08-04 15:20:06,203 | INFO | Creating new Access Control Policy...
2025-08-04 15:20:07,098 | INFO | Successfully created new Access Control Policy: ASA Migration ACP 1754346006 (ID: 005056BF-7B88-0ed3-0000-017187332900)
2025-08-04 15:20:07,098 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:20:07,098 | INFO | No API delay settings found to optimize
2025-08-04 15:21:53,908 | ERROR | Creation failed for TestRule_AllowWeb: fmcapi post() failed - no ID returned.
