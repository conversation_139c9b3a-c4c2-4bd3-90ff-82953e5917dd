2025-08-04 14:41:48,284 | INFO | ================================================================================
2025-08-04 14:41:48,284 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 14:41:48,284 | INFO | Session ID: migration_1754343708
2025-08-04 14:41:48,284 | INFO | Connection Type: fmcapi
2025-08-04 14:41:48,284 | INFO | 🔍 Connection Diagnostic:
2025-08-04 14:41:48,284 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 14:41:48,285 | INFO |    • fmcapi Available: True
2025-08-04 14:41:48,285 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 14:41:51,098 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 14:41:51,098 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 14:41:51,098 | INFO | 🔍 Connection diagnostic complete
2025-08-04 14:41:51,098 | INFO | ================================================================================
2025-08-04 14:41:51,099 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:41:51,099 | INFO | No API delay settings found to optimize
2025-08-04 14:41:51,099 | DEBUG | Processing hosts 1: TestHost1
2025-08-04 14:41:51,099 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:41:51,099 | DEBUG | Object data: {'name': 'TestHost1', 'type': 'Host', 'value': '************', 'description': 'Test host object 1', 'overridable': False}
2025-08-04 14:41:55,118 | DEBUG | GET result for TestHost1: success=True, message='None'
2025-08-04 14:41:58,742 | DEBUG | PUT result for TestHost1: success=True, message='None'
2025-08-04 14:41:58,742 | DEBUG | Processing hosts 2: TestHost2
2025-08-04 14:41:58,742 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:41:58,742 | DEBUG | Object data: {'name': 'TestHost2', 'type': 'Host', 'value': '************', 'description': 'Test host object 2', 'overridable': False}
2025-08-04 14:42:02,785 | DEBUG | GET result for TestHost2: success=True, message='None'
2025-08-04 14:42:06,261 | DEBUG | PUT result for TestHost2: success=True, message='None'
2025-08-04 14:42:06,262 | DEBUG | Processing hosts 3: TestHost3
2025-08-04 14:42:06,262 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:42:06,262 | DEBUG | Object data: {'name': 'TestHost3', 'type': 'Host', 'value': '**********', 'description': 'Test host object 3', 'overridable': False}
2025-08-04 14:42:10,461 | DEBUG | GET result for TestHost3: success=True, message='None'
2025-08-04 14:42:13,856 | DEBUG | PUT result for TestHost3: success=True, message='None'
2025-08-04 14:42:13,858 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343708_phase1_hosts.json
2025-08-04 14:42:13,858 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 14:42:13,858 | INFO | No API delay settings found to optimize
2025-08-04 14:42:13,858 | DEBUG | Processing networks 1: TestNetwork1
2025-08-04 14:42:13,858 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:42:13,858 | DEBUG | Object data: {'name': 'TestNetwork1', 'type': 'Network', 'value': '*************/24', 'description': 'Test network object 1', 'overridable': False}
2025-08-04 14:42:18,693 | DEBUG | GET result for TestNetwork1: success=True, message='None'
2025-08-04 14:42:22,201 | DEBUG | PUT result for TestNetwork1: success=True, message='None'
2025-08-04 14:42:22,201 | DEBUG | Processing networks 2: TestNetwork2
2025-08-04 14:42:22,201 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:42:22,201 | DEBUG | Object data: {'name': 'TestNetwork2', 'type': 'Network', 'value': '*********/16', 'description': 'Test network object 2', 'overridable': False}
2025-08-04 14:42:26,914 | DEBUG | GET result for TestNetwork2: success=True, message='None'
2025-08-04 14:42:30,351 | DEBUG | PUT result for TestNetwork2: success=True, message='None'
2025-08-04 14:42:30,352 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343708_phase1_networks.json
2025-08-04 14:42:30,352 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:42:30,352 | INFO | No API delay settings found to optimize
2025-08-04 14:42:30,352 | DEBUG | Processing services 1: TestHTTP
2025-08-04 14:42:30,352 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:42:30,352 | DEBUG | Object data: {'name': 'TestHTTP', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '80', 'description': 'Test HTTP service'}
2025-08-04 14:42:33,699 | DEBUG | GET result for TestHTTP: success=True, message='None'
2025-08-04 14:42:36,669 | DEBUG | PUT result for TestHTTP: success=False, message='fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType'
2025-08-04 14:42:36,669 | ERROR | Update failed for TestHTTP: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:42:36,669 | DEBUG | Processing services 2: TestHTTPS
2025-08-04 14:42:36,670 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:42:36,670 | DEBUG | Object data: {'name': 'TestHTTPS', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '443', 'description': 'Test HTTPS service'}
2025-08-04 14:42:39,689 | DEBUG | GET result for TestHTTPS: success=True, message='None'
2025-08-04 14:42:42,558 | DEBUG | PUT result for TestHTTPS: success=False, message='fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType'
2025-08-04 14:42:42,558 | ERROR | Update failed for TestHTTPS: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:42:42,558 | DEBUG | Processing services 3: TestSSH
2025-08-04 14:42:42,558 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:42:42,558 | DEBUG | Object data: {'name': 'TestSSH', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '22', 'description': 'Test SSH service'}
2025-08-04 14:42:45,565 | DEBUG | GET result for TestSSH: success=True, message='None'
2025-08-04 14:42:48,535 | DEBUG | PUT result for TestSSH: success=False, message='fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType'
2025-08-04 14:42:48,535 | ERROR | Update failed for TestSSH: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:42:48,536 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343708_phase1_services.json
2025-08-04 14:42:48,536 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:42:48,536 | INFO | No API delay settings found to optimize
2025-08-04 14:42:48,537 | DEBUG | Processing object_groups 1: TestNetworkGroup
2025-08-04 14:42:48,537 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 14:42:48,537 | DEBUG | Object data: {'name': 'TestNetworkGroup', 'type': 'NetworkGroup', 'description': 'Test network group', 'objects': [{'name': 'TestNetwork1', 'type': 'NetworkObject'}, {'name': 'TestHost1', 'type': 'HostObject'}]}
2025-08-04 14:42:51,890 | DEBUG | GET result for TestNetworkGroup: success=False, message='Object 'TestNetworkGroup' not found in FMC (will be created)'
2025-08-04 14:43:03,852 | DEBUG | POST result for TestNetworkGroup: success=True, message='None'
2025-08-04 14:43:03,853 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343708_phase1_object_groups.json
2025-08-04 14:43:03,853 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:43:03,853 | INFO | No API delay settings found to optimize
2025-08-04 14:43:03,853 | DEBUG | Processing service_groups 1: TestWebServices
2025-08-04 14:43:03,854 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 14:43:03,854 | DEBUG | Object data: {'name': 'TestWebServices', 'type': 'PortObjectGroup', 'description': 'Test web services group', 'objects': [{'name': 'TestHTTP', 'type': 'ProtocolPortObject'}, {'name': 'TestHTTPS', 'type': 'ProtocolPortObject'}]}
2025-08-04 14:43:07,033 | DEBUG | GET result for TestWebServices: success=False, message='Object 'TestWebServices' not found in FMC (will be created)'
2025-08-04 14:43:16,439 | DEBUG | POST result for TestWebServices: success=True, message='None'
2025-08-04 14:43:16,439 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343708_phase1_service_groups.json
2025-08-04 14:43:16,439 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:43:16,440 | INFO | No API delay settings found to optimize
2025-08-04 14:43:16,440 | DEBUG | Processing access_rules 1: TestRule1
2025-08-04 14:43:16,440 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'description', 'sourceNetworks', 'destinationNetworks', 'destinationPorts']
2025-08-04 14:43:16,440 | DEBUG | Object data: {'name': 'TestRule1', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'description': 'Test access rule', 'sourceNetworks': {'objects': [{'name': 'TestHost1', 'type': 'Host'}]}, 'destinationNetworks': {'objects': [{'name': 'TestNetwork1', 'type': 'Network'}]}, 'destinationPorts': {'objects': [{'name': 'TestHTTP', 'type': 'ProtocolPortObject'}]}}
2025-08-04 14:43:21,237 | DEBUG | GET result for TestRule1: success=False, message='Object 'TestRule1' not found in FMC (will be created)'
2025-08-04 14:43:25,643 | DEBUG | POST result for TestRule1: success=False, message='fmcapi post() failed - no ID returned. Result: False'
2025-08-04 14:43:25,643 | ERROR | Creation failed for TestRule1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:43:25,645 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343708_phase1_access_rules.json
2025-08-04 14:43:25,645 | INFO | ================================================================================
2025-08-04 14:43:25,645 | INFO | MIGRATION SUMMARY
2025-08-04 14:43:25,645 | INFO | ================================================================================
2025-08-04 14:43:25,646 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 14:43:25,646 | INFO |    • Total Objects: 11
2025-08-04 14:43:25,647 | INFO |    • Created: 2
2025-08-04 14:43:25,647 | INFO |    • Updated: 5
2025-08-04 14:43:25,647 | INFO |    • Failed: 4
2025-08-04 14:43:25,647 | INFO |    • Skipped: 0
2025-08-04 14:43:25,647 | INFO |    • Success Rate: 63.6%
2025-08-04 14:43:25,648 | INFO | ================================================================================
2025-08-04 14:43:25,649 | INFO | [FILE] Summary saved: migration_summary_migration_1754343708.json
