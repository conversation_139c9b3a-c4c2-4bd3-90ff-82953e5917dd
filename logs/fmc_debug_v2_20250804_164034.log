2025-08-04 16:40:34,356 | INFO | ================================================================================
2025-08-04 16:40:34,356 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 16:40:34,356 | INFO | Session ID: migration_1754350834
2025-08-04 16:40:34,356 | INFO | Connection Type: fmcapi
2025-08-04 16:40:34,356 | INFO | 🔍 Connection Diagnostic:
2025-08-04 16:40:34,356 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 16:40:34,356 | INFO |    • fmcapi Available: True
2025-08-04 16:40:34,356 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 16:40:36,984 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 16:40:36,984 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 16:40:36,984 | INFO | 🔍 Connection diagnostic complete
2025-08-04 16:40:36,984 | INFO | ================================================================================
2025-08-04 16:40:36,984 | INFO | BULK_API_ENABLED: Processing 100 objects with bulk API
2025-08-04 16:40:36,984 | INFO | No API delay settings found to optimize
2025-08-04 16:40:36,984 | INFO | BULK_API_START: Beginning bulk operation for 100 hosts
2025-08-04 16:40:36,984 | DEBUG | Using fmcapi bulk_post for 100 objects
2025-08-04 16:40:37,603 | DEBUG | Bulk create failed with 400, processing 100 objects individually
2025-08-04 16:53:06,988 | INFO | BULK_API_COMPLETE: 100 objects in 750.00s (0.1 obj/s)
2025-08-04 16:53:06,993 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754350834_phase1_hosts.json
2025-08-04 16:53:06,993 | INFO | BULK_API_ENABLED: Processing 20 objects with bulk API
2025-08-04 16:53:06,993 | INFO | No API delay settings found to optimize
2025-08-04 16:53:06,993 | INFO | BULK_API_START: Beginning bulk operation for 20 networks
2025-08-04 16:53:06,993 | DEBUG | Using fmcapi bulk_post for 20 objects
2025-08-04 16:53:07,249 | DEBUG | Bulk create failed with 400, processing 20 objects individually
2025-08-04 16:55:50,840 | INFO | BULK_API_COMPLETE: 20 objects in 163.85s (0.1 obj/s)
2025-08-04 16:55:50,841 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754350834_phase1_networks.json
2025-08-04 16:55:50,841 | INFO | BULK_API_ENABLED: Processing 10 objects with bulk API
2025-08-04 16:55:50,841 | INFO | No API delay settings found to optimize
2025-08-04 16:55:50,842 | INFO | BULK_API_START: Beginning bulk operation for 10 services
2025-08-04 16:55:50,842 | DEBUG | Using fmcapi bulk_post for 10 objects
2025-08-04 16:55:51,091 | DEBUG | Bulk create failed with 400, processing 10 objects individually
2025-08-04 16:56:21,993 | INFO | BULK_API_COMPLETE: 10 objects in 31.15s (0.3 obj/s)
2025-08-04 16:56:21,994 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754350834_phase1_services.json
