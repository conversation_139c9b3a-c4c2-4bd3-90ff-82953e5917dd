2025-08-04 15:07:02,168 | INFO | ================================================================================
2025-08-04 15:07:02,168 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 15:07:02,168 | INFO | Session ID: migration_1754345222
2025-08-04 15:07:02,168 | INFO | Connection Type: fmcapi
2025-08-04 15:07:02,168 | INFO | 🔍 Connection Diagnostic:
2025-08-04 15:07:02,168 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 15:07:02,168 | INFO |    • fmcapi Available: True
2025-08-04 15:07:02,168 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 15:07:04,909 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 15:07:04,909 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 15:07:04,909 | INFO | 🔍 Connection diagnostic complete
2025-08-04 15:07:04,909 | INFO | ================================================================================
2025-08-04 15:07:04,909 | INFO | 🔍 Running pre-migration validation...
2025-08-04 15:07:08,804 | INFO | ================================================================================
2025-08-04 15:07:08,804 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 15:07:08,804 | INFO | ================================================================================
2025-08-04 15:07:08,804 | INFO | [OK] Overall Status: PASSED
2025-08-04 15:07:08,804 | INFO | [OK] Configuration: 17 objects found
2025-08-04 15:07:08,804 | INFO |    • host_objects: 5
2025-08-04 15:07:08,805 | INFO |    • network_objects: 3
2025-08-04 15:07:08,805 | INFO |    • service_objects: 4
2025-08-04 15:07:08,805 | INFO |    • object_groups: 2
2025-08-04 15:07:08,805 | INFO |    • service_groups: 1
2025-08-04 15:07:08,805 | INFO |    • access_rules: 2
2025-08-04 15:07:08,805 | INFO | [OK] Connection: fmcapi - API accessible
2025-08-04 15:07:08,805 | INFO | ================================================================================
2025-08-04 15:07:08,805 | INFO | BULK_API_ENABLED: Processing 5 objects with bulk API
2025-08-04 15:07:08,805 | INFO | No API delay settings found to optimize
2025-08-04 15:07:46,870 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345222_phase1_hosts.json
2025-08-04 15:07:46,870 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 15:07:46,871 | INFO | No API delay settings found to optimize
2025-08-04 15:08:11,594 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345222_phase1_networks.json
2025-08-04 15:08:11,594 | INFO | BULK_API_ENABLED: Processing 4 objects with bulk API
2025-08-04 15:08:11,594 | INFO | No API delay settings found to optimize
2025-08-04 15:08:38,419 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345222_phase1_services.json
2025-08-04 15:08:38,419 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:08:38,419 | INFO | No API delay settings found to optimize
2025-08-04 15:09:13,458 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345222_phase1_object_groups.json
2025-08-04 15:09:13,458 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 15:09:13,458 | INFO | No API delay settings found to optimize
2025-08-04 15:09:25,661 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345222_phase1_service_groups.json
2025-08-04 15:09:28,393 | INFO | Attempting to get or create Access Control Policy...
2025-08-04 15:09:30,082 | WARNING | No existing Access Control Policies found
2025-08-04 15:09:30,082 | INFO | Creating new Access Control Policy...
2025-08-04 15:09:30,983 | INFO | Successfully created new Access Control Policy: ASA Migration Access Control Policy (ID: 005056BF-7B88-0ed3-0000-017187313775)
2025-08-04 15:09:30,983 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:09:30,983 | INFO | No API delay settings found to optimize
2025-08-04 15:10:47,866 | ERROR | Creation failed for TestRule_AllowWeb: Failed to get or create Access Control Policy after multiple attempts
2025-08-04 15:12:03,349 | ERROR | Creation failed for TestRule_AllowDatabase: Failed to get or create Access Control Policy after multiple attempts
2025-08-04 15:12:03,350 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345222_phase1_access_rules.json
2025-08-04 15:12:03,350 | INFO | ================================================================================
2025-08-04 15:12:03,351 | INFO | MIGRATION SUMMARY
2025-08-04 15:12:03,351 | INFO | ================================================================================
2025-08-04 15:12:03,351 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 15:12:03,351 | INFO |    • Total Objects: 17
2025-08-04 15:12:03,351 | INFO |    • Created: 15
2025-08-04 15:12:03,351 | INFO |    • Updated: 0
2025-08-04 15:12:03,351 | INFO |    • Failed: 2
2025-08-04 15:12:03,351 | INFO |    • Skipped: 0
2025-08-04 15:12:03,351 | INFO |    • Success Rate: 88.2%
2025-08-04 15:12:03,351 | INFO | ================================================================================
2025-08-04 15:12:03,352 | INFO | [FILE] Summary saved: migration_summary_migration_1754345222.json
