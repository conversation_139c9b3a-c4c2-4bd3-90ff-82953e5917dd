2025-08-04 14:45:14,939 | INFO | ================================================================================
2025-08-04 14:45:14,940 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 14:45:14,940 | INFO | Session ID: migration_1754343914
2025-08-04 14:45:14,940 | INFO | Connection Type: fmcapi
2025-08-04 14:45:14,940 | INFO | 🔍 Connection Diagnostic:
2025-08-04 14:45:14,940 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 14:45:14,940 | INFO |    • fmcapi Available: True
2025-08-04 14:45:14,940 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 14:45:17,603 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 14:45:17,603 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 14:45:17,603 | INFO | 🔍 Connection diagnostic complete
2025-08-04 14:45:17,603 | INFO | ================================================================================
2025-08-04 14:45:17,604 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:45:17,604 | INFO | No API delay settings found to optimize
2025-08-04 14:45:39,612 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343914_phase1_hosts.json
2025-08-04 14:45:39,612 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 14:45:39,612 | INFO | No API delay settings found to optimize
2025-08-04 14:45:55,366 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343914_phase1_networks.json
2025-08-04 14:45:55,366 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:45:55,366 | INFO | No API delay settings found to optimize
2025-08-04 14:46:04,109 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343914_phase1_services.json
2025-08-04 14:46:04,110 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:46:04,110 | INFO | No API delay settings found to optimize
2025-08-04 14:46:10,359 | ERROR | Update failed for TestNetworkGroup: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:46:10,360 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343914_phase1_object_groups.json
2025-08-04 14:46:10,360 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:46:10,360 | INFO | No API delay settings found to optimize
2025-08-04 14:46:15,952 | ERROR | Update failed for TestWebServices: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:46:15,953 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343914_phase1_service_groups.json
2025-08-04 14:46:15,953 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:46:15,953 | INFO | No API delay settings found to optimize
2025-08-04 14:46:24,469 | ERROR | Creation failed for TestRule1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:24,470 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343914_phase1_access_rules.json
2025-08-04 14:46:24,470 | INFO | ================================================================================
2025-08-04 14:46:24,470 | INFO | MIGRATION SUMMARY
2025-08-04 14:46:24,470 | INFO | ================================================================================
2025-08-04 14:46:24,470 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 14:46:24,470 | INFO |    • Total Objects: 11
2025-08-04 14:46:24,470 | INFO |    • Created: 0
2025-08-04 14:46:24,470 | INFO |    • Updated: 8
2025-08-04 14:46:24,470 | INFO |    • Failed: 3
2025-08-04 14:46:24,470 | INFO |    • Skipped: 0
2025-08-04 14:46:24,470 | INFO |    • Success Rate: 72.7%
2025-08-04 14:46:24,470 | INFO | ================================================================================
2025-08-04 14:46:24,471 | INFO | [FILE] Summary saved: migration_summary_migration_1754343914.json
