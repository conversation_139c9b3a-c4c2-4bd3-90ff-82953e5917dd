2025-08-04 15:14:58,764 | INFO | ================================================================================
2025-08-04 15:14:58,764 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 15:14:58,764 | INFO | Session ID: migration_1754345698
2025-08-04 15:14:58,764 | INFO | Connection Type: fmcapi
2025-08-04 15:14:58,764 | INFO | 🔍 Connection Diagnostic:
2025-08-04 15:14:58,764 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 15:14:58,764 | INFO |    • fmcapi Available: True
2025-08-04 15:14:58,764 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 15:15:01,408 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 15:15:01,409 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 15:15:01,409 | INFO | 🔍 Connection diagnostic complete
2025-08-04 15:15:01,409 | INFO | ================================================================================
2025-08-04 15:15:01,409 | INFO | 🔍 Running pre-migration validation...
2025-08-04 15:15:05,069 | INFO | ================================================================================
2025-08-04 15:15:05,070 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 15:15:05,070 | INFO | ================================================================================
2025-08-04 15:15:05,070 | INFO | [OK] Overall Status: PASSED
2025-08-04 15:15:05,070 | INFO | [OK] Configuration: 17 objects found
2025-08-04 15:15:05,070 | INFO |    • host_objects: 5
2025-08-04 15:15:05,070 | INFO |    • network_objects: 3
2025-08-04 15:15:05,070 | INFO |    • service_objects: 4
2025-08-04 15:15:05,070 | INFO |    • object_groups: 2
2025-08-04 15:15:05,070 | INFO |    • service_groups: 1
2025-08-04 15:15:05,070 | INFO |    • access_rules: 2
2025-08-04 15:15:05,070 | INFO | [OK] Connection: fmcapi - API accessible
2025-08-04 15:15:05,070 | INFO | ================================================================================
2025-08-04 15:15:05,070 | INFO | BULK_API_ENABLED: Processing 5 objects with bulk API
2025-08-04 15:15:05,070 | INFO | No API delay settings found to optimize
2025-08-04 15:15:41,729 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345698_phase1_hosts.json
2025-08-04 15:15:41,729 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 15:15:41,729 | INFO | No API delay settings found to optimize
2025-08-04 15:16:05,750 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345698_phase1_networks.json
2025-08-04 15:16:05,750 | INFO | BULK_API_ENABLED: Processing 4 objects with bulk API
2025-08-04 15:16:05,750 | INFO | No API delay settings found to optimize
2025-08-04 15:16:17,855 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345698_phase1_services.json
2025-08-04 15:16:17,856 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:16:17,856 | INFO | No API delay settings found to optimize
2025-08-04 15:16:24,491 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345698_phase1_object_groups.json
2025-08-04 15:16:24,491 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 15:16:24,491 | INFO | No API delay settings found to optimize
2025-08-04 15:16:27,486 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345698_phase1_service_groups.json
2025-08-04 15:16:30,203 | INFO | Attempting to get or create Access Control Policy...
2025-08-04 15:16:32,027 | WARNING | No existing Access Control Policies found
2025-08-04 15:16:32,027 | INFO | Creating new Access Control Policy...
2025-08-04 15:16:33,024 | INFO | Successfully created new Access Control Policy: ASA Migration ACP 1754345792 (ID: 005056BF-7B88-0ed3-0000-017187327177)
2025-08-04 15:16:33,024 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:16:33,024 | INFO | No API delay settings found to optimize
2025-08-04 15:17:11,775 | ERROR | Creation failed for TestRule_AllowWeb: Failed to create access rule: super(): no arguments
2025-08-04 15:17:50,439 | ERROR | Creation failed for TestRule_AllowDatabase: Failed to create access rule: super(): no arguments
2025-08-04 15:17:50,440 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345698_phase1_access_rules.json
2025-08-04 15:17:50,440 | INFO | ================================================================================
2025-08-04 15:17:50,440 | INFO | MIGRATION SUMMARY
2025-08-04 15:17:50,440 | INFO | ================================================================================
2025-08-04 15:17:50,440 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 15:17:50,441 | INFO |    • Total Objects: 17
2025-08-04 15:17:50,441 | INFO |    • Created: 0
2025-08-04 15:17:50,441 | INFO |    • Updated: 15
2025-08-04 15:17:50,441 | INFO |    • Failed: 2
2025-08-04 15:17:50,441 | INFO |    • Skipped: 0
2025-08-04 15:17:50,441 | INFO |    • Success Rate: 88.2%
2025-08-04 15:17:50,441 | INFO | ================================================================================
2025-08-04 15:17:50,441 | INFO | [FILE] Summary saved: migration_summary_migration_1754345698.json
