2025-08-04 15:52:53,610 | INFO | ================================================================================
2025-08-04 15:52:53,610 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 15:52:53,610 | INFO | Session ID: migration_1754347973
2025-08-04 15:52:53,610 | INFO | Connection Type: fmcapi
2025-08-04 15:52:53,610 | INFO | 🔍 Connection Diagnostic:
2025-08-04 15:52:53,610 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 15:52:53,610 | INFO |    • fmcapi Available: True
2025-08-04 15:52:53,610 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 15:52:56,224 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 15:52:56,224 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 15:52:56,224 | INFO | 🔍 Connection diagnostic complete
2025-08-04 15:52:56,224 | INFO | ================================================================================
2025-08-04 15:52:56,224 | INFO | BULK_API_ENABLED: Processing 100 objects with bulk API
2025-08-04 15:52:56,224 | INFO | No API delay settings found to optimize
2025-08-04 15:52:56,224 | INFO | BULK_API_START: Beginning bulk operation for 100 hosts
2025-08-04 16:05:33,565 | INFO | BULK_API_COMPLETE: 100 objects in 757.34s (0.1 obj/s)
2025-08-04 16:05:33,566 | ERROR | Bulk operation failed for NexTalkSec: Create failed: fmcapi post() failed - no ID returned.
2025-08-04 16:05:33,571 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754347973_phase1_hosts.json
2025-08-04 16:05:33,571 | INFO | BULK_API_ENABLED: Processing 20 objects with bulk API
2025-08-04 16:05:33,571 | INFO | No API delay settings found to optimize
2025-08-04 16:05:33,571 | INFO | BULK_API_START: Beginning bulk operation for 20 networks
2025-08-04 16:08:11,869 | INFO | BULK_API_COMPLETE: 20 objects in 158.30s (0.1 obj/s)
2025-08-04 16:08:11,871 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754347973_phase1_networks.json
2025-08-04 16:08:11,871 | INFO | BULK_API_ENABLED: Processing 10 objects with bulk API
2025-08-04 16:08:11,871 | INFO | No API delay settings found to optimize
2025-08-04 16:08:11,871 | INFO | BULK_API_START: Beginning bulk operation for 10 services
2025-08-04 16:09:57,279 | INFO | BULK_API_COMPLETE: 10 objects in 105.41s (0.1 obj/s)
2025-08-04 16:09:57,279 | ERROR | Bulk operation failed for obj-tcp-eq-80: Create failed: fmcapi post() failed - no ID returned.
2025-08-04 16:09:57,279 | ERROR | Bulk operation failed for obj-tcp-eq-23: Create failed: fmcapi post() failed - no ID returned.
2025-08-04 16:09:57,279 | ERROR | Bulk operation failed for obj-tcp-eq-5631: Create failed: fmcapi post() failed - no ID returned.
2025-08-04 16:09:57,280 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754347973_phase1_services.json
