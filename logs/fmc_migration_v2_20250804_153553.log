2025-08-04 15:35:53,521 | INFO | ================================================================================
2025-08-04 15:35:53,521 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 15:35:53,521 | INFO | Session ID: migration_1754346953
2025-08-04 15:35:53,521 | INFO | Connection Type: fmcapi
2025-08-04 15:35:53,521 | INFO | 🔍 Connection Diagnostic:
2025-08-04 15:35:53,521 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 15:35:53,521 | INFO |    • fmcapi Available: True
2025-08-04 15:35:53,521 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 15:35:56,171 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 15:35:56,172 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 15:35:56,172 | INFO | 🔍 Connection diagnostic complete
2025-08-04 15:35:56,172 | INFO | ================================================================================
2025-08-04 15:35:56,172 | INFO | BULK_API_ENABLED: Processing 10 objects with bulk API
2025-08-04 15:35:56,172 | INFO | No API delay settings found to optimize
2025-08-04 15:35:56,172 | INFO | BULK_API_START: Beginning bulk operation for 10 hosts
2025-08-04 15:37:11,460 | INFO | BULK_API_COMPLETE: 10 objects in 75.29s (0.1 obj/s)
2025-08-04 15:37:11,462 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754346953_phase1_hosts.json
