2025-08-04 14:55:05,319 | INFO | ================================================================================
2025-08-04 14:55:05,319 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 14:55:05,319 | INFO | Session ID: migration_1754344505
2025-08-04 14:55:05,319 | INFO | Connection Type: fmcapi
2025-08-04 14:55:05,319 | INFO | 🔍 Connection Diagnostic:
2025-08-04 14:55:05,320 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 14:55:05,320 | INFO |    • fmcapi Available: True
2025-08-04 14:55:05,320 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 14:55:08,098 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 14:55:08,098 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 14:55:08,098 | INFO | 🔍 Connection diagnostic complete
2025-08-04 14:55:08,098 | INFO | ================================================================================
2025-08-04 14:55:08,099 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:55:08,099 | INFO | No API delay settings found to optimize
2025-08-04 14:55:31,300 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344505_phase1_hosts.json
2025-08-04 14:55:31,300 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 14:55:31,301 | INFO | No API delay settings found to optimize
2025-08-04 14:55:48,416 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344505_phase1_networks.json
2025-08-04 14:55:48,416 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:55:48,416 | INFO | No API delay settings found to optimize
2025-08-04 14:55:57,986 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344505_phase1_services.json
2025-08-04 14:55:57,986 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:55:57,986 | INFO | No API delay settings found to optimize
2025-08-04 14:56:01,321 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344505_phase1_object_groups.json
2025-08-04 14:56:01,322 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:56:01,322 | INFO | No API delay settings found to optimize
2025-08-04 14:56:04,445 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344505_phase1_service_groups.json
2025-08-04 14:56:10,021 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:56:10,021 | INFO | No API delay settings found to optimize
2025-08-04 14:56:19,515 | ERROR | Creation failed for TestRule1: No Access Control Policy found - cannot create access rules
2025-08-04 14:56:19,516 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754344505_phase1_access_rules.json
2025-08-04 14:56:19,517 | INFO | ================================================================================
2025-08-04 14:56:19,517 | INFO | MIGRATION SUMMARY
2025-08-04 14:56:19,517 | INFO | ================================================================================
2025-08-04 14:56:19,517 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 14:56:19,517 | INFO |    • Total Objects: 11
2025-08-04 14:56:19,517 | INFO |    • Created: 0
2025-08-04 14:56:19,517 | INFO |    • Updated: 10
2025-08-04 14:56:19,517 | INFO |    • Failed: 1
2025-08-04 14:56:19,517 | INFO |    • Skipped: 0
2025-08-04 14:56:19,517 | INFO |    • Success Rate: 90.9%
2025-08-04 14:56:19,517 | INFO | ================================================================================
2025-08-04 14:56:19,518 | INFO | [FILE] Summary saved: migration_summary_migration_1754344505.json
