2025-08-04 12:09:17,856 | INFO | ================================================================================
2025-08-04 12:09:17,856 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 12:09:17,856 | INFO | Session ID: migration_1754334557
2025-08-04 12:09:17,856 | INFO | Connection Type: fmcapi
2025-08-04 12:09:17,856 | INFO | 🔍 Connection Diagnostic:
2025-08-04 12:09:17,856 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 12:09:17,856 | INFO |    • fmcapi Available: True
2025-08-04 12:09:17,856 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 12:09:17,929 | WARNING |    • fmcapi object creation failed: HTTPSConnectionPool(host='test.example.com', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x105c7f8c0>: Failed to resolve 'test.example.com' ([Errno 8] nodename nor servname provided, or not known)"))
2025-08-04 12:09:17,929 | INFO | 🔍 Connection diagnostic complete
2025-08-04 12:09:17,930 | INFO | ================================================================================
