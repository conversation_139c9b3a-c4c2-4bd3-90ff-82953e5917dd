2025-08-04 14:45:14,939 | INFO | ================================================================================
2025-08-04 14:45:14,940 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 14:45:14,940 | INFO | Session ID: migration_1754343914
2025-08-04 14:45:14,940 | INFO | Connection Type: fmcapi
2025-08-04 14:45:14,940 | INFO | 🔍 Connection Diagnostic:
2025-08-04 14:45:14,940 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 14:45:14,940 | INFO |    • fmcapi Available: True
2025-08-04 14:45:14,940 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 14:45:17,603 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 14:45:17,603 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 14:45:17,603 | INFO | 🔍 Connection diagnostic complete
2025-08-04 14:45:17,603 | INFO | ================================================================================
2025-08-04 14:45:17,604 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:45:17,604 | INFO | No API delay settings found to optimize
2025-08-04 14:45:17,604 | DEBUG | Processing hosts 1: TestHost1
2025-08-04 14:45:17,604 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:45:17,604 | DEBUG | Object data: {'name': 'TestHost1', 'type': 'Host', 'value': '************', 'description': 'Test host object 1', 'overridable': False}
2025-08-04 14:45:21,420 | DEBUG | GET result for TestHost1: success=True, message='None'
2025-08-04 14:45:25,054 | DEBUG | PUT result for TestHost1: success=True, message='None'
2025-08-04 14:45:25,054 | DEBUG | Processing hosts 2: TestHost2
2025-08-04 14:45:25,054 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:45:25,054 | DEBUG | Object data: {'name': 'TestHost2', 'type': 'Host', 'value': '************', 'description': 'Test host object 2', 'overridable': False}
2025-08-04 14:45:29,008 | DEBUG | GET result for TestHost2: success=True, message='None'
2025-08-04 14:45:32,291 | DEBUG | PUT result for TestHost2: success=True, message='None'
2025-08-04 14:45:32,292 | DEBUG | Processing hosts 3: TestHost3
2025-08-04 14:45:32,292 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:45:32,292 | DEBUG | Object data: {'name': 'TestHost3', 'type': 'Host', 'value': '**********', 'description': 'Test host object 3', 'overridable': False}
2025-08-04 14:45:36,397 | DEBUG | GET result for TestHost3: success=True, message='None'
2025-08-04 14:45:39,611 | DEBUG | PUT result for TestHost3: success=True, message='None'
2025-08-04 14:45:39,612 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343914_phase1_hosts.json
2025-08-04 14:45:39,612 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 14:45:39,612 | INFO | No API delay settings found to optimize
2025-08-04 14:45:39,612 | DEBUG | Processing networks 1: TestNetwork1
2025-08-04 14:45:39,612 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:45:39,612 | DEBUG | Object data: {'name': 'TestNetwork1', 'type': 'Network', 'value': '*************/24', 'description': 'Test network object 1', 'overridable': False}
2025-08-04 14:45:44,134 | DEBUG | GET result for TestNetwork1: success=True, message='None'
2025-08-04 14:45:47,580 | DEBUG | PUT result for TestNetwork1: success=True, message='None'
2025-08-04 14:45:47,581 | DEBUG | Processing networks 2: TestNetwork2
2025-08-04 14:45:47,581 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 14:45:47,581 | DEBUG | Object data: {'name': 'TestNetwork2', 'type': 'Network', 'value': '*********/16', 'description': 'Test network object 2', 'overridable': False}
2025-08-04 14:45:52,188 | DEBUG | GET result for TestNetwork2: success=True, message='None'
2025-08-04 14:45:55,365 | DEBUG | PUT result for TestNetwork2: success=True, message='None'
2025-08-04 14:45:55,366 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343914_phase1_networks.json
2025-08-04 14:45:55,366 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 14:45:55,366 | INFO | No API delay settings found to optimize
2025-08-04 14:45:55,366 | DEBUG | Processing services 1: TestHTTP
2025-08-04 14:45:55,367 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:45:55,367 | DEBUG | Object data: {'name': 'TestHTTP', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '80', 'description': 'Test HTTP service'}
2025-08-04 14:45:58,384 | DEBUG | GET result for TestHTTP: success=True, message='None'
2025-08-04 14:45:58,384 | DEBUG | PUT result for TestHTTP: success=True, message='Protocol port object updates skipped (objects are immutable)'
2025-08-04 14:45:58,384 | DEBUG | Processing services 2: TestHTTPS
2025-08-04 14:45:58,384 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:45:58,384 | DEBUG | Object data: {'name': 'TestHTTPS', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '443', 'description': 'Test HTTPS service'}
2025-08-04 14:46:01,247 | DEBUG | GET result for TestHTTPS: success=True, message='None'
2025-08-04 14:46:01,247 | DEBUG | PUT result for TestHTTPS: success=True, message='Protocol port object updates skipped (objects are immutable)'
2025-08-04 14:46:01,248 | DEBUG | Processing services 3: TestSSH
2025-08-04 14:46:01,248 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 14:46:01,248 | DEBUG | Object data: {'name': 'TestSSH', 'type': 'TCPPortObject', 'protocol': 'TCP', 'port': '22', 'description': 'Test SSH service'}
2025-08-04 14:46:04,109 | DEBUG | GET result for TestSSH: success=True, message='None'
2025-08-04 14:46:04,109 | DEBUG | PUT result for TestSSH: success=True, message='Protocol port object updates skipped (objects are immutable)'
2025-08-04 14:46:04,109 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343914_phase1_services.json
2025-08-04 14:46:04,110 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:46:04,110 | INFO | No API delay settings found to optimize
2025-08-04 14:46:04,110 | DEBUG | Processing object_groups 1: TestNetworkGroup
2025-08-04 14:46:04,110 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 14:46:04,110 | DEBUG | Object data: {'name': 'TestNetworkGroup', 'type': 'NetworkGroup', 'description': 'Test network group', 'objects': [{'name': 'TestNetwork1', 'type': 'NetworkObject'}, {'name': 'TestHost1', 'type': 'HostObject'}]}
2025-08-04 14:46:07,322 | DEBUG | GET result for TestNetworkGroup: success=True, message='None'
2025-08-04 14:46:10,359 | DEBUG | PUT result for TestNetworkGroup: success=False, message='fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType'
2025-08-04 14:46:10,359 | ERROR | Update failed for TestNetworkGroup: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:46:10,360 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343914_phase1_object_groups.json
2025-08-04 14:46:10,360 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:46:10,360 | INFO | No API delay settings found to optimize
2025-08-04 14:46:10,361 | DEBUG | Processing service_groups 1: TestWebServices
2025-08-04 14:46:10,361 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 14:46:10,361 | DEBUG | Object data: {'name': 'TestWebServices', 'type': 'PortObjectGroup', 'description': 'Test web services group', 'objects': [{'name': 'TestHTTP', 'type': 'ProtocolPortObject'}, {'name': 'TestHTTPS', 'type': 'ProtocolPortObject'}]}
2025-08-04 14:46:13,199 | DEBUG | GET result for TestWebServices: success=True, message='None'
2025-08-04 14:46:15,952 | DEBUG | PUT result for TestWebServices: success=False, message='fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType'
2025-08-04 14:46:15,952 | ERROR | Update failed for TestWebServices: fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType
2025-08-04 14:46:15,953 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343914_phase1_service_groups.json
2025-08-04 14:46:15,953 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 14:46:15,953 | INFO | No API delay settings found to optimize
2025-08-04 14:46:15,953 | DEBUG | Processing access_rules 1: TestRule1
2025-08-04 14:46:15,953 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'description', 'sourceNetworks', 'destinationNetworks', 'destinationPorts']
2025-08-04 14:46:15,953 | DEBUG | Object data: {'name': 'TestRule1', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'description': 'Test access rule', 'sourceNetworks': {'objects': [{'name': 'TestHost1', 'type': 'Host'}]}, 'destinationNetworks': {'objects': [{'name': 'TestNetwork1', 'type': 'Network'}]}, 'destinationPorts': {'objects': [{'name': 'TestHTTP', 'type': 'ProtocolPortObject'}]}}
2025-08-04 14:46:20,239 | DEBUG | GET result for TestRule1: success=False, message='Object 'TestRule1' not found in FMC (will be created)'
2025-08-04 14:46:24,469 | DEBUG | POST result for TestRule1: success=False, message='fmcapi post() failed - no ID returned. Result: False'
2025-08-04 14:46:24,469 | ERROR | Creation failed for TestRule1: fmcapi post() failed - no ID returned. Result: False
2025-08-04 14:46:24,470 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754343914_phase1_access_rules.json
2025-08-04 14:46:24,470 | INFO | ================================================================================
2025-08-04 14:46:24,470 | INFO | MIGRATION SUMMARY
2025-08-04 14:46:24,470 | INFO | ================================================================================
2025-08-04 14:46:24,470 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 14:46:24,470 | INFO |    • Total Objects: 11
2025-08-04 14:46:24,470 | INFO |    • Created: 0
2025-08-04 14:46:24,470 | INFO |    • Updated: 8
2025-08-04 14:46:24,470 | INFO |    • Failed: 3
2025-08-04 14:46:24,470 | INFO |    • Skipped: 0
2025-08-04 14:46:24,470 | INFO |    • Success Rate: 72.7%
2025-08-04 14:46:24,470 | INFO | ================================================================================
2025-08-04 14:46:24,471 | INFO | [FILE] Summary saved: migration_summary_migration_1754343914.json
