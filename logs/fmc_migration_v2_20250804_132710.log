2025-08-04 13:27:10,481 | INFO | ================================================================================
2025-08-04 13:27:10,481 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 13:27:10,481 | INFO | Session ID: migration_1754339230
2025-08-04 13:27:10,481 | INFO | Connection Type: fmcapi
2025-08-04 13:27:10,481 | INFO | 🔍 Connection Diagnostic:
2025-08-04 13:27:10,481 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 13:27:10,481 | INFO |    • fmcapi Available: True
2025-08-04 13:27:10,481 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 13:27:15,486 | WARNING |    • fmcapi object creation failed: HTTPSConnectionPool(host='*************', port=443): Max retries exceeded with url: /api/fmc_platform/v1/auth/generatetoken (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x106befa10>, 'Connection to ************* timed out. (connect timeout=5)'))
2025-08-04 13:27:15,486 | INFO | 🔍 Connection diagnostic complete
2025-08-04 13:27:15,486 | INFO | ================================================================================
2025-08-04 13:27:15,486 | INFO | 🔍 Running pre-migration validation...
2025-08-04 13:27:20,491 | INFO | ================================================================================
2025-08-04 13:27:20,491 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 13:27:20,491 | INFO | ================================================================================
2025-08-04 13:27:20,491 | INFO | [OK] Overall Status: PASSED
2025-08-04 13:27:20,491 | INFO | [OK] Configuration: 1122 objects found
2025-08-04 13:27:20,491 | INFO |    • host_objects: 629
2025-08-04 13:27:20,491 | INFO |    • network_objects: 63
2025-08-04 13:27:20,491 | INFO |    • service_objects: 29
2025-08-04 13:27:20,491 | INFO |    • object_groups: 111
2025-08-04 13:27:20,491 | INFO |    • service_groups: 66
2025-08-04 13:27:20,491 | INFO |    • access_rules: 224
2025-08-04 13:27:20,491 | WARNING | [WARN]  Connection: fmcapi - API test inconclusive
2025-08-04 13:27:20,492 | WARNING | [WARN]  Warnings (1):
2025-08-04 13:27:20,492 | WARNING |    • FMC API connection test inconclusive
2025-08-04 13:27:20,492 | INFO | ================================================================================
2025-08-04 13:27:20,499 | INFO | BULK_API_ENABLED: Processing 629 objects with bulk API
2025-08-04 13:27:20,499 | INFO | No API delay settings found to optimize
2025-08-04 13:27:20,499 | INFO | BULK_API_START: Beginning bulk operation for 629 hosts
2025-08-04 13:27:20,500 | INFO | BULK_API_COMPLETE: 629 objects in 0.00s (638484.3 obj/s)
2025-08-04 13:27:20,500 | ERROR | Bulk operation failed for RadSaratoga: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,500 | ERROR | Bulk operation failed for RadAmsMem: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,500 | ERROR | Bulk operation failed for RadStMarys: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,500 | ERROR | Bulk operation failed for RadSeton: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,500 | ERROR | Bulk operation failed for RadBellevue: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,507 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339230_phase1_hosts.json
2025-08-04 13:27:20,507 | INFO | BULK_API_ENABLED: Processing 63 objects with bulk API
2025-08-04 13:27:20,507 | INFO | No API delay settings found to optimize
2025-08-04 13:27:20,507 | INFO | BULK_API_START: Beginning bulk operation for 63 networks
2025-08-04 13:27:20,507 | INFO | BULK_API_COMPLETE: 63 objects in 0.00s (599186.3 obj/s)
2025-08-04 13:27:20,508 | ERROR | Bulk operation failed for TeleMedVT3: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,508 | ERROR | Bulk operation failed for TelemedVT4: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,508 | ERROR | Bulk operation failed for TelemedVT5: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,508 | ERROR | Bulk operation failed for TeleMedVT1: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,508 | ERROR | Bulk operation failed for Medent.VPN.net: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,509 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339230_phase1_networks.json
2025-08-04 13:27:20,509 | INFO | BULK_API_ENABLED: Processing 29 objects with bulk API
2025-08-04 13:27:20,509 | INFO | No API delay settings found to optimize
2025-08-04 13:27:20,509 | INFO | BULK_API_START: Beginning bulk operation for 29 services
2025-08-04 13:27:20,509 | INFO | BULK_API_COMPLETE: 29 objects in 0.00s (545447.6 obj/s)
2025-08-04 13:27:20,509 | ERROR | Bulk operation failed for obj-tcp-eq-80: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,509 | ERROR | Bulk operation failed for obj-tcp-eq-15002: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,509 | ERROR | Bulk operation failed for obj-tcp-eq-15331: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,509 | ERROR | Bulk operation failed for obj-tcp-eq-3389: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,509 | ERROR | Bulk operation failed for obj-tcp-eq-2222: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,509 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339230_phase1_services.json
2025-08-04 13:27:20,509 | INFO | BULK_API_ENABLED: Processing 111 objects with bulk API
2025-08-04 13:27:20,509 | INFO | No API delay settings found to optimize
2025-08-04 13:27:20,510 | INFO | BULK_API_START: Beginning bulk operation for 111 object_groups
2025-08-04 13:27:20,510 | INFO | BULK_API_COMPLETE: 111 objects in 0.00s (706476.1 obj/s)
2025-08-04 13:27:20,510 | ERROR | Bulk operation failed for Medivators: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,510 | ERROR | Bulk operation failed for NUVODIA.INTERNAL.GROUP.NEW: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,510 | ERROR | Bulk operation failed for NUVODIA.INTERNAL.PEER.NET1: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,510 | ERROR | Bulk operation failed for DM_INLINE_NETWORK_4: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,510 | ERROR | Bulk operation failed for DM_INLINE_NETWORK_6: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,511 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339230_phase1_object_groups.json
2025-08-04 13:27:20,511 | INFO | BULK_API_ENABLED: Processing 66 objects with bulk API
2025-08-04 13:27:20,511 | INFO | No API delay settings found to optimize
2025-08-04 13:27:20,511 | INFO | BULK_API_START: Beginning bulk operation for 66 service_groups
2025-08-04 13:27:20,511 | INFO | BULK_API_COMPLETE: 66 objects in 0.00s (659104.9 obj/s)
2025-08-04 13:27:20,511 | ERROR | Bulk operation failed for PaceGlobalgrp: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,511 | ERROR | Bulk operation failed for timeservice: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,511 | ERROR | Bulk operation failed for timeserviceUDP: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,511 | ERROR | Bulk operation failed for QUEST: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,511 | ERROR | Bulk operation failed for citrixXML: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,512 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339230_phase1_service_groups.json
2025-08-04 13:27:20,513 | INFO | BULK_API_ENABLED: Processing 224 objects with bulk API
2025-08-04 13:27:20,513 | INFO | No API delay settings found to optimize
2025-08-04 13:27:20,513 | INFO | BULK_API_START: Beginning bulk operation for 224 access_rules
2025-08-04 13:27:20,513 | INFO | BULK_API_COMPLETE: 224 objects in 0.00s (641751.4 obj/s)
2025-08-04 13:27:20,513 | ERROR | Bulk operation failed for inside_access_in_rule_1: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,513 | ERROR | Bulk operation failed for inside_access_in_rule_2: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,513 | ERROR | Bulk operation failed for inside_access_in_rule_3: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,513 | ERROR | Bulk operation failed for inside_access_in_rule_4: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,513 | ERROR | Bulk operation failed for inside_access_in_rule_5: Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'
2025-08-04 13:27:20,515 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754339230_phase1_access_rules.json
2025-08-04 13:27:20,515 | INFO | ================================================================================
2025-08-04 13:27:20,515 | INFO | MIGRATION SUMMARY
2025-08-04 13:27:20,515 | INFO | ================================================================================
2025-08-04 13:27:20,515 | INFO | [INFO] OVERALL RESULTS:
2025-08-04 13:27:20,516 | INFO |    • Total Objects: 1122
2025-08-04 13:27:20,516 | INFO |    • Created: 0
2025-08-04 13:27:20,516 | INFO |    • Updated: 0
2025-08-04 13:27:20,516 | INFO |    • Failed: 1122
2025-08-04 13:27:20,516 | INFO |    • Skipped: 0
2025-08-04 13:27:20,516 | INFO |    • Success Rate: 0.0%
2025-08-04 13:27:20,516 | INFO | ================================================================================
2025-08-04 13:27:20,516 | INFO | [FILE] Summary saved: migration_summary_migration_1754339230.json
2025-08-04 13:27:20,516 | WARNING | [WARN]  Migration completed with 1122 failures
