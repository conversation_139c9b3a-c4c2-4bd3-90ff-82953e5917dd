2025-08-04 15:51:23,869 | INFO | ================================================================================
2025-08-04 15:51:23,869 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 15:51:23,869 | INFO | Session ID: migration_1754347883
2025-08-04 15:51:23,869 | INFO | Connection Type: fmcapi
2025-08-04 15:51:23,869 | INFO | 🔍 Connection Diagnostic:
2025-08-04 15:51:23,869 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 15:51:23,869 | INFO |    • fmcapi Available: True
2025-08-04 15:51:23,869 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 15:51:26,657 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 15:51:26,657 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 15:51:26,657 | INFO | 🔍 Connection diagnostic complete
2025-08-04 15:51:26,657 | INFO | ================================================================================
2025-08-04 15:51:26,657 | INFO | BULK_API_ENABLED: Processing 10 objects with bulk API
2025-08-04 15:51:26,657 | INFO | No API delay settings found to optimize
2025-08-04 15:51:26,657 | INFO | BULK_API_START: Beginning bulk operation for 10 hosts
2025-08-04 15:51:26,657 | DEBUG | Using fmcapi bulk_post for 10 objects
2025-08-04 15:51:26,916 | DEBUG | Bulk create failed with 400, processing 10 objects individually
2025-08-04 15:52:42,003 | INFO | BULK_API_COMPLETE: 10 objects in 75.35s (0.1 obj/s)
2025-08-04 15:52:42,005 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754347883_phase1_hosts.json
