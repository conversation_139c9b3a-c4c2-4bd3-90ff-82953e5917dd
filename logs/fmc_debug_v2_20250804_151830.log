2025-08-04 15:18:30,281 | INFO | ================================================================================
2025-08-04 15:18:30,281 | INFO | FMC Migration Engine v2.0 Started
2025-08-04 15:18:30,281 | INFO | Session ID: migration_1754345910
2025-08-04 15:18:30,281 | INFO | Connection Type: fmcapi
2025-08-04 15:18:30,281 | INFO | 🔍 Connection Diagnostic:
2025-08-04 15:18:30,281 | INFO |    • FMC Object Type: <class 'fmcapi.fmc.FMC'>
2025-08-04 15:18:30,281 | INFO |    • fmcapi Available: True
2025-08-04 15:18:30,281 | INFO |    • Available Methods: __enter__ (context manager)
2025-08-04 15:18:33,024 | INFO |    • fmcapi Hosts object created successfully
2025-08-04 15:18:33,024 | INFO |    • fmcapi Hosts methods: ['FILTER_BY_NAME', 'FIRST_SUPPORTED_FMC_VERSION', 'GLOBAL_VALID_FOR_KWARGS', 'REQUIRED_FOR_BULK_DELETE', 'REQUIRED_FOR_BULK_POST', 'REQUIRED_FOR_DELETE', 'REQUIRED_FOR_GET', 'REQUIRED_FOR_POST', 'REQUIRED_FOR_PUT', 'REQUIRED_GET_FILTERS']...
2025-08-04 15:18:33,024 | INFO | 🔍 Connection diagnostic complete
2025-08-04 15:18:33,024 | INFO | ================================================================================
2025-08-04 15:18:33,025 | INFO | 🔍 Running pre-migration validation...
2025-08-04 15:18:36,843 | INFO | ================================================================================
2025-08-04 15:18:36,843 | INFO | PRE-MIGRATION VALIDATION SUMMARY
2025-08-04 15:18:36,843 | INFO | ================================================================================
2025-08-04 15:18:36,843 | INFO | [OK] Overall Status: PASSED
2025-08-04 15:18:36,843 | INFO | [OK] Configuration: 17 objects found
2025-08-04 15:18:36,843 | INFO |    • host_objects: 5
2025-08-04 15:18:36,843 | INFO |    • network_objects: 3
2025-08-04 15:18:36,843 | INFO |    • service_objects: 4
2025-08-04 15:18:36,843 | INFO |    • object_groups: 2
2025-08-04 15:18:36,843 | INFO |    • service_groups: 1
2025-08-04 15:18:36,843 | INFO |    • access_rules: 2
2025-08-04 15:18:36,843 | INFO | [OK] Connection: fmcapi - API accessible
2025-08-04 15:18:36,843 | INFO | ================================================================================
2025-08-04 15:18:36,844 | INFO | BULK_API_ENABLED: Processing 5 objects with bulk API
2025-08-04 15:18:36,844 | INFO | No API delay settings found to optimize
2025-08-04 15:18:36,844 | DEBUG | Processing hosts 1: TestHost_WebServer
2025-08-04 15:18:36,844 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:18:36,844 | DEBUG | Object data: {'name': 'TestHost_WebServer', 'type': 'Host', 'value': '*************', 'description': 'Test web server host', 'overridable': False}
2025-08-04 15:18:40,745 | DEBUG | GET result for TestHost_WebServer: success=True, message='None'
2025-08-04 15:18:44,981 | DEBUG | PUT result for TestHost_WebServer: success=True, message='None'
2025-08-04 15:18:44,981 | DEBUG | Processing hosts 2: TestHost_DatabaseServer
2025-08-04 15:18:44,982 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:18:44,982 | DEBUG | Object data: {'name': 'TestHost_DatabaseServer', 'type': 'Host', 'value': '*************', 'description': 'Test database server host', 'overridable': False}
2025-08-04 15:18:49,217 | DEBUG | GET result for TestHost_DatabaseServer: success=True, message='None'
2025-08-04 15:18:53,107 | DEBUG | PUT result for TestHost_DatabaseServer: success=True, message='None'
2025-08-04 15:18:53,107 | DEBUG | Processing hosts 3: TestHost_FileServer
2025-08-04 15:18:53,107 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:18:53,108 | DEBUG | Object data: {'name': 'TestHost_FileServer', 'type': 'Host', 'value': '*************', 'description': 'Test file server host', 'overridable': False}
2025-08-04 15:18:57,269 | DEBUG | GET result for TestHost_FileServer: success=True, message='None'
2025-08-04 15:19:01,012 | DEBUG | PUT result for TestHost_FileServer: success=True, message='None'
2025-08-04 15:19:05,306 | DEBUG | GET result for TestHost_PrintServer: success=True, message='None'
2025-08-04 15:19:08,781 | DEBUG | PUT result for TestHost_PrintServer: success=True, message='None'
2025-08-04 15:19:12,871 | DEBUG | GET result for TestHost_BackupServer: success=True, message='None'
2025-08-04 15:19:16,312 | DEBUG | PUT result for TestHost_BackupServer: success=True, message='None'
2025-08-04 15:19:16,313 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345910_phase1_hosts.json
2025-08-04 15:19:16,313 | INFO | BULK_API_ENABLED: Processing 3 objects with bulk API
2025-08-04 15:19:16,313 | INFO | No API delay settings found to optimize
2025-08-04 15:19:16,313 | DEBUG | Processing networks 1: TestNetwork_LAN
2025-08-04 15:19:16,313 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:19:16,313 | DEBUG | Object data: {'name': 'TestNetwork_LAN', 'type': 'Network', 'value': '***********/24', 'description': 'Test LAN network', 'overridable': False}
2025-08-04 15:19:21,074 | DEBUG | GET result for TestNetwork_LAN: success=True, message='None'
2025-08-04 15:19:24,757 | DEBUG | PUT result for TestNetwork_LAN: success=True, message='None'
2025-08-04 15:19:24,757 | DEBUG | Processing networks 2: TestNetwork_DMZ
2025-08-04 15:19:24,757 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:19:24,757 | DEBUG | Object data: {'name': 'TestNetwork_DMZ', 'type': 'Network', 'value': '********/24', 'description': 'Test DMZ network', 'overridable': False}
2025-08-04 15:19:29,233 | DEBUG | GET result for TestNetwork_DMZ: success=True, message='None'
2025-08-04 15:19:32,966 | DEBUG | PUT result for TestNetwork_DMZ: success=True, message='None'
2025-08-04 15:19:32,966 | DEBUG | Processing networks 3: TestNetwork_Management
2025-08-04 15:19:32,966 | DEBUG | Object data keys: ['name', 'type', 'value', 'description', 'overridable']
2025-08-04 15:19:32,966 | DEBUG | Object data: {'name': 'TestNetwork_Management', 'type': 'Network', 'value': '**********/24', 'description': 'Test management network', 'overridable': False}
2025-08-04 15:19:37,571 | DEBUG | GET result for TestNetwork_Management: success=True, message='None'
2025-08-04 15:19:40,795 | DEBUG | PUT result for TestNetwork_Management: success=True, message='None'
2025-08-04 15:19:40,795 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345910_phase1_networks.json
2025-08-04 15:19:40,795 | INFO | BULK_API_ENABLED: Processing 4 objects with bulk API
2025-08-04 15:19:40,795 | INFO | No API delay settings found to optimize
2025-08-04 15:19:40,795 | DEBUG | Processing services 1: TestService_HTTP_8080
2025-08-04 15:19:40,795 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 15:19:40,795 | DEBUG | Object data: {'name': 'TestService_HTTP_8080', 'type': 'ProtocolPortObject', 'protocol': 'TCP', 'port': '8080', 'description': 'Test HTTP service on port 8080'}
2025-08-04 15:19:43,801 | DEBUG | GET result for TestService_HTTP_8080: success=True, message='None'
2025-08-04 15:19:43,802 | DEBUG | PUT result for TestService_HTTP_8080: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 15:19:43,802 | DEBUG | Processing services 2: TestService_HTTPS_8443
2025-08-04 15:19:43,802 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 15:19:43,802 | DEBUG | Object data: {'name': 'TestService_HTTPS_8443', 'type': 'ProtocolPortObject', 'protocol': 'TCP', 'port': '8443', 'description': 'Test HTTPS service on port 8443'}
2025-08-04 15:19:46,609 | DEBUG | GET result for TestService_HTTPS_8443: success=True, message='None'
2025-08-04 15:19:46,609 | DEBUG | PUT result for TestService_HTTPS_8443: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 15:19:46,609 | DEBUG | Processing services 3: TestService_Database
2025-08-04 15:19:46,609 | DEBUG | Object data keys: ['name', 'type', 'protocol', 'port', 'description']
2025-08-04 15:19:46,609 | DEBUG | Object data: {'name': 'TestService_Database', 'type': 'ProtocolPortObject', 'protocol': 'TCP', 'port': '3306', 'description': 'Test MySQL database service'}
2025-08-04 15:19:49,429 | DEBUG | GET result for TestService_Database: success=True, message='None'
2025-08-04 15:19:49,429 | DEBUG | PUT result for TestService_Database: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 15:19:52,322 | DEBUG | GET result for TestService_FTP: success=True, message='None'
2025-08-04 15:19:52,322 | DEBUG | PUT result for TestService_FTP: success=True, message='Protocol port object exists and is correct (no update needed)'
2025-08-04 15:19:52,323 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345910_phase1_services.json
2025-08-04 15:19:52,323 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:19:52,323 | INFO | No API delay settings found to optimize
2025-08-04 15:19:52,324 | DEBUG | Processing object_groups 1: TestGroup_Servers
2025-08-04 15:19:52,324 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 15:19:52,324 | DEBUG | Object data: {'name': 'TestGroup_Servers', 'type': 'NetworkGroup', 'description': 'Test server group', 'objects': [{'name': 'TestHost_WebServer', 'type': 'Host'}, {'name': 'TestHost_DatabaseServer', 'type': 'Host'}, {'name': 'TestHost_FileServer', 'type': 'Host'}]}
2025-08-04 15:19:55,487 | DEBUG | GET result for TestGroup_Servers: success=True, message='None'
2025-08-04 15:19:55,487 | DEBUG | PUT result for TestGroup_Servers: success=True, message='Network group exists and is correct (no update needed)'
2025-08-04 15:19:55,487 | DEBUG | Processing object_groups 2: TestGroup_Networks
2025-08-04 15:19:55,487 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 15:19:55,487 | DEBUG | Object data: {'name': 'TestGroup_Networks', 'type': 'NetworkGroup', 'description': 'Test network group', 'objects': [{'name': 'TestNetwork_LAN', 'type': 'Network'}, {'name': 'TestNetwork_DMZ', 'type': 'Network'}]}
2025-08-04 15:19:58,593 | DEBUG | GET result for TestGroup_Networks: success=True, message='None'
2025-08-04 15:19:58,593 | DEBUG | PUT result for TestGroup_Networks: success=True, message='Network group exists and is correct (no update needed)'
2025-08-04 15:19:58,594 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345910_phase1_object_groups.json
2025-08-04 15:19:58,595 | INFO | BULK_API_ENABLED: Processing 1 objects with bulk API
2025-08-04 15:19:58,595 | INFO | No API delay settings found to optimize
2025-08-04 15:19:58,595 | DEBUG | Processing service_groups 1: TestGroup_WebServices
2025-08-04 15:19:58,595 | DEBUG | Object data keys: ['name', 'type', 'description', 'objects']
2025-08-04 15:19:58,595 | DEBUG | Object data: {'name': 'TestGroup_WebServices', 'type': 'PortObjectGroup', 'description': 'Test web services group', 'objects': [{'name': 'TestService_HTTP_8080', 'type': 'ProtocolPortObject'}, {'name': 'TestService_HTTPS_8443', 'type': 'ProtocolPortObject'}]}
2025-08-04 15:20:01,438 | DEBUG | GET result for TestGroup_WebServices: success=True, message='None'
2025-08-04 15:20:01,438 | DEBUG | PUT result for TestGroup_WebServices: success=True, message='Port object group exists and is correct (no update needed)'
2025-08-04 15:20:01,440 | INFO | [FILE] Enhanced checkpoint saved: migration_checkpoints/migration_1754345910_phase1_service_groups.json
2025-08-04 15:20:04,014 | INFO | Attempting to get or create Access Control Policy...
2025-08-04 15:20:04,015 | DEBUG | Fetching existing Access Control Policies...
2025-08-04 15:20:06,203 | DEBUG | ACP object after get(): <class 'fmcapi.api_objects.policy_services.accesspolicies.AccessPolicies'>
2025-08-04 15:20:06,203 | WARNING | No existing Access Control Policies found
2025-08-04 15:20:06,203 | INFO | Creating new Access Control Policy...
2025-08-04 15:20:06,203 | DEBUG | Posting new Access Control Policy...
2025-08-04 15:20:07,098 | DEBUG | Post result: {'metadata': {'inherit': False, 'lockingStatus': {'status': 'UNLOCKED'}, 'domain': {'name': 'Global', 'id': 'e276abec-e0f2-11e3-8169-6d9ed49b625f', 'type': 'Domain'}}, 'type': 'AccessPolicy', 'links': {'self': 'https://fmcrestapisandbox.cisco.com/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/policy/accesspolicies/005056BF-7B88-0ed3-0000-017187332900'}, 'rules': {'refType': 'list', 'type': 'AccessRule', 'links': {'self': 'https://fmcrestapisandbox.cisco.com/api/fmc_config/v1/domain/e276abec-e0f2-11e3-8169-6d9ed49b625f/policy/accesspolicies/005056BF-7B88-0ed3-0000-017187332900/accessrules'}}, 'name': 'ASA Migration ACP 1754346006', 'description': 'Created automatically for ASA to FMC migration', 'id': '005056BF-7B88-0ed3-0000-017187332900'}
2025-08-04 15:20:07,098 | INFO | Successfully created new Access Control Policy: ASA Migration ACP 1754346006 (ID: 005056BF-7B88-0ed3-0000-017187332900)
2025-08-04 15:20:07,098 | INFO | BULK_API_ENABLED: Processing 2 objects with bulk API
2025-08-04 15:20:07,098 | INFO | No API delay settings found to optimize
2025-08-04 15:20:07,099 | DEBUG | Processing access_rules 1: TestRule_AllowWeb
2025-08-04 15:20:07,099 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'sourceNetworks', 'destinationNetworks', 'destinationPorts', 'logBegin', 'logEnd', 'acp_id']
2025-08-04 15:20:07,099 | DEBUG | Object data: {'name': 'TestRule_AllowWeb', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'sourceNetworks': [{'name': 'TestNetwork_LAN', 'type': 'Network'}], 'destinationNetworks': [{'name': 'TestHost_WebServer', 'type': 'Host'}], 'destinationPorts': [{'name': 'TestService_HTTP_8080', 'type': 'ProtocolPortObject'}], 'logBegin': False, 'logEnd': True, 'acp_id': '005056BF-7B88-0ed3-0000-017187332900'}
2025-08-04 15:20:12,466 | DEBUG | GET result for TestRule_AllowWeb: success=False, message='No Access Control Policy found - cannot manage access rules'
2025-08-04 15:21:53,908 | DEBUG | POST result for TestRule_AllowWeb: success=False, message='fmcapi post() failed - no ID returned.'
2025-08-04 15:21:53,908 | ERROR | Creation failed for TestRule_AllowWeb: fmcapi post() failed - no ID returned.
2025-08-04 15:21:53,908 | DEBUG | Processing access_rules 2: TestRule_AllowDatabase
2025-08-04 15:21:53,908 | DEBUG | Object data keys: ['name', 'type', 'action', 'enabled', 'sourceNetworks', 'destinationNetworks', 'destinationPorts', 'logBegin', 'logEnd', 'acp_id']
2025-08-04 15:21:53,908 | DEBUG | Object data: {'name': 'TestRule_AllowDatabase', 'type': 'AccessRule', 'action': 'ALLOW', 'enabled': True, 'sourceNetworks': [{'name': 'TestHost_WebServer', 'type': 'Host'}], 'destinationNetworks': [{'name': 'TestHost_DatabaseServer', 'type': 'Host'}], 'destinationPorts': [{'name': 'TestService_Database', 'type': 'ProtocolPortObject'}], 'logBegin': False, 'logEnd': True, 'acp_id': '005056BF-7B88-0ed3-0000-017187332900'}
2025-08-04 15:21:59,197 | DEBUG | GET result for TestRule_AllowDatabase: success=False, message='No Access Control Policy found - cannot manage access rules'
