{"session_id": "migration_1754347075", "timestamp": "2025-08-04T15:47:54.983564", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 100, "created": 59, "updated": 0, "failed": 41, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue", "❌ Failed host: XENAPP02 - Create failed: Data validation failed: No data provided", "❌ Failed host: NLHNAS11 - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGIC_A-iDRAC - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGIC_E-iDRAC - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGIC_D-NEWiSCSI - Create failed: Data validation failed: No data provided", "❌ Failed host: UNITY-SDC_iSCSI - Create failed: Data validation failed: No data provided", "❌ Failed host: NLH-WEB01-WS01 - <PERSON><PERSON> failed: Data validation failed: No data provided", "❌ Failed host: MAGICA - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGICC - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGICD - Create failed: Data validation failed: No data provided"], "duration_seconds": 597.1533288955688, "success_rate": 59.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 100, "created": 59, "updated": 0, "failed": 41, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue", "❌ Failed host: XENAPP02 - Create failed: Data validation failed: No data provided", "❌ Failed host: NLHNAS11 - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGIC_A-iDRAC - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGIC_E-iDRAC - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGIC_D-NEWiSCSI - Create failed: Data validation failed: No data provided", "❌ Failed host: UNITY-SDC_iSCSI - Create failed: Data validation failed: No data provided", "❌ Failed host: NLH-WEB01-WS01 - <PERSON><PERSON> failed: Data validation failed: No data provided", "❌ Failed host: MAGICA - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGICC - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGICD - Create failed: Data validation failed: No data provided"], "duration_seconds": 597.1533288955688, "success_rate": 59.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadSaratoga", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:38:10.602976", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadAmsMem", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:38:18.252397", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadStMarys", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:38:25.260389", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadSeton", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:38:32.367700", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadBellevue", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:38:39.337200", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "CITRIXFS02", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:38:46.224188", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "XENAPP30", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:38:53.155792", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC_C-iDRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:00.498131", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLHNAS12", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:07.540411", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC-D_iDRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:14.577670", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP02", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:18.610681", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHNAS11", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:22.603218", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_A-iDRAC", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:26.523504", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_E-iDRAC", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:30.370064", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_D-NEWiSCSI", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:34.365052", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "UNITY-SDC_iSCSI", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:38.313117", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-WEB01-WS01", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:42.157162", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICA", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:46.156527", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICC", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:50.027573", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICD", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:54.068715", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICE", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:39:58.067426", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICB", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:02.034509", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICF", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:05.897817", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICG", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:09.805357", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DICTATION02", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:13.718606", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDILIVE.NLH.ORG", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:17.640047", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_FS_SEC", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:21.491671", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CTScanner", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:25.438421", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON><PERSON><PERSON>", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:29.289856", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RandF", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:33.179205", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PetLinks1", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:37.057597", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PetLinks2", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:40.962185", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICD3_DRAC", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:44.778089", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LIEBERT.2FL", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:48.749402", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal132", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:52.662480", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal133", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:40:56.674396", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal144", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:00.601900", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal145", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:04.471165", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal176", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:08.377341", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal177", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:12.231106", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal194", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:16.051179", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal195", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:19.940537", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON><PERSON><PERSON>", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:23.834927", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "medinote2", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:27.747354", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "medinote1", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:31.613338", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN3.dmz", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:35.461422", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN5.dmz", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:39.372876", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN1.dmz", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:43.358557", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN4.dmz", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:47.185334", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN9.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:41:54.623459", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN10.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:42:02.072502", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN11.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:42:09.207119", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:42:16.465941", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:42:23.660858", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS3", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:42:30.795471", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS4", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:42:37.902834", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS5", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:42:44.983922", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLHEXCHANGE.NLH.ORG", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:42:52.285692", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLH-ISWEB_VIP_NETSCALER1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:42:59.908523", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:43:07.520622", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_CACHE", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:43:14.864756", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_STORE1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:43:22.483288", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_STORE2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:43:29.963662", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_STORE144", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:43:37.669080", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "ResnickPacs1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:43:45.194275", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "ResnickPACS2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:43:52.831749", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TeleRadPC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:44:00.568262", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "CatScan", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:44:08.202752", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PERTH_MRI", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:44:15.739122", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PETScanCT", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:44:23.326051", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "XELERIS", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:44:30.943439", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "INFINIA", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:44:38.552381", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "D5000", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:44:46.190970", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Ultrasound1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:44:53.872071", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Ultrasound2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:45:01.428889", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Ultrasound3", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:45:09.433197", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "KonicaJM", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:45:16.790444", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Konicardr1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:45:23.899078", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "KonicaRdr2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:45:30.946115", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "KonicaRdr3", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:45:38.036121", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_NEW", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:45:45.406129", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "US_LOGI_E9", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:45:52.810377", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk242", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:45:59.982324", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk243", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:46:07.172825", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk244", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:46:14.363481", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk245", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:46:21.505302", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk246", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:46:28.586067", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk247", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:46:35.731073", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalkPrime", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:46:42.876081", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalkSec", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:46:50.076912", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Spantel.Prod", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:46:57.289065", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "SpantelHL7.test", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:47:04.443905", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRXcenter2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:47:11.589543", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRXcenter3", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:47:18.781166", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRXcenter1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:47:25.942324", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRxChicago", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:47:33.123461", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRxDallas", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:47:40.203153", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MedentRemote", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:47:47.339017", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Medent.RPTS", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:47:51.258690", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "STpc.rtr", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:47:54.982078", "validation_status": null}], "checkpoint_version": "2.0"}