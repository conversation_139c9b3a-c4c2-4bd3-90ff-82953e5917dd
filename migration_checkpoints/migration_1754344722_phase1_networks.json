{"session_id": "migration_1754344722", "timestamp": "2025-08-04T14:59:25.477935", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost1", "✅ Updated host: TestHost2", "✅ Updated host: TestHost3"], "duration_seconds": 23.287909269332886, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork1", "✅ Updated network: TestNetwork2"], "duration_seconds": 17.15118908882141, "success_rate": 0.0}}, "current_phase": "phase1_networks", "phase_result": {"phase_name": "phase1_networks", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork1", "✅ Updated network: TestNetwork2"], "duration_seconds": 17.15118908882141, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "TestNetwork1", "object_id": "005056BF-7B88-0ed3-0000-017187308168", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:59:17.045893", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "TestNetwork2", "object_id": "005056BF-7B88-0ed3-0000-017187308187", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:59:25.477730", "validation_status": null}], "checkpoint_version": "2.0"}