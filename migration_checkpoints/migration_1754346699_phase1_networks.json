{"session_id": "migration_1754346699", "timestamp": "2025-08-04T15:32:08.024204", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost_Server1", "✅ Updated host: TestHost_Server2"], "duration_seconds": 14.304882764816284, "success_rate": 100.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 1, "created": 0, "updated": 1, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork_Subnet"], "duration_seconds": 7.790916204452515, "success_rate": 100.0}}, "current_phase": "phase1_networks", "phase_result": {"phase_name": "phase1_networks", "total_objects": 1, "created": 0, "updated": 1, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork_Subnet"], "duration_seconds": 7.790916204452515, "success_rate": 100.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "TestNetwork_Subnet", "object_id": "005056BF-7B88-0ed3-0000-017187339607", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:32:08.023999", "validation_status": null}], "checkpoint_version": "2.0"}