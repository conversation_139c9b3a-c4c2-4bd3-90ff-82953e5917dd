{"session_id": "migration_1754343406", "timestamp": "2025-08-04T14:38:01.454212", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost1", "✅ Updated host: TestHost2", "✅ Updated host: TestHost3"], "duration_seconds": 21.46864628791809, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 2, "created": 2, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created network: TestNetwork1", "✅ Created network: TestNetwork2"], "duration_seconds": 15.251391172409058, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 3, "created": 3, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created service: TestHTTP", "✅ Created service: TestHTTPS", "✅ Created service: TestSSH"], "duration_seconds": 18.54759979248047, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create object_group: TestNetworkGroup - fmcapi post() failed - no ID returned."], "duration_seconds": 5.977611780166626, "success_rate": 0.0}, "phase1_service_groups": {"phase_name": "phase1_service_groups", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create service_group: TestWebServices - fmcapi post() failed - no ID returned."], "duration_seconds": 5.973989009857178, "success_rate": 0.0}, "phase1_access_rules": {"phase_name": "phase1_access_rules", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule1 - fmcapi post() failed - no ID returned. Result: False"], "duration_seconds": 5.429436922073364, "success_rate": 0.0}}, "current_phase": "phase1_access_rules", "phase_result": {"phase_name": "phase1_access_rules", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule1 - fmcapi post() failed - no ID returned. Result: False"], "duration_seconds": 5.429436922073364, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "TestRule1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:01.453816", "validation_status": null}], "checkpoint_version": "2.0"}