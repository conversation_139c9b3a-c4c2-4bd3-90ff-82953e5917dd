{"session_id": "migration_1754345910", "timestamp": "2025-08-04T15:19:58.593978", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 0, "updated": 5, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost_WebServer", "✅ Updated host: TestHost_DatabaseServer", "✅ Updated host: TestHost_FileServer", "✅ Updated host: TestHost_PrintServer", "✅ Updated host: TestHost_BackupServer"], "duration_seconds": 39.46811509132385, "success_rate": 100.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork_LAN", "✅ Updated network: TestNetwork_DMZ", "✅ Updated network: TestNetwork_Management"], "duration_seconds": 24.48179006576538, "success_rate": 100.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 4, "created": 0, "updated": 4, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestService_HTTP_8080", "✅ Updated service: TestService_HTTPS_8443", "✅ Updated service: TestService_Database", "✅ Updated service: TestService_FTP"], "duration_seconds": 11.52721095085144, "success_rate": 100.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated object_group: TestGroup_Servers", "✅ Updated object_group: TestGroup_Networks"], "duration_seconds": 6.27012300491333, "success_rate": 100.0}}, "current_phase": "phase1_object_groups", "phase_result": {"phase_name": "phase1_object_groups", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated object_group: TestGroup_Servers", "✅ Updated object_group: TestGroup_Networks"], "duration_seconds": 6.27012300491333, "success_rate": 100.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "NetworkGroup", "object_name": "TestGroup_Servers", "object_id": "005056BF-7B88-0ed3-0000-017187313366", "message": "Network group exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:19:55.487120", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkGroup", "object_name": "TestGroup_Networks", "object_id": "005056BF-7B88-0ed3-0000-017187313385", "message": "Network group exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:19:58.593809", "validation_status": null}], "checkpoint_version": "2.0"}