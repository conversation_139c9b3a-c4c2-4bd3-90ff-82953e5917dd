{"session_id": "migration_1754343914", "timestamp": "2025-08-04T14:46:04.109509", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost1", "✅ Updated host: TestHost2", "✅ Updated host: TestHost3"], "duration_seconds": 22.00764489173889, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork1", "✅ Updated network: TestNetwork2"], "duration_seconds": 15.753277778625488, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestHTTP", "✅ Updated service: TestHTTPS", "✅ Updated service: TestSSH"], "duration_seconds": 8.742680072784424, "success_rate": 0.0}}, "current_phase": "phase1_services", "phase_result": {"phase_name": "phase1_services", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestHTTP", "✅ Updated service: TestHTTPS", "✅ Updated service: TestSSH"], "duration_seconds": 8.742680072784424, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "skipped", "object_type": "ProtocolPortObject", "object_name": "TestHTTP", "object_id": "005056BF-7B88-0ed3-0000-017187308206", "message": "Protocol port object updates skipped (objects are immutable)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:58.384463", "validation_status": null}, {"success": true, "action": "skipped", "object_type": "ProtocolPortObject", "object_name": "TestHTTPS", "object_id": "005056BF-7B88-0ed3-0000-017187308225", "message": "Protocol port object updates skipped (objects are immutable)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:01.247981", "validation_status": null}, {"success": true, "action": "skipped", "object_type": "ProtocolPortObject", "object_name": "TestSSH", "object_id": "005056BF-7B88-0ed3-0000-017187308244", "message": "Protocol port object updates skipped (objects are immutable)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:04.109317", "validation_status": null}], "checkpoint_version": "2.0"}