{"session_id": "migration_1754328956", "timestamp": "2025-08-04T14:57:37.705573", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 629, "failed": 0, "skipped": 0, "details": [], "duration_seconds": 2848.3845858573914, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 37, "failed": 26, "skipped": 0, "details": ["❌ Failed to create network: TeleMedVT3 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TelemedVT4 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TelemedVT5 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TeleMedVT1 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Medent.VPN.net - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Olympus.Inside.New - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: speculator - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: GEserviceNET - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Mill.PACS.NET - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: DI.NET - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 280.62504529953003, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed to create service: obj-tcp-eq-80 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to update service: obj-tcp-eq-15002 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-15331 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-3389 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-2222 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-6544 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-2020 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to create service: obj-tcp-eq-23 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to update service: obj-tcp-eq-15031 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to create service: obj-tcp-eq-5631 - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 122.99100136756897, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed to create object_group: Medivators - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: NUVODIA.INTERNAL.GROUP.NEW - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: NUVODIA.INTERNAL.PEER.NET1 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: DM_INLINE_NETWORK_4 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: DM_INLINE_NETWORK_6 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: FoodService - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: DI.Net.Group - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: Cardinal - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: medinotes - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 482.0017967224121, "success_rate": 0.0}, "phase1_service_groups": {"phase_name": "phase1_service_groups", "total_objects": 66, "created": 0, "updated": 0, "failed": 66, "skipped": 0, "details": ["❌ Failed to create service_group: PaceGlobalgrp - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: timeservice - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: timeserviceUDP - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: QUEST - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: citrixXML - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: GatewayDMZ - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: RSA - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: HFMBoces - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: GEinbound - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: GEoutbound - fmcapi post() failed - no ID returned. Result: False"], "duration_seconds": 270.94292068481445, "success_rate": 0.0}, "phase1_access_rules": {"phase_name": "phase1_access_rules", "total_objects": 224, "created": 0, "updated": 0, "failed": 224, "skipped": 0, "details": ["❌ Failed to create access_rule: inside_access_in_rule_1 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_2 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_3 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_4 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_5 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_6 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_7 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_8 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_9 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_10 - fmcapi post() failed - no ID returned. Result: False"], "duration_seconds": 891.2891645431519, "success_rate": 0.0}}, "current_phase": "phase1_access_rules", "phase_result": {"phase_name": "phase1_access_rules", "total_objects": 224, "created": 0, "updated": 0, "failed": 224, "skipped": 0, "details": ["❌ Failed to create access_rule: inside_access_in_rule_1 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_2 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_3 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_4 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_5 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_6 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_7 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_8 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_9 - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create access_rule: inside_access_in_rule_10 - fmcapi post() failed - no ID returned. Result: False"], "duration_seconds": 891.2891645431519, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:50.449438", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:54.383154", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:58.319172", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_4", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:02.337626", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_5", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:06.370481", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_6", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:10.309803", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_7", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:14.290624", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_8", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:18.270503", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_9", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:22.246220", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_10", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:26.224978", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_11", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:30.192448", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_12", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:34.155235", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_13", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:38.185934", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_14", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:42.125385", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_15", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:46.057239", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_16", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:50.042069", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_17", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:53.997439", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_18", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:43:57.919646", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_19", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:01.855669", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_20", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:05.816400", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_21", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:09.759958", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_22", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:13.751274", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_23", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:17.750544", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_24", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:21.718942", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_25", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:25.702295", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_26", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:29.675341", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_27", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:33.655203", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_28", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:37.602197", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_29", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:41.592396", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_30", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:45.576439", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_31", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:49.508613", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_32", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:53.499358", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_33", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:44:57.530520", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_34", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:01.492367", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_35", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:05.427569", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_36", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:09.361657", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_37", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:13.358642", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_38", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:17.398099", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_39", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:21.334644", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_40", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:25.344103", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_41", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:29.371374", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_42", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:33.352932", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_43", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:37.291887", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_44", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:41.217852", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_45", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:45.204826", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_46", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:49.152814", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_47", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:53.080788", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_48", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:45:57.073107", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_49", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:01.167960", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_50", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:05.094779", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_51", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:09.115178", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_52", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:13.196930", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_53", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:17.200943", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_54", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:21.165551", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_55", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:25.183068", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_56", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:29.168054", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_57", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:33.156528", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_58", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:37.081619", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_59", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:41.072525", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_60", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:45.012952", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_61", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:48.989165", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_62", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:52.950136", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_63", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:46:56.878554", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_64", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:00.803155", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_65", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:04.771499", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_66", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:08.711723", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_67", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:12.723781", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_68", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:16.687063", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_69", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:20.736031", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_70", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:24.691184", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_71", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:28.614650", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_72", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:32.586744", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_73", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:36.553651", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_74", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:40.501132", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_75", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:44.435361", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_76", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:48.398867", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_77", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:52.367071", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_78", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:47:56.326795", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_79", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:00.259203", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_80", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:04.227114", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_81", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:08.217844", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_82", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:12.161816", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_83", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:16.101379", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_84", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:20.060059", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_85", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:24.047031", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_86", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:28.010432", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_87", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:31.975378", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_88", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:35.940196", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_89", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:39.941396", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_90", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:43.929394", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:47.875368", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:51.789714", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:55.782246", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_4", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:48:59.773498", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_5", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:03.770040", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_6", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:07.749084", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_7", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:11.766273", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_8", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:15.754719", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_9", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:19.767186", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_10", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:23.754339", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_11", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:27.804040", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_12", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:31.783088", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_13", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:35.720816", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_14", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:39.665577", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_15", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:43.715191", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_16", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:47.670635", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_17", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:51.608827", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_18", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:55.601385", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_19", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:49:59.590940", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_20", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:03.514395", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_21", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:07.514292", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_22", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:11.497714", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_23", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:15.468255", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_24", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:19.477292", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_25", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:23.443758", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_26", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:27.407883", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_27", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:31.392332", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_28", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:35.386654", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_29", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:39.336145", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_30", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:43.303972", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_31", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:47.270824", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_32", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:51.201407", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_33", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:55.135461", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_34", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:50:59.153127", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_35", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:03.209412", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_36", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:07.150442", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_37", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:11.131514", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_38", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:15.153208", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_39", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:19.182874", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_40", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:23.168859", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_41", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:27.145084", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_42", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:31.089961", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_43", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:35.065765", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_44", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:39.018639", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_45", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:43.092952", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_46", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:47.040589", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_47", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:51.091513", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_48", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:55.017787", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_49", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:51:58.943738", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_50", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:02.923573", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_51", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:06.954625", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:10.876929", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:14.847896", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:18.873518", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_4", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:22.883449", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_5", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:26.806966", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_6", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:30.754367", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:34.731810", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:38.790973", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:42.816926", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_4", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:46.750361", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_5", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:50.686191", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_6", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:54.684188", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_7", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:52:58.684366", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_8", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:02.620569", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_9", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:06.595903", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_10", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:10.624194", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_11", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:14.601123", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_12", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:18.592562", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_13", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:22.634016", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_14", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:26.769736", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_15", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:30.748044", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_16", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:34.760392", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_17", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:38.702164", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_18", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:42.676514", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_19", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:46.647018", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN2.nlh.org_splitTunnelAcl_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:50.578851", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_6_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:54.529554", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN.nlh.org_splitTunnelAcl_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:53:58.543352", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN.nlh.org_splitTunnelAcl_rule_2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:02.481566", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN.nlh.org_splitTunnelAcl_rule_3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:06.419832", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_3_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:10.360056", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_9_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:14.361023", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_10_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:18.370556", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_11_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:22.410273", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:26.391427", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_12_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:30.389393", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:34.328528", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_14_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:38.284161", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_15_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:42.229738", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_2_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:46.223491", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_7_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:50.162792", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_880_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:54.121503", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_pnat_inbound_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:54:58.078903", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V2_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:02.046719", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1000_1_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:06.006457", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_13_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:09.968720", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_16_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:13.906052", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1120_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:17.910151", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V3_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:21.874191", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V3_rule_2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:25.810539", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V3_rule_3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:29.804202", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V4_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:33.789928", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V4_rule_2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:37.726683", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V4_rule_3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:41.683467", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_17_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:45.794682", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V14_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:49.876693", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V15_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:53.844995", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_5_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:55:57.806083", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1300_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:01.765540", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "sfr_redirect_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:05.753989", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Guest_access_in_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:09.701410", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Guest_access_in_rule_2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:13.810469", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Guest_access_in_rule_3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:17.765405", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:21.799689", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:25.781966", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:29.731144", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_4", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:33.752676", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_5", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:37.777696", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:41.930390", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:45.886780", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:49.957366", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_4", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:54.000626", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_5", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:56:57.934599", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_6", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:57:01.975872", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_7", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:57:05.938965", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_8", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:57:09.933591", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_9", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:57:13.883794", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_10", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:57:17.846639", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_11", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:57:21.805981", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_12", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:57:25.798174", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_13", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:57:29.787440", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_4_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:57:33.751060", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_8_rule_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:57:37.702359", "validation_status": null}], "checkpoint_version": "2.0"}