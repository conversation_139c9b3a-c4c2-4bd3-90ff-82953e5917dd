{"session_id": "migration_1754339485", "timestamp": "2025-08-04T13:31:35.929126", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.005800008773803711, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0012547969818115234, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed service: obj-tcp-eq-80 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15002 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15331 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-3389 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2222 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-6544 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2020 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-23 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15031 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-5631 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0010218620300292969, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed object_group: Medivators - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.GROUP.NEW - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.PEER.NET1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: FoodService - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DI.Net.Group - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: Cardinal - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: medinotes - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0012371540069580078, "success_rate": 0.0}, "phase1_service_groups": {"phase_name": "phase1_service_groups", "total_objects": 66, "created": 0, "updated": 0, "failed": 66, "skipped": 0, "details": ["❌ Failed service_group: PaceGlobalgrp - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeservice - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeserviceUDP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: QUEST - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: citrixXML - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GatewayDMZ - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: RSA - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: HFMBoces - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEinbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEoutbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0009541511535644531, "success_rate": 0.0}, "phase1_access_rules": {"phase_name": "phase1_access_rules", "total_objects": 224, "created": 0, "updated": 0, "failed": 224, "skipped": 0, "details": ["❌ Failed access_rule: inside_access_in_rule_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_2 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_7 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_8 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_9 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_10 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0011830329895019531, "success_rate": 0.0}}, "current_phase": "phase1_access_rules", "phase_result": {"phase_name": "phase1_access_rules", "total_objects": 224, "created": 0, "updated": 0, "failed": 224, "skipped": 0, "details": ["❌ Failed access_rule: inside_access_in_rule_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_2 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_7 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_8 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_9 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_10 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0011830329895019531, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928160", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928164", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928166", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928167", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928168", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928170", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928171", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928172", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928174", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928175", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928176", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928177", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928179", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_14", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928180", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_15", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928181", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_16", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928182", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_17", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928184", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_18", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928185", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_19", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928186", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_20", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928187", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_21", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928189", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_22", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928190", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_23", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928191", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_24", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928192", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_25", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928193", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_26", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928195", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_27", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928196", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_28", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928197", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_29", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928198", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_30", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928199", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_31", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928201", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_32", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928202", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_33", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928203", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_34", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928204", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_35", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928205", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_36", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928207", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_37", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928208", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_38", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928209", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_39", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928210", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_40", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928212", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_41", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928213", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_42", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928214", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_43", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928215", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_44", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928216", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_45", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928218", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_46", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928219", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_47", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928220", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_48", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928221", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_49", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928222", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_50", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928224", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_51", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928225", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_52", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928226", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_53", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928227", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_54", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928228", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_55", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928230", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_56", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928231", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_57", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928232", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_58", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928233", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_59", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928234", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_60", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928236", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_61", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928237", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_62", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928238", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_63", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928239", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_64", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928240", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_65", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928242", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_66", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928243", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_67", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928245", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_68", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928246", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_69", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928247", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_70", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928248", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_71", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928250", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_72", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928251", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_73", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928252", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_74", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928253", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_75", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928254", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_76", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928256", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_77", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928257", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_78", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928259", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_79", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928260", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_80", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928261", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_81", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928262", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_82", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928264", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_83", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928265", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_84", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928266", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_85", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928267", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_86", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928268", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_87", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928270", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_88", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928271", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_89", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928272", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_90", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928273", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928274", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928276", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928277", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928294", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928298", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928300", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928301", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928303", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928304", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928305", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928307", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928308", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928310", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_14", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928311", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_15", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928312", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_16", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928313", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_17", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928315", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_18", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928316", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_19", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928317", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_20", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928319", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_21", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928321", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_22", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928322", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_23", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928323", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_24", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928324", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_25", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928326", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_26", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928328", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_27", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928329", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_28", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928330", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_29", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928331", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_30", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928333", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_31", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928334", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_32", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928335", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_33", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928336", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_34", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928338", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_35", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928339", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_36", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928340", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_37", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928341", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_38", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928343", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_39", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928344", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_40", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928346", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_41", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928347", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_42", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928348", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_43", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928349", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_44", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928351", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_45", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928352", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_46", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928353", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_47", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928354", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_48", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928356", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_49", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928357", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_50", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928358", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_51", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928359", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928360", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928362", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928363", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928364", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928366", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928367", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928368", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928369", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928371", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928372", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928373", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928375", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928376", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928377", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928378", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928380", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928381", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928382", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928383", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_14", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928385", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_15", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928386", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_16", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928387", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_17", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928388", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_18", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928389", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_19", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928391", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN2.nlh.org_splitTunnelAcl_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928392", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_6_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928393", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN.nlh.org_splitTunnelAcl_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928394", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN.nlh.org_splitTunnelAcl_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928396", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN.nlh.org_splitTunnelAcl_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928397", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_3_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928398", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_9_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928399", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_10_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928401", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_11_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928402", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928403", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_12_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928405", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928406", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_14_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928407", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_15_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928408", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_2_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928410", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_7_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928411", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_880_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928412", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_pnat_inbound_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928413", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V2_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928414", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1000_1_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928416", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_13_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928417", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_16_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928418", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1120_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928419", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V3_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928421", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V3_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928422", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V3_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928423", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V4_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928424", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V4_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928425", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V4_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928427", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_17_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928428", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V14_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928429", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V15_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928430", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_5_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928432", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1300_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928433", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "sfr_redirect_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928434", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Guest_access_in_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928435", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Guest_access_in_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928437", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Guest_access_in_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928438", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928439", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928440", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928442", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928443", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928444", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928445", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928447", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928448", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928449", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928450", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928452", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928453", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928454", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928455", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928456", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928458", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928459", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928460", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_4_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928461", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_8_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.928463", "validation_status": null}], "checkpoint_version": "2.0"}