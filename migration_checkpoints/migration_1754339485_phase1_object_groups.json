{"session_id": "migration_1754339485", "timestamp": "2025-08-04T13:31:35.924250", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.005800008773803711, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0012547969818115234, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed service: obj-tcp-eq-80 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15002 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15331 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-3389 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2222 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-6544 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2020 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-23 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15031 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-5631 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0010218620300292969, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed object_group: Medivators - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.GROUP.NEW - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.PEER.NET1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: FoodService - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DI.Net.Group - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: Cardinal - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: medinotes - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0012371540069580078, "success_rate": 0.0}}, "current_phase": "phase1_object_groups", "phase_result": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed object_group: Medivators - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.GROUP.NEW - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.PEER.NET1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: FoodService - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DI.Net.Group - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: Cardinal - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: medinotes - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0012371540069580078, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Medivators", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923521", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA.INTERNAL.GROUP.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923524", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA.INTERNAL.PEER.NET1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923526", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923527", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923529", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FoodService", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923530", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DI.Net.Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923532", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "STRATEGICSOLUTIONS.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923533", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "<PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923535", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "medinotes", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923536", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CitrixServers.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923538", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MilleniumPACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923539", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ExchangeServers", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923541", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "TeleMedVT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923542", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "PacsServers", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923543", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MDI.OUT.<PERSON>ow", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923545", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "eRXdataCenters", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923546", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Medent.Interface", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923548", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SMHA.RAD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923549", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "RAD.PACS.READ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923550", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SOPHOS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923552", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CitrixServers1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923553", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CitrixServers1_ref", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923555", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MVO_Allow_OUTBOUND_Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923556", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ProvationServers", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923558", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "AAI.NYOH.PACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923559", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Dolby_OUT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923561", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Dolby_Servers", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923562", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Healthtouch.out", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923563", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FoodSVC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923565", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ALBANYPACS.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923566", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HIXNY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923567", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Olympus.inside.group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923569", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MDI_Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923570", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Brian_DHCP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923572", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Schumacher.Inside", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923573", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENTHQ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923575", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT_GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923576", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "WINDOWS_XP_DENY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923577", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "APPLE.OUT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923579", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ProviderOrg.External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923580", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CITRIX_EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923582", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CITRIXGATEWAY.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923583", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CITRIX_INTERNAL_TO_DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923585", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MIS_TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923586", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MARKETO_SPAMMER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923588", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ENDOWORKS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923589", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CE2000.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923590", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CE2000.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923592", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_24.97.36.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923593", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_65.114.41.136", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923595", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_12.39.198.49", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923596", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_173.84.224.94", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923598", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_12.152.123.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923599", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HIXNY.MBMS.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923600", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA_VPN_NLH_PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923602", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CLEARWATERTEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923603", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HIXNY.INTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923605", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MDI.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923606", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NYOH.INTERNAL.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923608", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA.VPN.SENDPOINT.MASTER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923609", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923610", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923612", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923613", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SMHA.RAD.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923615", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923616", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT.NIMBLE.OPENVPN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923617", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923619", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT.NIMBLE.OPENVPN.INSIDE.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923620", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "TCPUDP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923622", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "EXPANSE_VLANS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923623", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SmartNet_Devices", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923624", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Domain.Controllers.Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923626", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Quest.NLH2Quest.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923627", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923629", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CHANGE.HEALTHCARE.NLH.INTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923630", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CHC.EXTERNAL.NETWORK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923631", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "PHINMS.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923633", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLI.INTERNAL.NAT.CHC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923635", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLI-BG-GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923636", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923637", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "QUEST.VPN.INTERNAL.GROUP.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923639", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "WEBSSO.MEDITECH.COM.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923640", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "BACKLINE.LDAP.NLH.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923641", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FIRECALLSYSTEM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923643", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FIRECALLSYSTEM__ENDPOINTS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923644", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "IT_DEPT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923645", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FULL_PORT_ACCESS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923647", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CISCO_INTERNAL_2_EXTERNAL_ACL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923648", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HEALTHTOUCH.NLH.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923650", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HEALTHTOUCH.PEER.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923651", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Greycastle_Testing_External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923652", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "LINKBG.SPAM.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923654", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "love.explorethebest.com.spam.group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923655", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.ACRONIS.GROUP.INSIDE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923657", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.ACRONIS.GROUP.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923658", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "BARRACUDA.LDAP.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923660", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "REYHEALTH.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923661", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "EMAIL.BLACKLIST.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923662", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.DI.GEDEVICES", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923664", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923665", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "blackblazeb2.goup", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923667", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HAYNS.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923668", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923669", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Incident.External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923671", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.Firewall.Internal.Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923672", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Clearwater.Internal.Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923674", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923675", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SSI.EXTERNAL.PEER.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923676", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "BANDWIDTH.TEST.GROUP.OUTSIDE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923678", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FRESHWORKS.EXCLUSIONS.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.923679", "validation_status": null}], "checkpoint_version": "2.0"}