{"session_id": "migration_1754328956", "timestamp": "2025-08-04T14:38:15.385168", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 629, "failed": 0, "skipped": 0, "details": [], "duration_seconds": 2848.3845858573914, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 37, "failed": 26, "skipped": 0, "details": ["❌ Failed to create network: TeleMedVT3 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TelemedVT4 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TelemedVT5 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TeleMedVT1 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Medent.VPN.net - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Olympus.Inside.New - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: speculator - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: GEserviceNET - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Mill.PACS.NET - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: DI.NET - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 280.62504529953003, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed to create service: obj-tcp-eq-80 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to update service: obj-tcp-eq-15002 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-15331 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-3389 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-2222 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-6544 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-2020 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to create service: obj-tcp-eq-23 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to update service: obj-tcp-eq-15031 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to create service: obj-tcp-eq-5631 - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 122.99100136756897, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed to create object_group: Medivators - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: NUVODIA.INTERNAL.GROUP.NEW - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: NUVODIA.INTERNAL.PEER.NET1 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: DM_INLINE_NETWORK_4 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: DM_INLINE_NETWORK_6 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: FoodService - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: DI.Net.Group - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: Cardinal - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: medinotes - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 482.0017967224121, "success_rate": 0.0}}, "current_phase": "phase1_object_groups", "phase_result": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed to create object_group: Medivators - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: NUVODIA.INTERNAL.GROUP.NEW - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: NUVODIA.INTERNAL.PEER.NET1 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: DM_INLINE_NETWORK_4 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: DM_INLINE_NETWORK_6 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: FoodService - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: DI.Net.Group - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: Cardinal - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: medinotes - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 482.0017967224121, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Medivators", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:17.700024", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA.INTERNAL.GROUP.NEW", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:22.042497", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA.INTERNAL.PEER.NET1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:26.421014", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_4", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:30.713454", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_6", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:35.117047", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FoodService", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:39.457124", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DI.Net.Group", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:43.809108", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "STRATEGICSOLUTIONS.EXTERNAL.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:48.112092", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "<PERSON>", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:52.429783", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "medinotes", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:56.715883", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CitrixServers.dmz", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:01.003613", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MilleniumPACS", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:05.345054", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ExchangeServers", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:09.667382", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "TeleMedVT", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:13.981769", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "PacsServers", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:18.302180", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MDI.OUT.<PERSON>ow", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:22.670962", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "eRXdataCenters", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:26.996336", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Medent.Interface", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:31.321137", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SMHA.RAD", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:35.738576", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "RAD.PACS.READ", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:40.157561", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SOPHOS", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:44.503174", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CitrixServers1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:48.891609", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CitrixServers1_ref", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:53.351896", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MVO_Allow_OUTBOUND_Group", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:31:57.716214", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ProvationServers", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:02.065103", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "AAI.NYOH.PACS", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:06.481244", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Dolby_OUT", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:10.813349", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Dolby_Servers", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:15.157275", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Healthtouch.out", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:19.468227", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FoodSVC", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:23.876588", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ALBANYPACS.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:28.150148", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HIXNY", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:32.421628", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Olympus.inside.group", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:36.766801", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MDI_Group", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:41.041176", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Brian_DHCP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:45.404468", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Schumacher.Inside", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:49.679129", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENTHQ", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:54.094686", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT_GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:32:58.417476", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "WINDOWS_XP_DENY", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:02.806347", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "APPLE.OUT", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:07.124924", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ProviderOrg.External", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:11.409082", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CITRIX_EXTERNAL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:15.754189", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CITRIXGATEWAY.DMZ", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:20.076839", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CITRIX_INTERNAL_TO_DMZ", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:24.457094", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MIS_TEST", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:28.753758", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MARKETO_SPAMMER", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:33.038434", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ENDOWORKS", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:37.344091", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CE2000.INTERNAL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:41.678161", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CE2000.EXTERNAL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:45.978989", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_24.97.36.3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:50.363242", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_65.114.41.136", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:54.750425", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_12.39.198.49", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:33:59.082122", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_173.84.224.94", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:03.376441", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_12.152.123.2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:07.713984", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HIXNY.MBMS.INTERNAL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:12.119664", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA_VPN_NLH_PEER", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:16.590823", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CLEARWATERTEST", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:20.948616", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HIXNY.INTERNAL.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:25.370068", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MDI.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:29.695907", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NYOH.INTERNAL.NET", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:34.056153", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA.VPN.SENDPOINT.MASTER", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:38.424059", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_7", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:42.715818", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_8", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:47.018094", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_9", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:51.328934", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SMHA.RAD.NEW", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:34:55.755332", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_10", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:00.073942", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT.NIMBLE.OPENVPN", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:04.374242", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:08.721414", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT.NIMBLE.OPENVPN.INSIDE.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:13.116375", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "TCPUDP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:17.421327", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "EXPANSE_VLANS", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:21.823955", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SmartNet_Devices", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:26.256913", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Domain.Controllers.Group", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:30.600634", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Quest.NLH2Quest.Internal", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:34.977660", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:39.356277", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CHANGE.HEALTHCARE.NLH.INTERNAL.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:43.678300", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CHC.EXTERNAL.NETWORK", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:47.981135", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "PHINMS.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:52.307471", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLI.INTERNAL.NAT.CHC", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:35:56.698052", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLI-BG-GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:01.063729", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_11", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:05.386361", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "QUEST.VPN.INTERNAL.GROUP.2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:09.731488", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "WEBSSO.MEDITECH.COM.EXTERNAL.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:14.081148", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "BACKLINE.LDAP.NLH.INTERNAL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:18.401860", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FIRECALLSYSTEM", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:22.695837", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FIRECALLSYSTEM__ENDPOINTS", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:27.122029", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "IT_DEPT", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:31.383700", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FULL_PORT_ACCESS", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:35.692312", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CISCO_INTERNAL_2_EXTERNAL_ACL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:40.056527", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HEALTHTOUCH.NLH.INTERNAL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:44.339890", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HEALTHTOUCH.PEER.INTERNAL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:48.661528", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Greycastle_Testing_External", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:53.027985", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "LINKBG.SPAM.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:36:57.408307", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "love.explorethebest.com.spam.group", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:01.695943", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.ACRONIS.GROUP.INSIDE", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:05.974720", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.ACRONIS.GROUP.EXTERNAL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:10.289795", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "BARRACUDA.LDAP.EXTERNAL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:14.587484", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "REYHEALTH.EXTERNAL.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:18.912115", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "EMAIL.BLACKLIST.EXTERNAL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:23.285190", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.DI.GEDEVICES", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:27.673427", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:32.008499", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "blackblazeb2.goup", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:36.396847", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HAYNS.EXTERNAL.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:40.797559", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:45.126569", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Incident.External", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:49.442559", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.Firewall.Internal.Group", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:53.779259", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Clearwater.Internal.Group", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:37:58.097532", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:02.379411", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SSI.EXTERNAL.PEER.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:06.673490", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "BANDWIDTH.TEST.GROUP.OUTSIDE", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:11.002634", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FRESHWORKS.EXCLUSIONS.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:15.382439", "validation_status": null}], "checkpoint_version": "2.0"}