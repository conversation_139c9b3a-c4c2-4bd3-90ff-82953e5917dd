{"session_id": "migration_1754344722", "timestamp": "2025-08-04T14:59:34.804453", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost1", "✅ Updated host: TestHost2", "✅ Updated host: TestHost3"], "duration_seconds": 23.287909269332886, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork1", "✅ Updated network: TestNetwork2"], "duration_seconds": 17.15118908882141, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestHTTP", "✅ Updated service: TestHTTPS", "✅ Updated service: TestSSH"], "duration_seconds": 9.325829029083252, "success_rate": 0.0}}, "current_phase": "phase1_services", "phase_result": {"phase_name": "phase1_services", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestHTTP", "✅ Updated service: TestHTTPS", "✅ Updated service: TestSSH"], "duration_seconds": 9.325829029083252, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "TestHTTP", "object_id": "005056BF-7B88-0ed3-0000-017187308206", "message": "Protocol port object exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:59:28.556812", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "TestHTTPS", "object_id": "005056BF-7B88-0ed3-0000-017187308225", "message": "Protocol port object exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:59:31.613706", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "TestSSH", "object_id": "005056BF-7B88-0ed3-0000-017187308244", "message": "Protocol port object exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:59:34.804333", "validation_status": null}], "checkpoint_version": "2.0"}