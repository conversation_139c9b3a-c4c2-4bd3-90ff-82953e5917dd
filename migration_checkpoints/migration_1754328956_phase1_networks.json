{"session_id": "migration_1754328956", "timestamp": "2025-08-04T14:28:10.324106", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 629, "failed": 0, "skipped": 0, "details": [], "duration_seconds": 2848.3845858573914, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 37, "failed": 26, "skipped": 0, "details": ["❌ Failed to create network: TeleMedVT3 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TelemedVT4 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TelemedVT5 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TeleMedVT1 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Medent.VPN.net - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Olympus.Inside.New - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: speculator - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: GEserviceNET - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Mill.PACS.NET - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: DI.NET - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 280.62504529953003, "success_rate": 0.0}}, "current_phase": "phase1_networks", "phase_result": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 37, "failed": 26, "skipped": 0, "details": ["❌ Failed to create network: TeleMedVT3 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TelemedVT4 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TelemedVT5 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TeleMedVT1 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Medent.VPN.net - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Olympus.Inside.New - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: speculator - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: GEserviceNET - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Mill.PACS.NET - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: DI.NET - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 280.62504529953003, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TeleMedVT3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:23:34.098785", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TelemedVT4", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:23:38.415934", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TelemedVT5", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:23:42.706174", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TeleMedVT1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:23:47.055458", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Medent.VPN.net", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:23:51.374804", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "SMHApacsSUBNET", "object_id": "AC2AA168-21DA-0ed3-0000-004294969427", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:23:55.968732", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "pacs.net", "object_id": "AC2AA168-21DA-0ed3-0000-004294969428", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:24:00.470564", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "PACS_VCE", "object_id": "AC2AA168-21DA-0ed3-0000-004294969429", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:24:05.141410", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "pacs.net_1", "object_id": "AC2AA168-21DA-0ed3-0000-004294969430", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:24:09.666004", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Olympus.Inside.New", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:24:13.993804", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "speculator", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:24:18.381874", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GEserviceNET", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:24:22.708418", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Mill.PACS.NET", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:24:27.092426", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "DI.NET", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:24:31.484967", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "STUDENT_VLAN", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:24:35.896309", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "questlab", "object_id": "AC2AA168-21DA-0ed3-0000-004294969431", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:24:40.475309", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "iPEOPLEremote", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:24:44.779086", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "LAN", "object_id": "AC2AA168-21DA-0ed3-0000-004294969432", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:24:49.469139", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "RALSplusLAN", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:24:53.847796", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "PhilipsSupport", "object_id": "AC2AA168-21DA-0ed3-0000-004294969433", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:24:58.435533", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "STRAT_SOL.NET.INTERNAL1", "object_id": "AC2AA168-21DA-0ed3-0000-004294969434", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:25:02.985927", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "MVOrtho.net", "object_id": "AC2AA168-21DA-0ed3-0000-004294969435", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:25:07.500667", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "LAN_1", "object_id": "AC2AA168-21DA-0ed3-0000-004294969436", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:25:12.012613", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "MilleniumPACSnat", "object_id": "AC2AA168-21DA-0ed3-0000-004294969437", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:25:16.480549", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "MVOatJSC.net", "object_id": "AC2AA168-21DA-0ed3-0000-004294969438", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:25:21.013980", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "SENTRYDS.NET", "object_id": "AC2AA168-21DA-0ed3-0000-004294969439", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:25:25.505017", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "SENTRYDS", "object_id": "AC2AA168-21DA-0ed3-0000-004294969440", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:25:30.094135", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "pacs.net-01", "object_id": "AC2AA168-21DA-0ed3-0000-004294972688", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:25:34.629034", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "LAN-01", "object_id": "AC2AA168-21DA-0ed3-0000-004294972703", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:25:39.181829", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MilleniumPACSnat-10.205.56.127", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:25:43.567204", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "obj-10.205.56.128-10.205.56.255", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:25:47.898273", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "obj_any", "object_id": "AC2AA168-21DA-0ed3-0000-004294972682", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:25:52.401071", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "obj_any-03", "object_id": "AC2AA168-21DA-0ed3-0000-004294972695", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:25:56.892302", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "NUVODIA_NETWORK_3", "object_id": "AC2AA168-21DA-0ed3-0000-004294972686", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:01.418272", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "GUEST_WLAN_NAT", "object_id": "AC2AA168-21DA-0ed3-0000-004294972665", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:05.946525", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "GUEST_NETWORK", "object_id": "AC2AA168-21DA-0ed3-0000-004294972700", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:10.466708", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "VENDOR_WLAN_NAT", "object_id": "AC2AA168-21DA-0ed3-0000-004294972680", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:14.991380", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "CREDITCARD_CAFE_EXTERNAL1", "object_id": "AC2AA168-21DA-0ed3-0000-004294972693", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:19.507629", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "CREDITCARD_CAFE2_EXTERNAL2", "object_id": "AC2AA168-21DA-0ed3-0000-004294972681", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:24.089646", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "CREDITCARD_CAFE_EXTERNAL", "object_id": "AC2AA168-21DA-0ed3-0000-004294972673", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:28.586083", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "STRAT_SOL.NET.INTERNAL2", "object_id": "AC2AA168-21DA-0ed3-0000-004294969441", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:33.076245", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN1", "object_id": "AC2AA168-21DA-0ed3-0000-004294972664", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:37.572192", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN2", "object_id": "AC2AA168-21DA-0ed3-0000-004294972671", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:42.127879", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN3", "object_id": "AC2AA168-21DA-0ed3-0000-004294972670", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:46.650058", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN4", "object_id": "AC2AA168-21DA-0ed3-0000-004294972669", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:51.178468", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "QUEST.VPN.EXTERNAL.2", "object_id": "AC2AA168-21DA-0ed3-0000-004294969442", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:26:55.686952", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CHC.OPTUM.NAT.INTERNAL.SUB", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:26:59.951654", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:27:04.236572", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:27:08.503699", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "BARRACUDA.CLOUD.EXTERNAL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:27:12.902493", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE3", "object_id": "AC2AA168-21DA-0ed3-0000-004294972678", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:27:17.436408", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE4", "object_id": "AC2AA168-21DA-0ed3-0000-004294972677", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:27:21.953227", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "GESUPPORT.INTERNAL.NET", "object_id": "AC2AA168-21DA-0ed3-0000-004294969443", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:27:26.489854", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CLEARWATER3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:27:30.778638", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CLEARWATER4", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:27:35.136450", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "backblazeb2.com", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:27:39.491175", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "HANYS.EXTERNAL.1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:27:43.867969", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "HANYS.INTERNAL.2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:27:48.199304", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "HANYS.EXTERNAL.3", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:27:52.444700", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "Clearwater.Internal.Peer.Range", "object_id": "AC2AA168-21DA-0ed3-0000-004294969444", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:27:57.057989", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "NLH.Firewall.Range.Internal", "object_id": "AC2AA168-21DA-0ed3-0000-004294969445", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:28:01.512012", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "SSI.EXTERNAL.PEER.1", "object_id": "AC2AA168-21DA-0ed3-0000-004294972687", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:28:06.047364", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MICROSOFTSTREAM.COM", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:28:10.322131", "validation_status": null}], "checkpoint_version": "2.0"}