{"session_id": "migration_1754339485", "timestamp": "2025-08-04T13:31:35.913281", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.005800008773803711, "success_rate": 0.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.005800008773803711, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadSaratoga", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909312", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadAmsMem", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909324", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadStMarys", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909326", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadSeton", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909329", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadBellevue", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909331", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CITRIXFS02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909333", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP30", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909335", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_C-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909337", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHNAS12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909338", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC-D_iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909340", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909342", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHNAS11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909344", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_A-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909346", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_E-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909347", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_D-NEWiSCSI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909349", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "UNITY-SDC_iSCSI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909351", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-WEB01-WS01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909352", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909354", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909356", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909358", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909360", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909361", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICF", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909363", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909365", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DICTATION02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909367", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDILIVE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909369", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_FS_SEC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909371", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CTScanner", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909372", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON><PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909374", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RandF", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909376", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PetLinks1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909377", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PetLinks2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909379", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICD3_DRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909381", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LIEBERT.2FL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909382", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal132", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909384", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal133", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909387", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal144", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909389", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal145", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909391", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal176", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909393", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal177", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909395", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal194", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909397", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal195", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909398", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON><PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909400", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "medinote2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909402", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "medinote1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909403", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN3.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909405", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN5.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909407", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN1.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909408", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN4.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909410", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN9.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909412", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN10.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909413", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN11.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909415", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909417", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909419", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909420", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909422", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909424", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHEXCHANGE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909425", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB_VIP_NETSCALER1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909427", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909429", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_CACHE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909431", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_STORE1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909432", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_STORE2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909434", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_STORE144", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909436", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ResnickPacs1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909437", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ResnickPACS2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909439", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleRadPC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909441", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CatScan", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909443", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PERTH_MRI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909444", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PETScanCT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909446", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XELERIS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909448", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INFINIA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909450", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "D5000", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909451", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ultrasound1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909453", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ultrasound2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909455", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ultrasound3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909456", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KonicaJM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909458", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Konicardr1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909461", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KonicaRdr2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909462", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KonicaRdr3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909464", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909465", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "US_LOGI_E9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909467", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk242", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909469", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk243", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909470", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk244", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909472", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk245", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909474", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk246", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909475", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk247", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909477", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalkPrime", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909479", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalkSec", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909480", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Spantel.Prod", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909482", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SpantelHL7.test", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909484", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRXcenter2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909485", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRXcenter3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909487", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRXcenter1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909489", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRxChicago", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909491", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRxDallas", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909492", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MedentRemote", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909494", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Medent.RPTS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909496", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "STpc.rtr", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909497", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "spc.rtr", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909499", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ppc.pix", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909501", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.ps1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909502", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.ps2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909504", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.ps3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909506", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.ps4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909508", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.syn1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909509", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.syn2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909511", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.orpc1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909513", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.orpc2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909515", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.orpc3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909516", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909518", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909520", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909521", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.KPServer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909523", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909525", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "smha.mammo", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909526", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "smha.pacsed30", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909528", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "smha.pacrd06", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909530", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909532", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909533", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SHMA.read7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909535", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909537", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909538", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909540", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.Synapse.Dest", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909542", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P-DI-MGR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909543", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_READ3_NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909545", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_CIO1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909547", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_DI_NUMED", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909549", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAMMO40", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909550", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MOMMO41", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909552", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "phil<PERSON><PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909554", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSP19WEB.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909555", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909557", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909559", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PCC_BILL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909560", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909562", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909564", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909565", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SOPHOSEMAIL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909567", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SOPHOSWEB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909569", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN6.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909571", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ORTIZ_LT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909572", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "p_mis_netadmin", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909574", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_OR3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909576", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_OR1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909577", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_OR2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909579", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_DI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909581", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDC1_IPMI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909583", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDC2_IPMI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909584", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AAI.120", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909586", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AAI.124", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909588", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AAI.125", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909589", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AAI.52", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909591", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INTERLACE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909593", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHUTILITY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909594", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHTEST01-NIC2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909596", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BPC-UPS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909598", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ALBANYMED.IN.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909599", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ALBANYMED.IN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909601", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "hixny.com_integration", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909603", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "hixny.com_prod", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909604", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "webservices.hixny.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909606", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDITEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909608", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909610", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT03", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909612", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB_VIRTUALIP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909614", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB_VIRTUALIP_NETSCALER2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909616", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT05", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909618", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PHA_WS3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909621", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PHA_WS2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909623", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_MR_SCAN1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909625", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "easyeeg", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909627", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VENUE50_p_pacs_cdburn", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909629", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "integration.hixny.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909632", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Hixney.net_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909634", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CITRIX_STOREFRONT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909636", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P-IT-MGR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909638", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETSCALER.VPX", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909639", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETSCALER.WEB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909641", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETSCALERSUBNETIP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909643", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDC01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909645", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDC02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909646", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_CISCO_01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909648", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "p_mis_netadmin2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909649", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Direct.Hixny.Com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909651", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Healthstream.SMPT.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909653", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Hixny.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909654", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Hixny.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909656", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "statrad.hl7.test", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909658", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_FIN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909659", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHENDO01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909661", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHENDO01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909663", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ENDOWORKS02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909664", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ENDOWORKS03", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909666", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IS_PACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909668", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "retsolinc2.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909669", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "retsolinc3.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909671", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SophosMailExt", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909673", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "IRIS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909674", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "smtp.biz.rr.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909676", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Hypertype", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909678", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MVP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909680", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LeaderHFTPsite", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909681", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LeaderHFTPsite2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909683", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "stentor.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909685", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TOGARM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909686", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Infotrak", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909688", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "sftp.lifethc.org", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909689", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleVideo1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909691", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Televid2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909693", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CONNECTPLUS01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909694", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VeriquestPC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909696", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VeriquestSite", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909698", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT_PORTAL_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909699", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HYPER-_REPLICA_BROKER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909701", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Sodexho", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909703", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Provation-out", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909704", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VeriquestServer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909706", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909708", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "IMO_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909709", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WWW.UPTODATE.COM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909711", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP22", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909713", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HYPER-V_CLUSTER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909714", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENTPORTAL.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909716", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "remote.nlh.org", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909718", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mail.nlh.org", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909719", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DIRECT.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909721", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleMed_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909723", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDI.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909724", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCISCO", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909726", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LabCorp3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909728", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LabCorpDev", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909730", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LabCorpProd", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909731", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TheOutsourceGroup", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909733", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleradIT_Millenium1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909735", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleradIT_Millenium2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909736", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FastChart.Inside", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909738", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ellis.inside", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909740", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "STATRAD.DR.SVR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909741", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHTEST01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909743", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH.ORG.EXTERNAL.FORMS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909745", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-************", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909746", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-***********", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909748", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-***********", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909750", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-************", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909751", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-***********", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909753", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-************", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909755", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-************", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909756", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ellis.Peer.New", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909758", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH.PEER.INTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909760", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Medent.Peer.New.", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909762", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.MBMS.MILLENIUMBILLING.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909764", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909765", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MCKESSON.MC.PHARM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909767", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "newsync3.mkesson.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909769", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-0.0.0.0", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909770", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.pacs1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909772", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.pacs2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909774", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.pacs3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909775", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS.VCE1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909777", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS.VCE2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909779", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS.VCE3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909780", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS.VCE4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909782", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT_NAS_INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909784", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909785", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909787", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PDX.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909789", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PDX.External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909791", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.PEER.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909792", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.INTERNAL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909794", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XCHANGEWORX.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909796", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETSCALER.NLHRESTAPI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909797", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA_VPN_NLH_PEER1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909799", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA_VPN_NLH_PEER2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909801", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FIREPOWER_VM_ESXI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909803", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.READ.10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909804", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ESRS_EMC_VIRTUAL_APPLIANCE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909806", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909808", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909809", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RESTFULAPI.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909811", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909812", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909814", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909816", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909817", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909819", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_MIS52_DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909821", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDITEST_SENDTRYDS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909822", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP25", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909824", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "skype.nlh.org_external", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909826", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "st_netadmin", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909827", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.RAD.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909829", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MVO_AMST_PEER_NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909831", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "GUEST_INTERFACE_EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909832", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VENDOR_EXTERNAL_INTERFACE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909834", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "p_mis_netadmin.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909836", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB.DMZVR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909837", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AMC.PACS.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909839", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BRIAN_DHCP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909841", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BPC.External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909842", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_MIS_CISCOMON", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909844", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP17", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909845", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP18", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909847", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP19", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909849", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_MIS52.WAYNE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909850", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETADMIN.DMZ.TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909852", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "EUGENE10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909854", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDITECHAPIVIP1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909856", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDITECHAPIVIP2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909858", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "StratSolution.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909859", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PHA_PDX1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909861", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPRTG01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909863", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RCARE-SERVER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909864", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDMZ01_SWITCH", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909866", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909868", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PRTG.NLH.ORG.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909869", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKLINE.VPN.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909871", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "UNITEDLABNETWORK.VPN.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909873", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_18.204.173.205", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909874", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKLINE.LDAP.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909876", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT.NIMBLE.INSIDE.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909878", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909879", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909881", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ROBOT_GE_VOT_TRAIN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909883", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BILL_BAIRD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909884", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS29-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909886", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DOLBEY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909888", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DOLBEYTEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909889", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH_DCDS_9300s", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909891", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Schumacher.Inside1.new.ADTPROD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909893", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Schumacher.Inside2.new.ADTTEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909894", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Schumacher.VPN.Peer.New", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909896", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT-EXPORT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909898", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUEST.VPN.PEER.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909899", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUEST.VPN.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909901", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909902", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.PEER.INTERNAL.TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909904", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.PEER.INTERNAL.PROD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909906", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSP19OFCWEB.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909907", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENTPORTAL.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909909", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mtrestexpapis-live01.nlh.org.external", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909911", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mtrestexpapis-test01.nlh.org.external", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909913", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mtrestexpapis-test01.nlh.org.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909915", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mtrestexpapis-live01.nlh.org.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909917", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909919", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.IP1.PROD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909921", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.IP2.TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909923", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI.T.BG01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909925", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHC.EXTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909927", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHC.EXTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909929", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-T-BG01.CHC.NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909931", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDILIVE.CHC.NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909932", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDITEST.CHC.NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909934", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-T-BG01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909936", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHFTP01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909937", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SR_STACK_01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909939", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-BG01.nlh.org", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909941", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-BG04.CHC.NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909943", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-BG04", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909944", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHC.EXTERNAL.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909946", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909948", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WEBSSO.MEDITECH.COM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909949", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WEBSSO2FA.MEDITECH.COM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909951", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.INTERNAL.PUSH_SERVER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909953", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.INTERNAL.TESTHUB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909954", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FIRECALL_JSC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909956", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FIRECALLSYSTEM_ENDPOINTS1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909957", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FIRECALLSYSTEM_ENDPOINTS2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909959", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKLINE.VPN.PEER2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909961", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKLINE.LDAP.INTERNAL2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909962", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_35.155.201.32", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909964", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BANDWIDTH_TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909966", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IS_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909968", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IS_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909969", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IT_COOR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909971", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IT_TECH1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909973", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIRAM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909975", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RYAN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909976", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NICK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909978", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "IT_TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909980", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P-BOARDROOM1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909981", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS08", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909983", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS21-iLO", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909985", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHADMINCENTER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909986", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP24", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909988", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SQL01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909990", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FAXSERVER.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909991", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHFUSION", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909993", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKUPEXEC01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909995", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ARCHIVE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909996", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PRINT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.909998", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBACKUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910000", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INTERLACETEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910001", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHMONITOR01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910003", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SANPHNHM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910005", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK_BCR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910006", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK_VISTA2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910008", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK_VISTA1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910010", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK_LCM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910012", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910013", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS31-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910016", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP21", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910018", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCITRIXGATEWAY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910020", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ST_NETADMIN2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910022", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DR_CECIL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910024", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IS_RAMANI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910026", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "US_LOGU_E9_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910028", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910029", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-BG13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910031", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHTESTMOBILE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910033", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA.NLH.ORG.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910034", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BANDWIDTH_TEST_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910036", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_192.168.253.161", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910038", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_172.16.41.10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910039", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Barracuda.Web.NLH.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910041", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Barracuda.Email.NLH.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910043", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910044", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH.PEER.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910046", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_216.41.86.228", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910048", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DMZ_TEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910049", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DUOTEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910051", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DUOTEST.NLH.ORG.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910053", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BARRACUDA.EMAIL.INSIDE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910054", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH.CORE.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910056", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DCDS.CORE.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910058", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "GPC_STACK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910059", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSSI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910061", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBRAUNPUMPS.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910063", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBRAUNPUMPS.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910064", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P-ITMGR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910066", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDIVATOR66838147", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910068", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDIVATOR66838143", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910069", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AMC.VPN.PEER.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910071", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.NEW.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910073", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.NEW.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910075", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ULN.VPN.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910076", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CISCOPRIME.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910078", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CISCOPRIMEINF", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910080", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MIS_TEST2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910081", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CISCONMON", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910083", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SYSLOGSERVER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910085", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA-QIE.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910086", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA.INTERLACE.PEER.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910088", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WLC1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910090", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "sendgrid.net.virus", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910091", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA.INTERLACE.PEER.EXTERNAL2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910093", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "love.explorethebest.com.spam.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910095", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "love.explorethebest.com.spam.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910096", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "love.explorethebest.com.spam.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910098", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CISCO.WSA.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910100", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HARRIET.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910101", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS32.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910103", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910105", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS19B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910106", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WILLYWONKA.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910108", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSYN01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910110", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSYN02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910111", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSYN03.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910113", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSYN04.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910115", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSP19APP.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910116", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS18C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910118", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS19C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910120", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS14.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910121", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS26D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910123", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DDPC.FIREALARM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910125", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCUIS16B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910126", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS17B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910128", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS19A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910130", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SUMMIT.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910131", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS25A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910133", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ONEVIEW.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910135", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DR1.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910136", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS26B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910138", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBACKUP02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910140", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KRONOSNEW.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910141", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.RAD.EXTERNAL.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910143", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BARRACUDA.LDAP.EXTERNAL.PEER.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910145", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BARRACUDA.LDAP.EXTERNAL.PEER.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910146", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BARRACUDA.LDAP.EXTERNAL.PEER.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910148", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "REYHEALTH.EXTERNAL.EXTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910150", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "REYHEALTH.EXTERNAL.EXTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910151", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHC.OPTUM.EXTERNAL.VPN.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910153", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS18D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910155", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "STREAMTASK.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910156", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "GPSUPPORT.VPN.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910158", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.AWSERVER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910160", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.AWSERVER.ILO", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910161", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.CTSCANNER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910163", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.CT.ADV.WS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910165", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.GE.MAMMO.INTERFACE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910166", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MAMMO.SHUTTLE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910168", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MRI.ALLIANCE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910170", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MUSE01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910171", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MUSE02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910173", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MUSE03", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910175", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MAMMO", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910176", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.NUCMEDCAMERA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910178", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.PETCTVIEWER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910180", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.PERTH.XRAY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910181", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.R.AND.F", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910953", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.ROOMA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910962", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.XELERIS.NM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910965", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910967", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CLEARWATER1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910970", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CLEARWATER2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910971", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "JELMENDORFSPAM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910973", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS25C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910975", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PROVMDAPP.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910977", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPROVMDORACLE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910978", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHMUSE01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910980", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHMUSE02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910982", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS16A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910984", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DESIGO.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910986", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910987", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910989", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910991", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIOUS01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910993", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPRTGPROBE04", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.910994", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10C", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911019", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS28", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911052", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911057", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911059", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS07.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911061", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NURSECALLAPP.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911063", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBRAUNPUMPS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911065", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BRAUNWEB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911066", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS09A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911068", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS09B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911070", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS09C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911072", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCISCO.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911074", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS13.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911075", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SQLTEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911077", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHMONITOR.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911079", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPRTG01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911081", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHKIWISYSLOG01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911082", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS17A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911084", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911086", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CITRIXSF.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911088", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHWEB01..NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911090", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AVAYACALLACCT.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911091", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSSI.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911093", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TEMPTRAK.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911095", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PRINT.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911097", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUICKCHARGE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911098", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH3M.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911100", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS19D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911102", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHAV01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911104", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS23A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911106", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS23B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911107", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS23C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911109", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS23D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911111", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDHCP01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911113", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS25B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911114", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS21.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911116", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911118", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS27.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911120", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911122", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE03.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911123", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KRONOSTEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911125", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE-TEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911127", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INTERLACETEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911129", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHINT-TEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911131", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS29.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911132", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHFUSION.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911134", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE-CCGHL7.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911136", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS31.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911138", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911140", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911141", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCA.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911143", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPRTGPROBE3.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911145", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911147", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CODONICS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911149", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDITEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911150", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CITRIXFS02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911152", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911154", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENTPRINT01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911156", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911158", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS18A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911159", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INTERLACE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911161", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA-QIE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911163", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS18B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911165", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHUTILITY.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911166", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCODONICS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911168", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHLICENSE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911170", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HPDMAN.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911172", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SCVMM.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911173", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS24A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911175", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS24B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911177", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS24C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911179", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS24D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911180", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS26A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911182", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ESICALLACCT26A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911184", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ESRS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911186", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDRFIRST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911188", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHELOCK.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911189", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS26C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911191", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "COBAS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911193", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PRADEV.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911195", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHADMINCENTER.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911197", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS28.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911198", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NURSECALLHD.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911200", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-iUV.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911202", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS30.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911204", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE-APP.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911205", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE-NXWEB.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911207", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Clearwater.External.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911209", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ASA01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911211", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ASA02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911212", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_172.16.201.35", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911214", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_192.168.178.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911216", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911218", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911220", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MMI.BILLING.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911222", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MMI.BILLING.INTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911226", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.MEDICOM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911228", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.AMBRA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911230", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.POWERSHARE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911232", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.CLOUD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911234", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Nuvodia.OneOncology.Cloud.External.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911236", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Nuvodia.OneOncology.Cloud.Internal.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911237", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_162.245.33.10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911239", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.NEW.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911241", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911243", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911244", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911246", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911248", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911250", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.911251", "validation_status": null}], "checkpoint_version": "2.0"}