{"session_id": "migration_1754345910", "timestamp": "2025-08-04T15:19:52.322956", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 0, "updated": 5, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost_WebServer", "✅ Updated host: TestHost_DatabaseServer", "✅ Updated host: TestHost_FileServer", "✅ Updated host: TestHost_PrintServer", "✅ Updated host: TestHost_BackupServer"], "duration_seconds": 39.46811509132385, "success_rate": 100.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork_LAN", "✅ Updated network: TestNetwork_DMZ", "✅ Updated network: TestNetwork_Management"], "duration_seconds": 24.48179006576538, "success_rate": 100.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 4, "created": 0, "updated": 4, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestService_HTTP_8080", "✅ Updated service: TestService_HTTPS_8443", "✅ Updated service: TestService_Database", "✅ Updated service: TestService_FTP"], "duration_seconds": 11.52721095085144, "success_rate": 100.0}}, "current_phase": "phase1_services", "phase_result": {"phase_name": "phase1_services", "total_objects": 4, "created": 0, "updated": 4, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestService_HTTP_8080", "✅ Updated service: TestService_HTTPS_8443", "✅ Updated service: TestService_Database", "✅ Updated service: TestService_FTP"], "duration_seconds": 11.52721095085144, "success_rate": 100.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "TestService_HTTP_8080", "object_id": "005056BF-7B88-0ed3-0000-017187313290", "message": "Protocol port object exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:19:43.802026", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "TestService_HTTPS_8443", "object_id": "005056BF-7B88-0ed3-0000-017187313309", "message": "Protocol port object exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:19:46.609327", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "TestService_Database", "object_id": "005056BF-7B88-0ed3-0000-017187313328", "message": "Protocol port object exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:19:49.429489", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "TestService_FTP", "object_id": "005056BF-7B88-0ed3-0000-017187313347", "message": "Protocol port object exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:19:52.322830", "validation_status": null}], "checkpoint_version": "2.0"}