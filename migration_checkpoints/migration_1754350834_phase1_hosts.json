{"session_id": "migration_1754350834", "timestamp": "2025-08-04T16:53:06.989761", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 100, "created": 100, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue"], "duration_seconds": 750.0046527385712, "success_rate": 100.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 100, "created": 100, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue"], "duration_seconds": 750.0046527385712, "success_rate": 100.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadSaratoga", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:40:44.522518", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadAmsMem", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:40:51.586767", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadStMarys", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:40:58.486000", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadSeton", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:41:05.481523", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadBellevue", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:41:12.555207", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "CITRIXFS02", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:41:19.441574", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "XENAPP30", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:41:26.357172", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC_C-iDRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:41:33.427694", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLHNAS12", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:41:40.681804", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC-D_iDRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:41:48.307456", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "XENAPP02", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:41:55.894752", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLHNAS11", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:42:03.492534", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC_A-iDRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:42:11.003355", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC_E-iDRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:42:18.565301", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC_D-NEWiSCSI", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:42:26.178154", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "UNITY-SDC_iSCSI", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:42:33.747246", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLH-WEB01-WS01", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:42:41.385254", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGICA", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:42:48.829011", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGICC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:42:56.031800", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGICD", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:43:03.117032", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGICE", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:43:10.152222", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGICB", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:43:17.217094", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGICF", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:43:24.261044", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGICG", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:43:31.368304", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "DICTATION02", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:43:38.463073", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MDILIVE.NLH.ORG", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:43:45.681191", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "P_FS_SEC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:43:53.085840", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "CTScanner", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:44:00.724741", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "<PERSON><PERSON><PERSON>", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:44:08.296369", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RandF", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:44:15.702271", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PetLinks1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:44:23.432289", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PetLinks2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:44:31.108264", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGICD3_DRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:44:38.825551", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "LIEBERT.2FL", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:44:46.202681", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Cardinal132", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:44:53.881246", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Cardinal133", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:45:01.550024", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Cardinal144", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:45:09.287227", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Cardinal145", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:45:16.953001", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Cardinal176", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:45:24.524737", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Cardinal177", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:45:32.211691", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Cardinal194", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:45:39.997826", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Cardinal195", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:45:47.612000", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "<PERSON><PERSON><PERSON>", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:45:55.207912", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "medinote2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:46:02.970920", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "medinote1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:46:10.600888", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN3.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:46:17.667929", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN5.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:46:24.898837", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN1.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:46:32.005467", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN4.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:46:39.264083", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN9.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:46:46.607156", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN10.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:46:53.748978", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN11.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:47:00.935365", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:47:08.127834", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:47:15.485924", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS3", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:47:22.760881", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS4", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:47:30.395504", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS5", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:47:38.093244", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLHEXCHANGE.NLH.ORG", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:47:45.886611", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLH-ISWEB_VIP_NETSCALER1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:47:53.577305", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:48:01.344110", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_CACHE", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:48:09.165554", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_STORE1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:48:16.719320", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_STORE2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:48:24.102152", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_STORE144", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:48:31.757288", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "ResnickPacs1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:48:39.228451", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "ResnickPACS2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:48:46.835312", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TeleRadPC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:48:54.451793", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "CatScan", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:49:02.299633", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PERTH_MRI", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:49:10.125569", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PETScanCT", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:49:17.943902", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "XELERIS", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:49:25.588527", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "INFINIA", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:49:33.221288", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "D5000", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:49:41.191743", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Ultrasound1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:49:49.221004", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Ultrasound2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:49:56.968551", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Ultrasound3", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:50:04.762533", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "KonicaJM", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:50:12.537023", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Konicardr1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:50:20.343392", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "KonicaRdr2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:50:28.049788", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "KonicaRdr3", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:50:35.773777", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_NEW", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:50:43.605885", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "US_LOGI_E9", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:50:51.197759", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk242", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:50:59.179790", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk243", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:51:07.181615", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk244", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:51:15.122268", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk245", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:51:22.821098", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk246", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:51:30.500100", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk247", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:51:38.359531", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalkPrime", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:51:46.283300", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalkSec", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:51:54.471575", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Spantel.Prod", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:52:01.638820", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "SpantelHL7.test", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:52:09.083766", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRXcenter2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:52:16.331963", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRXcenter3", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:52:23.096879", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRXcenter1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:52:30.292857", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRxChicago", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:52:37.462456", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRxDallas", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:52:44.601996", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MedentRemote", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:52:51.856174", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Medent.RPTS", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:52:59.144492", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "STpc.rtr", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:53:06.988786", "validation_status": null}], "checkpoint_version": "2.0"}