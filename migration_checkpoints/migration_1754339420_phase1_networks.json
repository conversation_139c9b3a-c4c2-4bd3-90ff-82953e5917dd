{"session_id": "migration_1754339420", "timestamp": "2025-08-04T13:30:30.606016", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0018858909606933594, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0004558563232421875, "success_rate": 0.0}}, "current_phase": "phase1_networks", "phase_result": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0004558563232421875, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TeleMedVT3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605533", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TelemedVT4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605538", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TelemedVT5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605541", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TeleMedVT1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605542", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Medent.VPN.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605544", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SMHApacsSUBNET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605546", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "pacs.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605547", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "PACS_VCE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605549", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "pacs.net_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605550", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Olympus.Inside.New", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605552", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "speculator", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605554", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GEserviceNET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605555", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Mill.PACS.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605557", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "DI.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605558", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "STUDENT_VLAN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605560", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "questlab", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605562", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "iPEOPLEremote", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605563", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "LAN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605565", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "RALSplusLAN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605566", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "PhilipsSupport", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605568", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "STRAT_SOL.NET.INTERNAL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605569", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MVOrtho.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605571", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "LAN_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605572", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MilleniumPACSnat", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605574", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MVOatJSC.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605575", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SENTRYDS.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605577", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SENTRYDS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605578", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "pacs.net-01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605580", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "LAN-01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605581", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MilleniumPACSnat-10.205.56.127", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605583", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "obj-10.205.56.128-10.205.56.255", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605584", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "obj_any", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605586", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "obj_any-03", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605587", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "NUVODIA_NETWORK_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605589", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GUEST_WLAN_NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605590", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GUEST_NETWORK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605592", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "VENDOR_WLAN_NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605593", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CREDITCARD_CAFE_EXTERNAL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605595", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CREDITCARD_CAFE2_EXTERNAL2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605596", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CREDITCARD_CAFE_EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605598", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "STRAT_SOL.NET.INTERNAL2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605599", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605601", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605602", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605604", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605605", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "QUEST.VPN.EXTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605607", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CHC.OPTUM.NAT.INTERNAL.SUB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605608", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605610", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605611", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "BARRACUDA.CLOUD.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605613", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605614", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605616", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GESUPPORT.INTERNAL.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605617", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CLEARWATER3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605619", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CLEARWATER4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605621", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "backblazeb2.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605622", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "HANYS.EXTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605624", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "HANYS.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605625", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "HANYS.EXTERNAL.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605627", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Clearwater.Internal.Peer.Range", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605628", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "NLH.Firewall.Range.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605630", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SSI.EXTERNAL.PEER.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605631", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MICROSOFTSTREAM.COM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.605632", "validation_status": null}], "checkpoint_version": "2.0"}