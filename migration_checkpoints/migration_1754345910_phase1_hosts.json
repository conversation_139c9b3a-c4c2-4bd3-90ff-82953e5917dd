{"session_id": "migration_1754345910", "timestamp": "2025-08-04T15:19:16.312469", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 0, "updated": 5, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost_WebServer", "✅ Updated host: TestHost_DatabaseServer", "✅ Updated host: TestHost_FileServer", "✅ Updated host: TestHost_PrintServer", "✅ Updated host: TestHost_BackupServer"], "duration_seconds": 39.46811509132385, "success_rate": 100.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 0, "updated": 5, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost_WebServer", "✅ Updated host: TestHost_DatabaseServer", "✅ Updated host: TestHost_FileServer", "✅ Updated host: TestHost_PrintServer", "✅ Updated host: TestHost_BackupServer"], "duration_seconds": 39.46811509132385, "success_rate": 100.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost_WebServer", "object_id": "005056BF-7B88-0ed3-0000-017187313138", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:18:44.981787", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost_DatabaseServer", "object_id": "005056BF-7B88-0ed3-0000-017187313157", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:18:53.107558", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost_FileServer", "object_id": "005056BF-7B88-0ed3-0000-017187313176", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:19:01.012805", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost_PrintServer", "object_id": "005056BF-7B88-0ed3-0000-017187313195", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:19:08.780701", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost_BackupServer", "object_id": "005056BF-7B88-0ed3-0000-017187313214", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:19:16.312195", "validation_status": null}], "checkpoint_version": "2.0"}