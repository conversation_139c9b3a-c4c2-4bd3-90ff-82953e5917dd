{"session_id": "migration_1754345222", "timestamp": "2025-08-04T15:07:46.869668", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 5, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created host: TestHost_WebServer", "✅ Created host: TestHost_DatabaseServer", "✅ Created host: TestHost_FileServer", "✅ Created host: TestHost_PrintServer", "✅ Created host: TestHost_BackupServer"], "duration_seconds": 38.063902139663696, "success_rate": 0.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 5, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created host: TestHost_WebServer", "✅ Created host: TestHost_DatabaseServer", "✅ Created host: TestHost_FileServer", "✅ Created host: TestHost_PrintServer", "✅ Created host: TestHost_BackupServer"], "duration_seconds": 38.063902139663696, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "created", "object_type": "HostObject", "object_name": "TestHost_WebServer", "object_id": "005056BF-7B88-0ed3-0000-017187313138", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:07:16.380428", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "TestHost_DatabaseServer", "object_id": "005056BF-7B88-0ed3-0000-017187313157", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:07:24.054343", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "TestHost_FileServer", "object_id": "005056BF-7B88-0ed3-0000-017187313176", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:07:31.565463", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "TestHost_PrintServer", "object_id": "005056BF-7B88-0ed3-0000-017187313195", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:07:39.372064", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "TestHost_BackupServer", "object_id": "005056BF-7B88-0ed3-0000-017187313214", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:07:46.869296", "validation_status": null}], "checkpoint_version": "2.0"}