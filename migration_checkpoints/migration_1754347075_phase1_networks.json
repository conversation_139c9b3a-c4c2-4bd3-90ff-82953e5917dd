{"session_id": "migration_1754347075", "timestamp": "2025-08-04T15:49:24.637149", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 100, "created": 59, "updated": 0, "failed": 41, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue", "❌ Failed host: XENAPP02 - Create failed: Data validation failed: No data provided", "❌ Failed host: NLHNAS11 - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGIC_A-iDRAC - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGIC_E-iDRAC - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGIC_D-NEWiSCSI - Create failed: Data validation failed: No data provided", "❌ Failed host: UNITY-SDC_iSCSI - Create failed: Data validation failed: No data provided", "❌ Failed host: NLH-WEB01-WS01 - <PERSON><PERSON> failed: Data validation failed: No data provided", "❌ Failed host: MAGICA - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGICC - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGICD - Create failed: Data validation failed: No data provided"], "duration_seconds": 597.1533288955688, "success_rate": 59.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 20, "created": 1, "updated": 0, "failed": 19, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Create failed: Data validation failed: No data provided", "❌ Failed network: TelemedVT4 - Create failed: Data validation failed: No data provided", "❌ Failed network: TelemedVT5 - Create failed: Data validation failed: No data provided", "❌ Failed network: TeleMedVT1 - Create failed: Data validation failed: No data provided", "❌ Failed network: Medent.VPN.net - Create failed: Data validation failed: No data provided", "❌ Failed network: SMHApacsSUBNET - Create failed: Data validation failed: No data provided", "❌ Failed network: pacs.net - Create failed: Data validation failed: No data provided", "❌ Failed network: PACS_VCE - Create failed: Data validation failed: No data provided", "❌ Failed network: pacs.net_1 - Create failed: Data validation failed: No data provided", "❌ Failed network: Olympus.Inside.New - Create failed: Data validation failed: No data provided", "✅ Updated network: LAN"], "duration_seconds": 89.65031123161316, "success_rate": 5.0}}, "current_phase": "phase1_networks", "phase_result": {"phase_name": "phase1_networks", "total_objects": 20, "created": 1, "updated": 0, "failed": 19, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Create failed: Data validation failed: No data provided", "❌ Failed network: TelemedVT4 - Create failed: Data validation failed: No data provided", "❌ Failed network: TelemedVT5 - Create failed: Data validation failed: No data provided", "❌ Failed network: TeleMedVT1 - Create failed: Data validation failed: No data provided", "❌ Failed network: Medent.VPN.net - Create failed: Data validation failed: No data provided", "❌ Failed network: SMHApacsSUBNET - Create failed: Data validation failed: No data provided", "❌ Failed network: pacs.net - Create failed: Data validation failed: No data provided", "❌ Failed network: PACS_VCE - Create failed: Data validation failed: No data provided", "❌ Failed network: pacs.net_1 - Create failed: Data validation failed: No data provided", "❌ Failed network: Olympus.Inside.New - Create failed: Data validation failed: No data provided", "✅ Updated network: LAN"], "duration_seconds": 89.65031123161316, "success_rate": 5.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TeleMedVT3", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:47:59.635587", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TelemedVT4", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:03.916051", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TelemedVT5", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:08.120574", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TeleMedVT1", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:12.419366", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Medent.VPN.net", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:16.778453", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SMHApacsSUBNET", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:21.088240", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "pacs.net", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:25.351587", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "PACS_VCE", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:29.615774", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "pacs.net_1", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:33.904471", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Olympus.Inside.New", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:38.134872", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "speculator", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:42.377985", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GEserviceNET", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:46.705893", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Mill.PACS.NET", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:51.086959", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "DI.NET", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:55.377755", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "STUDENT_VLAN", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:48:59.588353", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "questlab", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:03.933191", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "iPEOPLEremote", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:08.277471", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "LAN", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:15.907843", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "RALSplusLAN", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:20.299094", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "PhilipsSupport", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:24.636697", "validation_status": null}], "checkpoint_version": "2.0"}