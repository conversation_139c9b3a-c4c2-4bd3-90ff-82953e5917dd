{"session_id": "migration_1754346573", "timestamp": "2025-08-04T15:30:25.717809", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 2, "created": 2, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created host: TestHost_Server1", "✅ Created host: TestHost_Server2"], "duration_seconds": 14.078372955322266, "success_rate": 100.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 1, "created": 1, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created network: TestNetwork_Subnet"], "duration_seconds": 7.548114061355591, "success_rate": 100.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 1, "created": 1, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created service: TestService_HTTP"], "duration_seconds": 6.128085136413574, "success_rate": 100.0}, "phase1_access_rules": {"phase_name": "phase1_access_rules", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule_AllowHTTP - Access rule creation failed - no ID returned. Result: None"], "duration_seconds": 11.731813907623291, "success_rate": 0.0}}, "current_phase": "phase1_access_rules", "phase_result": {"phase_name": "phase1_access_rules", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule_AllowHTTP - Access rule creation failed - no ID returned. Result: None"], "duration_seconds": 11.731813907623291, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "TestRule_AllowHTTP", "object_id": null, "message": "Access rule creation failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:30:25.717504", "validation_status": null}], "checkpoint_version": "2.0"}