{"session_id": "migration_1754346573", "timestamp": "2025-08-04T15:29:54.081789", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 2, "created": 2, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created host: TestHost_Server1", "✅ Created host: TestHost_Server2"], "duration_seconds": 14.078372955322266, "success_rate": 100.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 2, "created": 2, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created host: TestHost_Server1", "✅ Created host: TestHost_Server2"], "duration_seconds": 14.078372955322266, "success_rate": 100.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "created", "object_type": "HostObject", "object_name": "TestHost_Server1", "object_id": "005056BF-7B88-0ed3-0000-017187339569", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:29:47.077152", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "TestHost_Server2", "object_id": "005056BF-7B88-0ed3-0000-017187339588", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:29:54.081676", "validation_status": null}], "checkpoint_version": "2.0"}