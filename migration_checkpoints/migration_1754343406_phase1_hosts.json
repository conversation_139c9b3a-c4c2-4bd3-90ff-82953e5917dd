{"session_id": "migration_1754343406", "timestamp": "2025-08-04T14:37:10.268133", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost1", "✅ Updated host: TestHost2", "✅ Updated host: TestHost3"], "duration_seconds": 21.46864628791809, "success_rate": 0.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost1", "✅ Updated host: TestHost2", "✅ Updated host: TestHost3"], "duration_seconds": 21.46864628791809, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost1", "object_id": "005056BF-7B88-0ed3-0000-017181451868", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:36:55.928763", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost2", "object_id": "005056BF-7B88-0ed3-0000-017181212231", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:37:03.098457", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost3", "object_id": "005056BF-7B88-0ed3-0000-017181212250", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:37:10.267669", "validation_status": null}], "checkpoint_version": "2.0"}