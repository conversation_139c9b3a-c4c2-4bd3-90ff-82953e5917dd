{"session_id": "migration_1754346953", "timestamp": "2025-08-04T15:37:11.461192", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 10, "created": 10, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue"], "duration_seconds": 75.28859233856201, "success_rate": 100.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 10, "created": 10, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue"], "duration_seconds": 75.28859233856201, "success_rate": 100.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadSaratoga", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:36:05.778013", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadAmsMem", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:36:12.875684", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadStMarys", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:36:19.992478", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadSeton", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:36:27.380794", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadBellevue", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:36:34.429985", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "CITRIXFS02", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:36:41.527502", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "XENAPP30", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:36:48.646222", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC_C-iDRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:36:56.465353", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLHNAS12", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:37:03.855466", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC-D_iDRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:37:11.460862", "validation_status": null}], "checkpoint_version": "2.0"}