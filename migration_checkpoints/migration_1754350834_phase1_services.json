{"session_id": "migration_1754350834", "timestamp": "2025-08-04T16:56:21.993270", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 100, "created": 100, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue"], "duration_seconds": 750.0046527385712, "success_rate": 100.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 20, "created": 20, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TeleMedVT3", "✅ Updated network: TelemedVT4", "✅ Updated network: TelemedVT5", "✅ Updated network: TeleMedVT1", "✅ Updated network: Medent.VPN.net"], "duration_seconds": 163.84694385528564, "success_rate": 100.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 10, "created": 10, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Updated service: obj-tcp-eq-80", "✅ Updated service: obj-tcp-eq-15002", "✅ Updated service: obj-tcp-eq-15331", "✅ Updated service: obj-tcp-eq-3389", "✅ Updated service: obj-tcp-eq-2222"], "duration_seconds": 31.151252031326294, "success_rate": 100.0}}, "current_phase": "phase1_services", "phase_result": {"phase_name": "phase1_services", "total_objects": 10, "created": 10, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Updated service: obj-tcp-eq-80", "✅ Updated service: obj-tcp-eq-15002", "✅ Updated service: obj-tcp-eq-15331", "✅ Updated service: obj-tcp-eq-3389", "✅ Updated service: obj-tcp-eq-2222"], "duration_seconds": 31.151252031326294, "success_rate": 100.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-80", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:55:54.626414", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-15002", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:55:57.737950", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-15331", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:56:00.727696", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-3389", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:56:03.780014", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-2222", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:56:06.863815", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-6544", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:56:09.862173", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-2020", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:56:12.916177", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-23", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:56:15.988321", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-15031", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:56:18.975361", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-5631", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:56:21.992994", "validation_status": null}], "checkpoint_version": "2.0"}