{"session_id": "migration_1754339420", "timestamp": "2025-08-04T13:30:30.610464", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0018858909606933594, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0004558563232421875, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed service: obj-tcp-eq-80 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15002 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15331 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-3389 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2222 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-6544 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2020 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-23 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15031 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-5631 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0003209114074707031, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed object_group: Medivators - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.GROUP.NEW - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.PEER.NET1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: FoodService - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DI.Net.Group - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: Cardinal - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: medinotes - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0004048347473144531, "success_rate": 0.0}, "phase1_service_groups": {"phase_name": "phase1_service_groups", "total_objects": 66, "created": 0, "updated": 0, "failed": 66, "skipped": 0, "details": ["❌ Failed service_group: PaceGlobalgrp - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeservice - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeserviceUDP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: QUEST - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: citrixXML - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GatewayDMZ - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: RSA - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: HFMBoces - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEinbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEoutbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0003330707550048828, "success_rate": 0.0}}, "current_phase": "phase1_service_groups", "phase_result": {"phase_name": "phase1_service_groups", "total_objects": 66, "created": 0, "updated": 0, "failed": 66, "skipped": 0, "details": ["❌ Failed service_group: PaceGlobalgrp - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeservice - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeserviceUDP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: QUEST - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: citrixXML - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GatewayDMZ - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: RSA - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: HFMBoces - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEinbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEoutbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0003330707550048828, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PaceGlobalgrp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610055", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "timeservice", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610057", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "timeserviceUDP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610059", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "QUEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610061", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "citrixXML", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610062", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "GatewayDMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610064", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "RSA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610065", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "HFMBoces", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610067", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "GEinbound", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610068", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "GEoutbound", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610070", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PetLinks", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610071", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "TeleVideoTcpUdp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610073", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "GEPACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610074", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ExchangePorts", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610075", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PrintPorts", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610077", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PrinterPorts", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610078", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "IPSEC_ISAKMP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610079", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "EmdeonPorts", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610081", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "in_any_to_out_any_tcp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610082", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "RAMSOFTports", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610084", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CoreFTP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610085", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PhilipsPacs", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610087", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Pacs", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610088", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "NexTalk1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610090", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "NexTalkTcpUdp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610091", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CastleSys", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610093", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "FTPpsv5500", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610094", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Labcorp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610095", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Labcorptcp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610097", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "IVANStcp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610098", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "IVANSudp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610100", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "<PERSON>ph<PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610101", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "any_in_udp_to_any_out", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610102", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "SophosMail", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610104", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "BobSFTP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610105", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Impulse.UDP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610107", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ImpulseTCP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610108", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "TEMP_TRACK1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610109", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "<PERSON><PERSON><PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610111", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ALLSCRIPT_PORTAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610112", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "testgroup", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610114", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ALBANYMEDPACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610115", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Guest_Wireless", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610116", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "SOPHOSFTP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610118", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "BOCES_IPADS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610119", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610121", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "IMO_Ports", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610122", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "TeamViewer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610123", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CCD_MESSAGING", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610125", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Apple_Services", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610126", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ProviderOrg", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610127", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "MAIL_VIRUS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610129", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "STAT_RAD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610130", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "StatRadService", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610131", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PAT_ACCTS_FTP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610133", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "UDP_TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610134", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CE000SVC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610135", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CE2000", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610137", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "m<PERSON>sson", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610138", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "DM_INLINE_SERVICE_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610140", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "MEDENT_TELEMED", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610141", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PHINMS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610142", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "SALUCRO_FTP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610144", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "QUEST_SFTP_NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610145", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "REYHEALTH.EXTERNAL.PORT.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610146", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "DM_INLINE_SERVICE_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.610148", "validation_status": null}], "checkpoint_version": "2.0"}