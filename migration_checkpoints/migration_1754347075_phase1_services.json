{"session_id": "migration_1754347075", "timestamp": "2025-08-04T15:49:53.983241", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 100, "created": 59, "updated": 0, "failed": 41, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue", "❌ Failed host: XENAPP02 - Create failed: Data validation failed: No data provided", "❌ Failed host: NLHNAS11 - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGIC_A-iDRAC - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGIC_E-iDRAC - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGIC_D-NEWiSCSI - Create failed: Data validation failed: No data provided", "❌ Failed host: UNITY-SDC_iSCSI - Create failed: Data validation failed: No data provided", "❌ Failed host: NLH-WEB01-WS01 - <PERSON><PERSON> failed: Data validation failed: No data provided", "❌ Failed host: MAGICA - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGICC - Create failed: Data validation failed: No data provided", "❌ Failed host: MAGICD - Create failed: Data validation failed: No data provided"], "duration_seconds": 597.1533288955688, "success_rate": 59.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 20, "created": 1, "updated": 0, "failed": 19, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Create failed: Data validation failed: No data provided", "❌ Failed network: TelemedVT4 - Create failed: Data validation failed: No data provided", "❌ Failed network: TelemedVT5 - Create failed: Data validation failed: No data provided", "❌ Failed network: TeleMedVT1 - Create failed: Data validation failed: No data provided", "❌ Failed network: Medent.VPN.net - Create failed: Data validation failed: No data provided", "❌ Failed network: SMHApacsSUBNET - Create failed: Data validation failed: No data provided", "❌ Failed network: pacs.net - Create failed: Data validation failed: No data provided", "❌ Failed network: PACS_VCE - Create failed: Data validation failed: No data provided", "❌ Failed network: pacs.net_1 - Create failed: Data validation failed: No data provided", "❌ Failed network: Olympus.Inside.New - Create failed: Data validation failed: No data provided", "✅ Updated network: LAN"], "duration_seconds": 89.65031123161316, "success_rate": 5.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 10, "created": 0, "updated": 0, "failed": 10, "skipped": 0, "details": ["❌ Failed service: obj-tcp-eq-80 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-15002 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-15331 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-3389 - <PERSON><PERSON> failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-2222 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-6544 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-2020 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-23 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-15031 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-5631 - <PERSON><PERSON> failed: Data validation failed: No data provided"], "duration_seconds": 29.345184087753296, "success_rate": 0.0}}, "current_phase": "phase1_services", "phase_result": {"phase_name": "phase1_services", "total_objects": 10, "created": 0, "updated": 0, "failed": 10, "skipped": 0, "details": ["❌ Failed service: obj-tcp-eq-80 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-15002 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-15331 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-3389 - <PERSON><PERSON> failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-2222 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-6544 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-2020 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-23 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-15031 - Create failed: Data validation failed: No data provided", "❌ Failed service: obj-tcp-eq-5631 - <PERSON><PERSON> failed: Data validation failed: No data provided"], "duration_seconds": 29.345184087753296, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-80", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:27.705296", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-15002", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:30.498810", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-15331", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:33.338005", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-3389", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:36.143367", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-2222", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:38.987382", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-6544", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:41.808849", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-2020", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:44.827133", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-23", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:47.843223", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-15031", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:50.884829", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-5631", "object_id": null, "message": "Create failed: Data validation failed: No data provided", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:49:53.983012", "validation_status": null}], "checkpoint_version": "2.0"}