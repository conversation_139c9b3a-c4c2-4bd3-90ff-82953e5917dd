{"session_id": "migration_1754339420", "timestamp": "2025-08-04T13:30:30.608829", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0018858909606933594, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0004558563232421875, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed service: obj-tcp-eq-80 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15002 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15331 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-3389 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2222 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-6544 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2020 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-23 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15031 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-5631 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0003209114074707031, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed object_group: Medivators - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.GROUP.NEW - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.PEER.NET1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: FoodService - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DI.Net.Group - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: Cardinal - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: medinotes - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0004048347473144531, "success_rate": 0.0}}, "current_phase": "phase1_object_groups", "phase_result": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed object_group: Medivators - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.GROUP.NEW - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.PEER.NET1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: FoodService - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DI.Net.Group - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: Cardinal - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: medinotes - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0004048347473144531, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Medivators", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608265", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA.INTERNAL.GROUP.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608268", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA.INTERNAL.PEER.NET1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608270", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608271", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608273", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FoodService", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608275", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DI.Net.Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608276", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "STRATEGICSOLUTIONS.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608278", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "<PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608279", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "medinotes", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608281", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CitrixServers.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608283", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MilleniumPACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608284", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ExchangeServers", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608286", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "TeleMedVT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608287", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "PacsServers", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608289", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MDI.OUT.<PERSON>ow", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608291", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "eRXdataCenters", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608292", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Medent.Interface", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608294", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SMHA.RAD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608295", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "RAD.PACS.READ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608297", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SOPHOS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608298", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CitrixServers1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608299", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CitrixServers1_ref", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608301", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MVO_Allow_OUTBOUND_Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608302", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ProvationServers", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608304", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "AAI.NYOH.PACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608305", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Dolby_OUT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608306", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Dolby_Servers", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608308", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Healthtouch.out", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608309", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FoodSVC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608311", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ALBANYPACS.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608312", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HIXNY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608313", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Olympus.inside.group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608315", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MDI_Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608316", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Brian_DHCP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608317", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Schumacher.Inside", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608319", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENTHQ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608320", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT_GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608322", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "WINDOWS_XP_DENY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608323", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "APPLE.OUT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608324", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ProviderOrg.External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608326", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CITRIX_EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608327", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CITRIXGATEWAY.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608329", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CITRIX_INTERNAL_TO_DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608330", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MIS_TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608332", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MARKETO_SPAMMER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608333", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ENDOWORKS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608335", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CE2000.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608336", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CE2000.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608337", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_24.97.36.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608339", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_65.114.41.136", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608340", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_12.39.198.49", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608341", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_173.84.224.94", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608343", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_12.152.123.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608344", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HIXNY.MBMS.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608345", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA_VPN_NLH_PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608347", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CLEARWATERTEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608348", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HIXNY.INTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608350", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MDI.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608351", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NYOH.INTERNAL.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608352", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA.VPN.SENDPOINT.MASTER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608354", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608355", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608356", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608358", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SMHA.RAD.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608359", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608361", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT.NIMBLE.OPENVPN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608362", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608363", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT.NIMBLE.OPENVPN.INSIDE.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608365", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "TCPUDP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608366", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "EXPANSE_VLANS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608368", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SmartNet_Devices", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608369", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Domain.Controllers.Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608370", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Quest.NLH2Quest.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608372", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608373", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CHANGE.HEALTHCARE.NLH.INTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608374", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CHC.EXTERNAL.NETWORK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608376", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "PHINMS.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608377", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLI.INTERNAL.NAT.CHC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608379", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLI-BG-GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608380", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608381", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "QUEST.VPN.INTERNAL.GROUP.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608383", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "WEBSSO.MEDITECH.COM.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608384", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "BACKLINE.LDAP.NLH.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608386", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FIRECALLSYSTEM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608387", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FIRECALLSYSTEM__ENDPOINTS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608388", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "IT_DEPT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608390", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FULL_PORT_ACCESS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608391", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CISCO_INTERNAL_2_EXTERNAL_ACL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608392", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HEALTHTOUCH.NLH.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608394", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HEALTHTOUCH.PEER.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608395", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Greycastle_Testing_External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608397", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "LINKBG.SPAM.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608398", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "love.explorethebest.com.spam.group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608400", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.ACRONIS.GROUP.INSIDE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608401", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.ACRONIS.GROUP.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608402", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "BARRACUDA.LDAP.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608404", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "REYHEALTH.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608405", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "EMAIL.BLACKLIST.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608407", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.DI.GEDEVICES", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608408", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608409", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "blackblazeb2.goup", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608411", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HAYNS.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608412", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608413", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Incident.External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608415", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.Firewall.Internal.Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608416", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Clearwater.Internal.Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608417", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608419", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SSI.EXTERNAL.PEER.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608420", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "BANDWIDTH.TEST.GROUP.OUTSIDE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608422", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FRESHWORKS.EXCLUSIONS.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.608423", "validation_status": null}], "checkpoint_version": "2.0"}