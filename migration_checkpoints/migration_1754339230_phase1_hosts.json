{"session_id": "migration_1754339230", "timestamp": "2025-08-04T13:27:20.502172", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0015249252319335938, "success_rate": 0.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0015249252319335938, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadSaratoga", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499475", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadAmsMem", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499483", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadStMarys", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499486", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadSeton", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499488", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadBellevue", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499490", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CITRIXFS02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499492", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP30", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499493", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_C-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499495", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHNAS12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499496", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC-D_iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499498", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499500", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHNAS11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499501", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_A-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499503", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_E-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499504", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_D-NEWiSCSI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499506", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "UNITY-SDC_iSCSI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499507", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-WEB01-WS01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499509", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499510", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499512", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499514", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499515", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499517", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICF", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499519", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499520", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DICTATION02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499522", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDILIVE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499524", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_FS_SEC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499525", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CTScanner", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499527", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON><PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499528", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RandF", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499530", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PetLinks1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499532", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PetLinks2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499533", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICD3_DRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499535", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LIEBERT.2FL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499536", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal132", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499538", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal133", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499539", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal144", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499541", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal145", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499542", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal176", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499544", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal177", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499545", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal194", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499547", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal195", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499549", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON><PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499550", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "medinote2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499552", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "medinote1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499553", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN3.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499555", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN5.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499556", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN1.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499558", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN4.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499559", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN9.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499562", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN10.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499563", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN11.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499565", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499566", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499568", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499570", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499571", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499573", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHEXCHANGE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499574", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB_VIP_NETSCALER1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499576", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499577", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_CACHE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499579", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_STORE1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499580", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_STORE2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499582", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_STORE144", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499584", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ResnickPacs1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499585", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ResnickPACS2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499587", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleRadPC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499588", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CatScan", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499590", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PERTH_MRI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499591", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PETScanCT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499593", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XELERIS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499594", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INFINIA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499596", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "D5000", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499597", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ultrasound1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499599", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ultrasound2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499600", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ultrasound3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499602", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KonicaJM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499603", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Konicardr1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499608", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KonicaRdr2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499609", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KonicaRdr3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499611", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499612", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "US_LOGI_E9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499614", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk242", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499615", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk243", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499617", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk244", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499618", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk245", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499620", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk246", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499621", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk247", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499623", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalkPrime", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499624", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalkSec", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499639", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Spantel.Prod", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499640", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SpantelHL7.test", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499642", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRXcenter2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499644", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRXcenter3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499646", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRXcenter1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499647", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRxChicago", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499649", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRxDallas", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499650", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MedentRemote", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499652", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Medent.RPTS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499653", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "STpc.rtr", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499655", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "spc.rtr", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499656", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ppc.pix", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499658", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.ps1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499659", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.ps2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499661", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.ps3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499663", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.ps4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499664", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.syn1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499666", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.syn2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499668", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.orpc1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499669", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.orpc2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499671", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.orpc3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499673", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499674", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499676", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499677", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.KPServer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499679", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499680", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "smha.mammo", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499682", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "smha.pacsed30", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499683", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "smha.pacrd06", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499685", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499687", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499688", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SHMA.read7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499690", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499691", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499693", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499694", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.Synapse.Dest", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499696", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P-DI-MGR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499697", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_READ3_NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499699", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_CIO1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499701", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_DI_NUMED", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499703", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAMMO40", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499704", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MOMMO41", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499706", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "phil<PERSON><PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499707", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSP19WEB.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499709", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499710", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499712", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PCC_BILL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499713", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499715", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499716", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499718", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SOPHOSEMAIL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499719", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SOPHOSWEB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499721", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN6.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499722", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ORTIZ_LT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499724", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "p_mis_netadmin", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499725", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_OR3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499727", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_OR1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499728", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_OR2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499730", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_DI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499731", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDC1_IPMI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499733", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDC2_IPMI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499734", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AAI.120", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499736", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AAI.124", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499737", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AAI.125", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499739", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AAI.52", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499740", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INTERLACE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499742", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHUTILITY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499744", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHTEST01-NIC2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499745", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BPC-UPS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499747", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ALBANYMED.IN.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499748", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ALBANYMED.IN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499750", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "hixny.com_integration", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499751", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "hixny.com_prod", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499753", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "webservices.hixny.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499754", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDITEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499756", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499757", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT03", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499759", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB_VIRTUALIP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499760", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB_VIRTUALIP_NETSCALER2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499762", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT05", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499763", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PHA_WS3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499765", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PHA_WS2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499766", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_MR_SCAN1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499768", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "easyeeg", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499769", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VENUE50_p_pacs_cdburn", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499771", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "integration.hixny.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499772", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Hixney.net_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499774", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CITRIX_STOREFRONT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499775", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P-IT-MGR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499777", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETSCALER.VPX", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499778", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETSCALER.WEB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499780", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETSCALERSUBNETIP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499781", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDC01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499783", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDC02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499784", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_CISCO_01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499786", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "p_mis_netadmin2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499787", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Direct.Hixny.Com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499789", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Healthstream.SMPT.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499790", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Hixny.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499792", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Hixny.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499793", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "statrad.hl7.test", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499795", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_FIN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499796", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHENDO01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499798", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHENDO01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499800", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ENDOWORKS02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499801", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ENDOWORKS03", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499803", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IS_PACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499804", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "retsolinc2.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499806", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "retsolinc3.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499807", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SophosMailExt", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499809", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "IRIS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499810", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "smtp.biz.rr.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499812", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Hypertype", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499813", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MVP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499815", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LeaderHFTPsite", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499816", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LeaderHFTPsite2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499818", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "stentor.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499819", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TOGARM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499821", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Infotrak", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499822", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "sftp.lifethc.org", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499824", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleVideo1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499825", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Televid2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499827", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CONNECTPLUS01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499828", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VeriquestPC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499830", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VeriquestSite", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499831", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT_PORTAL_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499833", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HYPER-_REPLICA_BROKER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499834", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Sodexho", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499836", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Provation-out", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499838", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VeriquestServer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499839", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499841", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "IMO_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499843", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WWW.UPTODATE.COM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499844", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP22", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499846", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HYPER-V_CLUSTER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499847", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENTPORTAL.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499849", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "remote.nlh.org", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499850", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mail.nlh.org", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499852", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DIRECT.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499853", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleMed_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499855", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDI.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499856", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCISCO", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499858", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LabCorp3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499859", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LabCorpDev", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499861", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LabCorpProd", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499862", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TheOutsourceGroup", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499864", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleradIT_Millenium1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499865", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleradIT_Millenium2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499867", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FastChart.Inside", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499868", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ellis.inside", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499870", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "STATRAD.DR.SVR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499872", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHTEST01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499873", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH.ORG.EXTERNAL.FORMS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499875", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-************", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499876", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-***********", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499878", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-***********", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499879", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-************", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499881", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-***********", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499882", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-************", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499884", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-************", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499885", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ellis.Peer.New", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499887", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH.PEER.INTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499888", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Medent.Peer.New.", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499890", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.MBMS.MILLENIUMBILLING.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499891", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499893", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MCKESSON.MC.PHARM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499894", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "newsync3.mkesson.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499896", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-0.0.0.0", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499897", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.pacs1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499899", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.pacs2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499901", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.pacs3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499902", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS.VCE1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499904", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS.VCE2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499905", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS.VCE3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499906", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS.VCE4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499908", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT_NAS_INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499909", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499910", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499912", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PDX.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499913", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PDX.External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499915", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.PEER.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499916", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.INTERNAL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499917", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XCHANGEWORX.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499919", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETSCALER.NLHRESTAPI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499920", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA_VPN_NLH_PEER1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499921", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA_VPN_NLH_PEER2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499923", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FIREPOWER_VM_ESXI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499924", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.READ.10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499925", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ESRS_EMC_VIRTUAL_APPLIANCE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499927", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499928", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499929", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RESTFULAPI.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499931", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499932", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499933", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499935", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499936", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499937", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_MIS52_DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499939", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDITEST_SENDTRYDS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499940", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP25", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499941", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "skype.nlh.org_external", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499943", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "st_netadmin", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499944", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.RAD.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499945", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MVO_AMST_PEER_NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499947", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "GUEST_INTERFACE_EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499948", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VENDOR_EXTERNAL_INTERFACE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499950", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "p_mis_netadmin.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499951", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB.DMZVR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499952", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AMC.PACS.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499954", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BRIAN_DHCP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499955", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BPC.External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499956", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_MIS_CISCOMON", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499958", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP17", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499959", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP18", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499960", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP19", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499962", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_MIS52.WAYNE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499963", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETADMIN.DMZ.TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499964", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "EUGENE10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499966", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDITECHAPIVIP1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499967", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDITECHAPIVIP2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499968", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "StratSolution.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499970", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PHA_PDX1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499971", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPRTG01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499973", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RCARE-SERVER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499974", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDMZ01_SWITCH", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499975", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499977", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PRTG.NLH.ORG.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499978", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKLINE.VPN.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499979", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "UNITEDLABNETWORK.VPN.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499981", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_18.204.173.205", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499982", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKLINE.LDAP.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499983", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT.NIMBLE.INSIDE.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499985", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499986", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499988", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ROBOT_GE_VOT_TRAIN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499989", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BILL_BAIRD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499990", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS29-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499992", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DOLBEY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499993", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DOLBEYTEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499995", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH_DCDS_9300s", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499996", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Schumacher.Inside1.new.ADTPROD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499997", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Schumacher.Inside2.new.ADTTEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.499999", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Schumacher.VPN.Peer.New", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500000", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT-EXPORT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500002", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUEST.VPN.PEER.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500003", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUEST.VPN.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500004", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500006", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.PEER.INTERNAL.TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500007", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.PEER.INTERNAL.PROD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500008", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSP19OFCWEB.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500010", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENTPORTAL.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500011", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mtrestexpapis-live01.nlh.org.external", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500012", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mtrestexpapis-test01.nlh.org.external", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500014", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mtrestexpapis-test01.nlh.org.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500015", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mtrestexpapis-live01.nlh.org.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500016", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500018", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.IP1.PROD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500019", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.IP2.TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500020", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI.T.BG01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500022", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHC.EXTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500023", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHC.EXTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500024", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-T-BG01.CHC.NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500026", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDILIVE.CHC.NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500027", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDITEST.CHC.NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500028", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-T-BG01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500030", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHFTP01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500031", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SR_STACK_01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500033", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-BG01.nlh.org", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500034", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-BG04.CHC.NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500035", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-BG04", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500037", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHC.EXTERNAL.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500038", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500039", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WEBSSO.MEDITECH.COM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500041", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WEBSSO2FA.MEDITECH.COM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500042", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.INTERNAL.PUSH_SERVER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500043", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.INTERNAL.TESTHUB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500045", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FIRECALL_JSC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500046", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FIRECALLSYSTEM_ENDPOINTS1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500047", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FIRECALLSYSTEM_ENDPOINTS2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500049", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKLINE.VPN.PEER2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500050", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKLINE.LDAP.INTERNAL2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500051", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_35.155.201.32", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500053", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BANDWIDTH_TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500054", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IS_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500055", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IS_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500057", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IT_COOR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500058", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IT_TECH1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500059", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIRAM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500061", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RYAN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500062", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NICK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500063", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "IT_TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500065", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P-BOARDROOM1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500066", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS08", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500067", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS21-iLO", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500069", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHADMINCENTER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500070", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP24", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500072", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SQL01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500073", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FAXSERVER.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500074", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHFUSION", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500076", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKUPEXEC01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500077", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ARCHIVE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500078", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PRINT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500080", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBACKUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500081", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INTERLACETEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500082", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHMONITOR01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500084", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SANPHNHM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500085", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK_BCR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500086", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK_VISTA2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500088", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK_VISTA1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500089", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK_LCM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500090", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500092", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS31-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500093", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP21", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500094", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCITRIXGATEWAY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500096", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ST_NETADMIN2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500097", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DR_CECIL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500099", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IS_RAMANI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500100", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "US_LOGU_E9_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500101", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500103", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-BG13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500104", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHTESTMOBILE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500105", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA.NLH.ORG.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500107", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BANDWIDTH_TEST_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500108", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_192.168.253.161", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500109", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_172.16.41.10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500111", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Barracuda.Web.NLH.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500112", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Barracuda.Email.NLH.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500113", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500115", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH.PEER.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500116", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_216.41.86.228", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500117", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DMZ_TEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500119", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DUOTEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500120", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DUOTEST.NLH.ORG.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500122", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BARRACUDA.EMAIL.INSIDE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500123", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH.CORE.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500124", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DCDS.CORE.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500126", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "GPC_STACK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500127", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSSI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500129", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBRAUNPUMPS.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500130", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBRAUNPUMPS.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500131", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P-ITMGR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500133", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDIVATOR66838147", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500134", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDIVATOR66838143", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500135", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AMC.VPN.PEER.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500137", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.NEW.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500138", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.NEW.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500140", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ULN.VPN.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500141", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CISCOPRIME.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500142", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CISCOPRIMEINF", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500144", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MIS_TEST2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500145", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CISCONMON", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500146", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SYSLOGSERVER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500148", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA-QIE.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500149", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA.INTERLACE.PEER.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500150", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WLC1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500152", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "sendgrid.net.virus", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500153", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA.INTERLACE.PEER.EXTERNAL2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500155", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "love.explorethebest.com.spam.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500156", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "love.explorethebest.com.spam.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500157", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "love.explorethebest.com.spam.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500159", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CISCO.WSA.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500160", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HARRIET.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500161", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS32.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500163", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500164", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS19B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500165", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WILLYWONKA.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500167", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSYN01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500168", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSYN02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500169", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSYN03.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500171", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSYN04.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500172", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSP19APP.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500173", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS18C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500175", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS19C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500176", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS14.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500178", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS26D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500179", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DDPC.FIREALARM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500180", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCUIS16B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500182", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS17B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500183", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS19A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500184", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SUMMIT.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500186", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS25A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500187", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ONEVIEW.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500188", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DR1.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500190", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS26B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500191", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBACKUP02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500192", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KRONOSNEW.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500194", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.RAD.EXTERNAL.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500195", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BARRACUDA.LDAP.EXTERNAL.PEER.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500196", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BARRACUDA.LDAP.EXTERNAL.PEER.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500198", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BARRACUDA.LDAP.EXTERNAL.PEER.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500199", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "REYHEALTH.EXTERNAL.EXTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500200", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "REYHEALTH.EXTERNAL.EXTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500202", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHC.OPTUM.EXTERNAL.VPN.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500203", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS18D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500204", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "STREAMTASK.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500206", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "GPSUPPORT.VPN.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500207", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.AWSERVER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500208", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.AWSERVER.ILO", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500210", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.CTSCANNER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500211", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.CT.ADV.WS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500213", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.GE.MAMMO.INTERFACE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500214", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MAMMO.SHUTTLE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500215", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MRI.ALLIANCE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500217", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MUSE01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500218", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MUSE02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500219", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MUSE03", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500221", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MAMMO", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500222", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.NUCMEDCAMERA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500223", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.PETCTVIEWER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500225", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.PERTH.XRAY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500226", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.R.AND.F", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500237", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.ROOMA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500239", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.XELERIS.NM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500241", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500242", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CLEARWATER1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500243", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CLEARWATER2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500245", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "JELMENDORFSPAM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500246", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS25C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500248", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PROVMDAPP.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500249", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPROVMDORACLE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500250", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHMUSE01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500252", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHMUSE02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500253", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS16A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500254", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DESIGO.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500256", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500257", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500259", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500260", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIOUS01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500261", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPRTGPROBE04", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500263", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10C", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500264", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS28", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500265", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500267", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500268", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS07.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500270", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NURSECALLAPP.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500271", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBRAUNPUMPS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500272", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BRAUNWEB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500274", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS09A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500275", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS09B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500276", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS09C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500278", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCISCO.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500279", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS13.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500281", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SQLTEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500282", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHMONITOR.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500283", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPRTG01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500285", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHKIWISYSLOG01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500286", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS17A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500287", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500289", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CITRIXSF.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500290", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHWEB01..NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500291", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AVAYACALLACCT.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500293", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSSI.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500294", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TEMPTRAK.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500295", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PRINT.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500297", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUICKCHARGE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500298", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH3M.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500300", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS19D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500301", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHAV01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500302", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS23A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500304", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS23B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500305", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS23C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500306", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS23D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500308", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDHCP01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500309", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS25B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500310", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS21.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500312", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500313", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS27.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500314", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500316", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE03.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500317", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KRONOSTEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500319", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE-TEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500320", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INTERLACETEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500321", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHINT-TEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500323", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS29.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500324", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHFUSION.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500325", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE-CCGHL7.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500327", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS31.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500328", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500329", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500331", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCA.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500332", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPRTGPROBE3.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500333", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500335", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CODONICS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500336", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDITEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500337", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CITRIXFS02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500339", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500340", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENTPRINT01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500342", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500343", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS18A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500344", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INTERLACE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500346", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA-QIE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500347", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS18B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500348", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHUTILITY.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500350", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCODONICS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500351", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHLICENSE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500352", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HPDMAN.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500354", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SCVMM.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500355", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS24A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500356", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS24B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500358", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS24C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500359", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS24D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500361", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS26A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500362", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ESICALLACCT26A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500363", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ESRS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500365", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDRFIRST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500366", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHELOCK.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500367", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS26C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500369", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "COBAS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500370", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PRADEV.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500371", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHADMINCENTER.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500373", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS28.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500374", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NURSECALLHD.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500375", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-iUV.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500377", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS30.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500378", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE-APP.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500380", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE-NXWEB.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500381", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Clearwater.External.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500382", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ASA01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500384", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ASA02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500385", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_172.16.201.35", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500387", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_192.168.178.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500388", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500389", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500391", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MMI.BILLING.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500392", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MMI.BILLING.INTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500393", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.MEDICOM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500395", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.AMBRA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500396", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.POWERSHARE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500397", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.CLOUD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500399", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Nuvodia.OneOncology.Cloud.External.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500400", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Nuvodia.OneOncology.Cloud.Internal.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500401", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_162.245.33.10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500403", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.NEW.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500404", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500405", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500407", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500408", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500410", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500411", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.500412", "validation_status": null}], "checkpoint_version": "2.0"}