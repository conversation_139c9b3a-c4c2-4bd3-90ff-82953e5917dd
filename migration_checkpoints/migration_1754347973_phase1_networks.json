{"session_id": "migration_1754347973", "timestamp": "2025-08-04T16:08:11.869329", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 100, "created": 99, "updated": 0, "failed": 1, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue", "❌ Failed host: NexTalkSec - Create failed: fmcapi post() failed - no ID returned."], "duration_seconds": 757.3419950008392, "success_rate": 99.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 20, "created": 20, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created network: TeleMedVT3", "✅ Created network: TelemedVT4", "✅ Created network: TelemedVT5", "✅ Created network: TeleMedVT1", "✅ Created network: Medent.VPN.net"], "duration_seconds": 158.29800391197205, "success_rate": 100.0}}, "current_phase": "phase1_networks", "phase_result": {"phase_name": "phase1_networks", "total_objects": 20, "created": 20, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created network: TeleMedVT3", "✅ Created network: TelemedVT4", "✅ Created network: TelemedVT5", "✅ Created network: TeleMedVT1", "✅ Created network: Medent.VPN.net"], "duration_seconds": 158.29800391197205, "success_rate": 100.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "TeleMedVT3", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:05:41.587199", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "TelemedVT4", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:05:49.266888", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "TelemedVT5", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:05:57.081090", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "TeleMedVT1", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:06:04.633494", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "Medent.VPN.net", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:06:12.355925", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "SMHApacsSUBNET", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:06:20.049422", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "pacs.net", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:06:27.783615", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "PACS_VCE", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:06:35.423451", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "pacs.net_1", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:06:43.166642", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "Olympus.Inside.New", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:06:51.018544", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "speculator", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:06:58.814003", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "GEserviceNET", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:07:06.547212", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "Mill.PACS.NET", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:07:14.418509", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "DI.NET", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:07:22.411283", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "STUDENT_VLAN", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:07:30.767255", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "questlab", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:07:38.891465", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "iPEOPLEremote", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:07:47.154866", "validation_status": null}, {"success": true, "action": "updated", "object_type": "NetworkObject", "object_name": "LAN", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:07:55.412523", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "RALSplusLAN", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:08:03.592818", "validation_status": null}, {"success": true, "action": "created", "object_type": "NetworkObject", "object_name": "PhilipsSupport", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:08:11.869029", "validation_status": null}], "checkpoint_version": "2.0"}