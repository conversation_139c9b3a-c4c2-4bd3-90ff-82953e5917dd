{"session_id": "migration_1754347973", "timestamp": "2025-08-04T16:05:33.566713", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 100, "created": 99, "updated": 0, "failed": 1, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue", "❌ Failed host: NexTalkSec - Create failed: fmcapi post() failed - no ID returned."], "duration_seconds": 757.3419950008392, "success_rate": 99.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 100, "created": 99, "updated": 0, "failed": 1, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue", "❌ Failed host: NexTalkSec - Create failed: fmcapi post() failed - no ID returned."], "duration_seconds": 757.3419950008392, "success_rate": 99.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadSaratoga", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:53:04.409956", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadAmsMem", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:53:11.442058", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadStMarys", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:53:18.609365", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadSeton", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:53:25.700158", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadBellevue", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:53:32.850941", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "CITRIXFS02", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:53:39.866016", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "XENAPP30", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:53:46.985675", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC_C-iDRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:53:54.091514", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLHNAS12", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:54:01.106302", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC-D_iDRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:54:08.375121", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "XENAPP02", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:54:15.285322", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "NLHNAS11", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:54:22.328335", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "MAGIC_A-iDRAC", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:54:29.284392", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "MAGIC_E-iDRAC", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:54:36.265370", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "MAGIC_D-NEWiSCSI", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:54:43.380260", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "UNITY-SDC_iSCSI", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:54:50.456073", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "NLH-WEB01-WS01", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:54:58.170253", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "MAGICA", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:55:05.763171", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "MAGICC", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:55:13.429562", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "MAGICD", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:55:21.368549", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "MAGICE", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:55:29.022501", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "MAGICB", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:55:36.591989", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "MAGICF", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:55:44.273761", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "MAGICG", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:55:51.877452", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "DICTATION02", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:55:59.642659", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "MDILIVE.NLH.ORG", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:56:06.836672", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "P_FS_SEC", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:56:13.976803", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "CTScanner", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:56:21.072377", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "<PERSON><PERSON><PERSON>", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:56:28.125092", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "RandF", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:56:35.155090", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "PetLinks1", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:56:42.231094", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "PetLinks2", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:56:49.267960", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "MAGICD3_DRAC", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:56:56.612110", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "LIEBERT.2FL", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:57:03.690629", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "Cardinal132", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:57:11.495056", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "Cardinal133", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:57:19.046364", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "Cardinal144", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:57:26.453611", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "Cardinal145", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:57:33.860458", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "Cardinal176", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:57:49.302475", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "Cardinal177", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:57:57.019019", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "Cardinal194", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:58:04.463718", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "Cardinal195", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:58:11.928515", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "<PERSON><PERSON><PERSON>", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:58:19.458350", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "medinote2", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:58:27.318340", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "medinote1", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:58:34.759211", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "NATHAN3.dmz", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:58:42.483287", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "NATHAN5.dmz", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:58:49.737788", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "NATHAN1.dmz", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:58:57.521790", "validation_status": null}, {"success": true, "action": "created", "object_type": "HostObject", "object_name": "NATHAN4.dmz", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:59:05.076889", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN9.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:59:12.881664", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN10.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:59:20.404659", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NATHAN11.dmz", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:59:27.987364", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:59:35.123169", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:59:42.258659", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS3", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:59:49.299639", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS4", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:59:56.437171", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MilleniumPACS5", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:00:03.249388", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLHEXCHANGE.NLH.ORG", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:00:10.414823", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLH-ISWEB_VIP_NETSCALER1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:00:17.556159", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:00:24.548211", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_CACHE", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:00:31.704852", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_STORE1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:00:38.599624", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_STORE2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:00:45.651684", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_STORE144", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:00:52.735360", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "ResnickPacs1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:00:59.936397", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "ResnickPACS2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:01:07.108955", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TeleRadPC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:01:14.168704", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "CatScan", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:01:21.284637", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PERTH_MRI", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:01:28.813220", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PETScanCT", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:01:35.903188", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "XELERIS", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:01:43.008629", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "INFINIA", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:01:50.131626", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "D5000", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:01:57.683542", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Ultrasound1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:02:05.078444", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Ultrasound2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:02:12.491380", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Ultrasound3", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:02:19.659494", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "KonicaJM", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:02:26.942646", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Konicardr1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:02:34.146317", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "KonicaRdr2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:02:41.402109", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "KonicaRdr3", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:02:49.161943", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "PACS_NEW", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:02:57.079012", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "US_LOGI_E9", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:03:04.961528", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk242", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:03:12.723428", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk243", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:03:20.375764", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk244", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:03:27.993776", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk245", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:03:35.709660", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk246", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:03:43.351158", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalk247", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:03:50.934967", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NexTalkPrime", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:03:58.210902", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalkSec", "object_id": null, "message": "Create failed: fmcapi post() failed - no ID returned.", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:04:22.531258", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Spantel.Prod", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:04:29.711147", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "SpantelHL7.test", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:04:36.853909", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRXcenter2", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:04:43.891027", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRXcenter3", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:04:51.001091", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRXcenter1", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:04:58.166074", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRxChicago", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:05:05.278115", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "eRxDallas", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:05:12.474467", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MedentRemote", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:05:19.506034", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "Medent.RPTS", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:05:26.571506", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "STpc.rtr", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:05:33.565881", "validation_status": null}], "checkpoint_version": "2.0"}