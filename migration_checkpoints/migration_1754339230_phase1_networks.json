{"session_id": "migration_1754339230", "timestamp": "2025-08-04T13:27:20.508290", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0015249252319335938, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0003631114959716797, "success_rate": 0.0}}, "current_phase": "phase1_networks", "phase_result": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0003631114959716797, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TeleMedVT3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507896", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TelemedVT4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507899", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TelemedVT5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507901", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TeleMedVT1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507902", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Medent.VPN.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507904", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SMHApacsSUBNET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507905", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "pacs.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507906", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "PACS_VCE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507908", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "pacs.net_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507909", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Olympus.Inside.New", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507911", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "speculator", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507912", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GEserviceNET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507913", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Mill.PACS.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507914", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "DI.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507916", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "STUDENT_VLAN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507917", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "questlab", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507918", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "iPEOPLEremote", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507920", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "LAN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507921", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "RALSplusLAN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507922", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "PhilipsSupport", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507923", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "STRAT_SOL.NET.INTERNAL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507925", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MVOrtho.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507926", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "LAN_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507927", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MilleniumPACSnat", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507928", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MVOatJSC.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507930", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SENTRYDS.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507931", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SENTRYDS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507932", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "pacs.net-01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507933", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "LAN-01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507935", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MilleniumPACSnat-10.205.56.127", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507936", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "obj-10.205.56.128-10.205.56.255", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507937", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "obj_any", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507938", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "obj_any-03", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507940", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "NUVODIA_NETWORK_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507941", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GUEST_WLAN_NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507942", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GUEST_NETWORK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507943", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "VENDOR_WLAN_NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507945", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CREDITCARD_CAFE_EXTERNAL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507946", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CREDITCARD_CAFE2_EXTERNAL2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507947", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CREDITCARD_CAFE_EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507948", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "STRAT_SOL.NET.INTERNAL2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507950", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507951", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507952", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507953", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507955", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "QUEST.VPN.EXTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507956", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CHC.OPTUM.NAT.INTERNAL.SUB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507957", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507958", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507960", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "BARRACUDA.CLOUD.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507961", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507962", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507963", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GESUPPORT.INTERNAL.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507965", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CLEARWATER3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507966", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CLEARWATER4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507967", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "backblazeb2.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507969", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "HANYS.EXTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507970", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "HANYS.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507971", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "HANYS.EXTERNAL.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507972", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Clearwater.Internal.Peer.Range", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507974", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "NLH.Firewall.Range.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507975", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SSI.EXTERNAL.PEER.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507976", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MICROSOFTSTREAM.COM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.507977", "validation_status": null}], "checkpoint_version": "2.0"}