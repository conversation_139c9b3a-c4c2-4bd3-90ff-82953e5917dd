{"session_id": "migration_1754345222", "timestamp": "2025-08-04T15:08:38.418320", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 5, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created host: TestHost_WebServer", "✅ Created host: TestHost_DatabaseServer", "✅ Created host: TestHost_FileServer", "✅ Created host: TestHost_PrintServer", "✅ Created host: TestHost_BackupServer"], "duration_seconds": 38.063902139663696, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 3, "created": 3, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created network: TestNetwork_LAN", "✅ Created network: TestNetwork_DMZ", "✅ Created network: TestNetwork_Management"], "duration_seconds": 24.722725868225098, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 4, "created": 4, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created service: TestService_HTTP_8080", "✅ Created service: TestService_HTTPS_8443", "✅ Created service: TestService_Database", "✅ Created service: TestService_FTP"], "duration_seconds": 26.82383894920349, "success_rate": 0.0}}, "current_phase": "phase1_services", "phase_result": {"phase_name": "phase1_services", "total_objects": 4, "created": 4, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created service: TestService_HTTP_8080", "✅ Created service: TestService_HTTPS_8443", "✅ Created service: TestService_Database", "✅ Created service: TestService_FTP"], "duration_seconds": 26.82383894920349, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "created", "object_type": "ProtocolPortObject", "object_name": "TestService_HTTP_8080", "object_id": "005056BF-7B88-0ed3-0000-017187313290", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:08:18.268869", "validation_status": null}, {"success": true, "action": "created", "object_type": "ProtocolPortObject", "object_name": "TestService_HTTPS_8443", "object_id": "005056BF-7B88-0ed3-0000-017187313309", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:08:24.982453", "validation_status": null}, {"success": true, "action": "created", "object_type": "ProtocolPortObject", "object_name": "TestService_Database", "object_id": "005056BF-7B88-0ed3-0000-017187313328", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:08:31.498804", "validation_status": null}, {"success": true, "action": "created", "object_type": "ProtocolPortObject", "object_name": "TestService_FTP", "object_id": "005056BF-7B88-0ed3-0000-017187313347", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:08:38.417774", "validation_status": null}], "checkpoint_version": "2.0"}