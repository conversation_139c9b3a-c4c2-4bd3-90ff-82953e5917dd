{"session_id": "migration_1754345698", "timestamp": "2025-08-04T15:16:17.855081", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 0, "updated": 5, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost_WebServer", "✅ Updated host: TestHost_DatabaseServer", "✅ Updated host: TestHost_FileServer", "✅ Updated host: TestHost_PrintServer", "✅ Updated host: TestHost_BackupServer"], "duration_seconds": 36.6576030254364, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork_LAN", "✅ Updated network: TestNetwork_DMZ", "✅ Updated network: TestNetwork_Management"], "duration_seconds": 24.02049994468689, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 4, "created": 0, "updated": 4, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestService_HTTP_8080", "✅ Updated service: TestService_HTTPS_8443", "✅ Updated service: TestService_Database", "✅ Updated service: TestService_FTP"], "duration_seconds": 12.104196786880493, "success_rate": 0.0}}, "current_phase": "phase1_services", "phase_result": {"phase_name": "phase1_services", "total_objects": 4, "created": 0, "updated": 4, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestService_HTTP_8080", "✅ Updated service: TestService_HTTPS_8443", "✅ Updated service: TestService_Database", "✅ Updated service: TestService_FTP"], "duration_seconds": 12.104196786880493, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "TestService_HTTP_8080", "object_id": "005056BF-7B88-0ed3-0000-017187313290", "message": "Protocol port object exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:16:08.933290", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "TestService_HTTPS_8443", "object_id": "005056BF-7B88-0ed3-0000-017187313309", "message": "Protocol port object exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:16:11.873565", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "TestService_Database", "object_id": "005056BF-7B88-0ed3-0000-017187313328", "message": "Protocol port object exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:16:14.862094", "validation_status": null}, {"success": true, "action": "updated", "object_type": "ProtocolPortObject", "object_name": "TestService_FTP", "object_id": "005056BF-7B88-0ed3-0000-017187313347", "message": "Protocol port object exists and is correct (no update needed)", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:16:17.854891", "validation_status": null}], "checkpoint_version": "2.0"}