{"session_id": "migration_1754328956", "timestamp": "2025-08-04T14:42:46.374573", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 629, "failed": 0, "skipped": 0, "details": [], "duration_seconds": 2848.3845858573914, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 37, "failed": 26, "skipped": 0, "details": ["❌ Failed to create network: TeleMedVT3 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TelemedVT4 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TelemedVT5 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TeleMedVT1 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Medent.VPN.net - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Olympus.Inside.New - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: speculator - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: GEserviceNET - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Mill.PACS.NET - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: DI.NET - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 280.62504529953003, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed to create service: obj-tcp-eq-80 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to update service: obj-tcp-eq-15002 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-15331 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-3389 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-2222 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-6544 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-2020 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to create service: obj-tcp-eq-23 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to update service: obj-tcp-eq-15031 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to create service: obj-tcp-eq-5631 - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 122.99100136756897, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed to create object_group: Medivators - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: NUVODIA.INTERNAL.GROUP.NEW - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: NUVODIA.INTERNAL.PEER.NET1 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: DM_INLINE_NETWORK_4 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: DM_INLINE_NETWORK_6 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: FoodService - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: DI.Net.Group - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: Cardinal - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create object_group: medinotes - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 482.0017967224121, "success_rate": 0.0}, "phase1_service_groups": {"phase_name": "phase1_service_groups", "total_objects": 66, "created": 0, "updated": 0, "failed": 66, "skipped": 0, "details": ["❌ Failed to create service_group: PaceGlobalgrp - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: timeservice - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: timeserviceUDP - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: QUEST - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: citrixXML - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: GatewayDMZ - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: RSA - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: HFMBoces - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: GEinbound - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: GEoutbound - fmcapi post() failed - no ID returned. Result: False"], "duration_seconds": 270.94292068481445, "success_rate": 0.0}}, "current_phase": "phase1_service_groups", "phase_result": {"phase_name": "phase1_service_groups", "total_objects": 66, "created": 0, "updated": 0, "failed": 66, "skipped": 0, "details": ["❌ Failed to create service_group: PaceGlobalgrp - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: timeservice - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: timeserviceUDP - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: QUEST - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: citrixXML - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: GatewayDMZ - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: RSA - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: HFMBoces - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: GEinbound - fmcapi post() failed - no ID returned. Result: False", "❌ Failed to create service_group: GEoutbound - fmcapi post() failed - no ID returned. Result: False"], "duration_seconds": 270.94292068481445, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PaceGlobalgrp", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:19.583852", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "timeservice", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:23.730294", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "timeserviceUDP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:27.831500", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "QUEST", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:31.962115", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "citrixXML", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:36.058573", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "GatewayDMZ", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:40.197674", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "RSA", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:44.283737", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "HFMBoces", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:48.371926", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "GEinbound", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:52.501263", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "GEoutbound", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:38:56.679600", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PetLinks", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:00.798102", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "TeleVideoTcpUdp", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:04.941693", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "GEPACS", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:09.060579", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ExchangePorts", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:13.187778", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PrintPorts", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:17.302753", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PrinterPorts", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:21.419527", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "IPSEC_ISAKMP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:25.484348", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "EmdeonPorts", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:29.611846", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "in_any_to_out_any_tcp", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:33.746962", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "RAMSOFTports", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:37.835061", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CoreFTP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:41.894283", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PhilipsPacs", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:46.003817", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Pacs", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:50.119473", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "NexTalk1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:54.206055", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "NexTalkTcpUdp", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:39:58.309698", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CastleSys", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:02.423421", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "FTPpsv5500", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:06.492830", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Labcorp", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:10.527163", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Labcorptcp", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:14.649463", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "IVANStcp", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:18.715328", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "IVANSudp", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:22.780862", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "<PERSON>ph<PERSON>", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:26.847775", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "any_in_udp_to_any_out", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:30.938613", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "SophosMail", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:35.017711", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "BobSFTP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:39.096685", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Impulse.UDP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:43.207727", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ImpulseTCP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:47.339969", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "TEMP_TRACK1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:51.409929", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "<PERSON><PERSON><PERSON><PERSON>", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:55.541041", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ALLSCRIPT_PORTAL", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:40:59.610166", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "testgroup", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:03.755866", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ALBANYMEDPACS", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:07.800811", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Guest_Wireless", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:11.855035", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "SOPHOSFTP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:16.023419", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "BOCES_IPADS", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:20.116113", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "TEST", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:24.248358", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "IMO_Ports", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:28.391346", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "TeamViewer", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:32.553445", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CCD_MESSAGING", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:36.674604", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Apple_Services", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:40.774432", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ProviderOrg", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:44.822392", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "MAIL_VIRUS", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:48.958393", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "STAT_RAD", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:53.025304", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "StatRadService", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:41:57.155278", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PAT_ACCTS_FTP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:01.271700", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "UDP_TEST", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:05.412670", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CE000SVC", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:09.459496", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CE2000", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:13.591240", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "m<PERSON>sson", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:17.737433", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "DM_INLINE_SERVICE_1", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:21.863286", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "MEDENT_TELEMED", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:25.900510", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PHINMS", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:29.976165", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "SALUCRO_FTP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:34.088498", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "QUEST_SFTP_NEW", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:38.211418", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "REYHEALTH.EXTERNAL.PORT.GROUP", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:42.311958", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "DM_INLINE_SERVICE_2", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: F<PERSON><PERSON>", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:42:46.372281", "validation_status": null}], "checkpoint_version": "2.0"}