{"session_id": "migration_1754339420", "timestamp": "2025-08-04T13:30:30.612609", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0018858909606933594, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0004558563232421875, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed service: obj-tcp-eq-80 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15002 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15331 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-3389 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2222 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-6544 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2020 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-23 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15031 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-5631 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0003209114074707031, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed object_group: Medivators - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.GROUP.NEW - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.PEER.NET1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: FoodService - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DI.Net.Group - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: Cardinal - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: medinotes - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0004048347473144531, "success_rate": 0.0}, "phase1_service_groups": {"phase_name": "phase1_service_groups", "total_objects": 66, "created": 0, "updated": 0, "failed": 66, "skipped": 0, "details": ["❌ Failed service_group: PaceGlobalgrp - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeservice - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeserviceUDP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: QUEST - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: citrixXML - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GatewayDMZ - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: RSA - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: HFMBoces - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEinbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEoutbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0003330707550048828, "success_rate": 0.0}, "phase1_access_rules": {"phase_name": "phase1_access_rules", "total_objects": 224, "created": 0, "updated": 0, "failed": 224, "skipped": 0, "details": ["❌ Failed access_rule: inside_access_in_rule_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_2 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_7 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_8 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_9 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_10 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0006167888641357422, "success_rate": 0.0}}, "current_phase": "phase1_access_rules", "phase_result": {"phase_name": "phase1_access_rules", "total_objects": 224, "created": 0, "updated": 0, "failed": 224, "skipped": 0, "details": ["❌ Failed access_rule: inside_access_in_rule_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_2 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_7 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_8 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_9 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_10 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0006167888641357422, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611621", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611626", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611628", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611629", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611631", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611632", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611634", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611635", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611636", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611638", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611639", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611641", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611642", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_14", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611643", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_15", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611645", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_16", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611646", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_17", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611648", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_18", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611649", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_19", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611650", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_20", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611652", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_21", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611653", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_22", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611655", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_23", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611656", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_24", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611657", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_25", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611659", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_26", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611660", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_27", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611662", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_28", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611663", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_29", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611664", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_30", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611666", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_31", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611667", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_32", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611668", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_33", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611670", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_34", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611671", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_35", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611673", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_36", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611674", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_37", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611675", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_38", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611677", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_39", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611678", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_40", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611679", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_41", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611681", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_42", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611682", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_43", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611684", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_44", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611685", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_45", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611686", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_46", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611688", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_47", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611689", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_48", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611690", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_49", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611692", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_50", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611693", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_51", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611694", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_52", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611696", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_53", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611697", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_54", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611699", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_55", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611700", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_56", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611701", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_57", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611703", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_58", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611704", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_59", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611705", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_60", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611707", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_61", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611708", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_62", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611709", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_63", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611711", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_64", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611712", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_65", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611713", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_66", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611715", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_67", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611716", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_68", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611718", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_69", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611719", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_70", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611720", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_71", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611722", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_72", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611723", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_73", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611724", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_74", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611726", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_75", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611727", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_76", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611728", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_77", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611730", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_78", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611732", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_79", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611733", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_80", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611734", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_81", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611736", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_82", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611737", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_83", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611739", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_84", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611740", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_85", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611741", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_86", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611743", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_87", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611744", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_88", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611745", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_89", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611747", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_90", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611748", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611749", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611751", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611752", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611754", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611755", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611756", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611758", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611759", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611760", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611762", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611763", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611764", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611766", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_14", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611767", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_15", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611768", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_16", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611770", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_17", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611771", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_18", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611772", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_19", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611774", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_20", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611775", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_21", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611777", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_22", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611778", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_23", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611779", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_24", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611781", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_25", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611783", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_26", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611784", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_27", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611786", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_28", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611787", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_29", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611788", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_30", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611790", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_31", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611791", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_32", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611792", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_33", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611794", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_34", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611795", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_35", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611796", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_36", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611798", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_37", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611799", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_38", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611800", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_39", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611802", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_40", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611803", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_41", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611805", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_42", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611806", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_43", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611808", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_44", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611809", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_45", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611810", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_46", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611812", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_47", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611813", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_48", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611814", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_49", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611816", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_50", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611817", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_51", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611818", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611820", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611821", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611822", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611824", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611825", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611826", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611828", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611829", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611830", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611832", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611833", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611834", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611836", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611837", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611838", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611840", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611841", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611843", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611844", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_14", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611845", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_15", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611847", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_16", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611848", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_17", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611849", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_18", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611851", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_19", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611852", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN2.nlh.org_splitTunnelAcl_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611853", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_6_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611855", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN.nlh.org_splitTunnelAcl_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611856", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN.nlh.org_splitTunnelAcl_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611857", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN.nlh.org_splitTunnelAcl_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611859", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_3_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611860", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_9_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611861", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_10_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611863", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_11_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611864", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611866", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_12_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611867", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611868", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_14_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611870", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_15_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611871", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_2_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611872", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_7_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611874", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_880_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611875", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_pnat_inbound_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611876", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V2_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611878", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1000_1_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611879", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_13_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611880", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_16_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611882", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1120_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611883", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V3_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611884", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V3_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611886", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V3_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611887", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V4_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611888", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V4_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611890", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V4_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611891", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_17_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611892", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V14_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611894", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V15_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611895", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_5_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611896", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1300_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611898", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "sfr_redirect_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611899", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Guest_access_in_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611901", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Guest_access_in_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611902", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Guest_access_in_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611903", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611905", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611906", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611907", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611909", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611910", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611911", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611913", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611914", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611915", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611917", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611918", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611919", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611921", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611922", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611924", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611925", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611926", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611928", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_4_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611929", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_8_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.611930", "validation_status": null}], "checkpoint_version": "2.0"}