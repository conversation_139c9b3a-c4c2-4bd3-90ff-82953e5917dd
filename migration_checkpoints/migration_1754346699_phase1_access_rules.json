{"session_id": "migration_1754346699", "timestamp": "2025-08-04T15:32:29.461252", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost_Server1", "✅ Updated host: TestHost_Server2"], "duration_seconds": 14.304882764816284, "success_rate": 100.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 1, "created": 0, "updated": 1, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork_Subnet"], "duration_seconds": 7.790916204452515, "success_rate": 100.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 1, "created": 0, "updated": 1, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestService_HTTP"], "duration_seconds": 2.9988269805908203, "success_rate": 100.0}, "phase1_access_rules": {"phase_name": "phase1_access_rules", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule_AllowHTTP - Access rule creation failed - no ID returned. Result: None"], "duration_seconds": 12.312772989273071, "success_rate": 0.0}}, "current_phase": "phase1_access_rules", "phase_result": {"phase_name": "phase1_access_rules", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule_AllowHTTP - Access rule creation failed - no ID returned. Result: None"], "duration_seconds": 12.312772989273071, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "TestRule_AllowHTTP", "object_id": null, "message": "Access rule creation failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:32:29.461117", "validation_status": null}], "checkpoint_version": "2.0"}