{"session_id": "migration_1754339485", "timestamp": "2025-08-04T13:31:35.926633", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.005800008773803711, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0012547969818115234, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed service: obj-tcp-eq-80 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15002 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15331 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-3389 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2222 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-6544 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2020 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-23 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15031 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-5631 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0010218620300292969, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed object_group: Medivators - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.GROUP.NEW - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.PEER.NET1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: FoodService - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DI.Net.Group - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: Cardinal - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: medinotes - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0012371540069580078, "success_rate": 0.0}, "phase1_service_groups": {"phase_name": "phase1_service_groups", "total_objects": 66, "created": 0, "updated": 0, "failed": 66, "skipped": 0, "details": ["❌ Failed service_group: PaceGlobalgrp - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeservice - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeserviceUDP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: QUEST - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: citrixXML - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GatewayDMZ - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: RSA - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: HFMBoces - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEinbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEoutbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0009541511535644531, "success_rate": 0.0}}, "current_phase": "phase1_service_groups", "phase_result": {"phase_name": "phase1_service_groups", "total_objects": 66, "created": 0, "updated": 0, "failed": 66, "skipped": 0, "details": ["❌ Failed service_group: PaceGlobalgrp - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeservice - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeserviceUDP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: QUEST - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: citrixXML - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GatewayDMZ - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: RSA - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: HFMBoces - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEinbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEoutbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0009541511535644531, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PaceGlobalgrp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926155", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "timeservice", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926163", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "timeserviceUDP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926166", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "QUEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926167", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "citrixXML", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926169", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "GatewayDMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926170", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "RSA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926172", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "HFMBoces", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926173", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "GEinbound", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926175", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "GEoutbound", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926176", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PetLinks", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926177", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "TeleVideoTcpUdp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926179", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "GEPACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926180", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ExchangePorts", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926182", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PrintPorts", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926183", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PrinterPorts", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926184", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "IPSEC_ISAKMP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926186", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "EmdeonPorts", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926187", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "in_any_to_out_any_tcp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926188", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "RAMSOFTports", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926190", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CoreFTP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926191", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PhilipsPacs", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926193", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Pacs", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926194", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "NexTalk1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926196", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "NexTalkTcpUdp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926197", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CastleSys", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926198", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "FTPpsv5500", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926200", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Labcorp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926201", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Labcorptcp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926203", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "IVANStcp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926204", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "IVANSudp", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926205", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "<PERSON>ph<PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926207", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "any_in_udp_to_any_out", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926208", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "SophosMail", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926210", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "BobSFTP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926211", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Impulse.UDP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926212", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ImpulseTCP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926214", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "TEMP_TRACK1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926215", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "<PERSON><PERSON><PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926216", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ALLSCRIPT_PORTAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926218", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "testgroup", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926219", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ALBANYMEDPACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926220", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Guest_Wireless", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926222", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "SOPHOSFTP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926223", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "BOCES_IPADS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926224", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926226", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "IMO_Ports", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926227", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "TeamViewer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926229", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CCD_MESSAGING", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926230", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "Apple_Services", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926231", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "ProviderOrg", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926233", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "MAIL_VIRUS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926234", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "STAT_RAD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926235", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "StatRadService", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926237", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PAT_ACCTS_FTP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926238", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "UDP_TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926239", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CE000SVC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926241", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "CE2000", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926242", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "m<PERSON>sson", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926243", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "DM_INLINE_SERVICE_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926245", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "MEDENT_TELEMED", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926246", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "PHINMS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926247", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "SALUCRO_FTP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926249", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "QUEST_SFTP_NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926250", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "REYHEALTH.EXTERNAL.PORT.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926251", "validation_status": null}, {"success": false, "action": "failed", "object_type": "PortObjectGroup", "object_name": "DM_INLINE_SERVICE_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.926253", "validation_status": null}], "checkpoint_version": "2.0"}