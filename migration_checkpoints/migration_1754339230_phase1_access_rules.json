{"session_id": "migration_1754339230", "timestamp": "2025-08-04T13:27:20.514165", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0015249252319335938, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0003631114959716797, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed service: obj-tcp-eq-80 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15002 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15331 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-3389 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2222 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-6544 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2020 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-23 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15031 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-5631 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.00025916099548339844, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed object_group: Medivators - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.GROUP.NEW - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.PEER.NET1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: FoodService - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DI.Net.Group - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: Cardinal - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: medinotes - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.000370025634765625, "success_rate": 0.0}, "phase1_service_groups": {"phase_name": "phase1_service_groups", "total_objects": 66, "created": 0, "updated": 0, "failed": 66, "skipped": 0, "details": ["❌ Failed service_group: PaceGlobalgrp - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeservice - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: timeserviceUDP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: QUEST - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: citrixXML - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GatewayDMZ - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: RSA - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: HFMBoces - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEinbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service_group: GEoutbound - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0003142356872558594, "success_rate": 0.0}, "phase1_access_rules": {"phase_name": "phase1_access_rules", "total_objects": 224, "created": 0, "updated": 0, "failed": 224, "skipped": 0, "details": ["❌ Failed access_rule: inside_access_in_rule_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_2 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_7 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_8 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_9 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_10 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0006191730499267578, "success_rate": 0.0}}, "current_phase": "phase1_access_rules", "phase_result": {"phase_name": "phase1_access_rules", "total_objects": 224, "created": 0, "updated": 0, "failed": 224, "skipped": 0, "details": ["❌ Failed access_rule: inside_access_in_rule_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_2 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_7 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_8 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_9 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed access_rule: inside_access_in_rule_10 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0006191730499267578, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513192", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513194", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513195", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513197", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513198", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513199", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513224", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513229", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513231", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513232", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513234", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513235", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513236", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_14", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513238", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_15", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513239", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_16", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513240", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_17", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513242", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_18", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513243", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_19", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513244", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_20", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513246", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_21", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513247", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_22", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513248", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_23", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513249", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_24", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513251", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_25", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513252", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_26", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513253", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_27", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513255", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_28", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513256", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_29", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513257", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_30", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513259", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_31", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513260", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_32", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513261", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_33", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513274", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_34", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513279", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_35", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513281", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_36", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513283", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_37", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513284", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_38", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513286", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_39", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513287", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_40", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513288", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_41", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513290", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_42", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513291", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_43", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513292", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_44", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513294", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_45", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513295", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_46", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513296", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_47", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513298", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_48", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513299", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_49", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513300", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_50", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513301", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_51", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513303", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_52", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513304", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_53", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513305", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_54", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513307", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_55", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513308", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_56", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513309", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_57", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513310", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_58", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513312", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_59", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513313", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_60", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513314", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_61", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513315", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_62", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513316", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_63", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513318", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_64", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513319", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_65", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513320", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_66", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513322", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_67", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513323", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_68", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513325", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_69", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513326", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_70", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513327", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_71", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513328", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_72", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513329", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_73", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513331", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_74", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513332", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_75", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513333", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_76", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513334", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_77", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513336", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_78", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513337", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_79", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513339", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_80", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513340", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_81", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513341", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_82", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513342", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_83", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513343", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_84", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513345", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_85", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513346", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_86", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513347", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_87", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513349", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_88", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513350", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_89", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513351", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_access_in_rule_90", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513352", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513354", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513355", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513356", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513357", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513359", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513360", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513361", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513362", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513364", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513365", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513366", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513368", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513369", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_14", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513370", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_15", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513371", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_16", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513373", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_17", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513374", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_18", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513375", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_19", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513376", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_20", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513378", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_21", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513379", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_22", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513380", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_23", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513382", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_24", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513383", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_25", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513385", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_26", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513386", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_27", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513387", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_28", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513389", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_29", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513390", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_30", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513391", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_31", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513392", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_32", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513394", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_33", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513395", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_34", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513396", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_35", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513397", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_36", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513398", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_37", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513400", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_38", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513401", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_39", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513402", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_40", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513404", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_41", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513405", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_42", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513407", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_43", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513408", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_44", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513409", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_45", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513410", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_46", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513411", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_47", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513413", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_48", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513414", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_49", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513416", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_50", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513417", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_access_in_rule_51", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513418", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513419", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513421", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513422", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513423", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513424", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "DMZ_access_in_V1_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513426", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513427", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513428", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513430", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513431", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513432", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513433", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513435", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513436", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513437", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513438", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513439", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513441", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513442", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_14", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513443", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_15", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513444", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_16", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513446", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_17", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513447", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_18", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513448", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_nat0_outbound_rule_19", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513449", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN2.nlh.org_splitTunnelAcl_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513451", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_6_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513452", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN.nlh.org_splitTunnelAcl_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513453", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN.nlh.org_splitTunnelAcl_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513454", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "VPN.nlh.org_splitTunnelAcl_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513455", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_3_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513457", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_9_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513458", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_10_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513459", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_11_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513460", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513462", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_12_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513463", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513464", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_14_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513465", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_15_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513467", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_2_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513468", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_7_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513469", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_880_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513470", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_pnat_inbound_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513472", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V2_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513473", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1000_1_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513474", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_13_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513475", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_16_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513476", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1120_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513478", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V3_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513479", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V3_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513480", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V3_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513481", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V4_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513482", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V4_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513484", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V4_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513485", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_17_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513486", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V14_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513487", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "inside_pnat_outbound_V15_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513488", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_5_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513490", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_1300_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513491", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "sfr_redirect_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513492", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Guest_access_in_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513493", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Guest_access_in_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513494", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Guest_access_in_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513496", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513497", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513498", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513499", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513500", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "Vendor_access_in_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513502", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513503", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513504", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513505", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513507", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513508", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513509", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513510", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513511", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513513", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513514", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513515", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513516", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "AnyConnect_Client_Local_Print_rule_13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513517", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_4_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513519", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "outside_cryptomap_8_rule_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.513520", "validation_status": null}], "checkpoint_version": "2.0"}