{"session_id": "migration_1754347883", "timestamp": "2025-08-04T15:52:42.004198", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 10, "created": 10, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue"], "duration_seconds": 75.34674096107483, "success_rate": 100.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 10, "created": 10, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue"], "duration_seconds": 75.34674096107483, "success_rate": 100.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadSaratoga", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:51:34.296723", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadAmsMem", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:51:41.875708", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadStMarys", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:51:49.732888", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadSeton", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:51:57.227411", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "RadBellevue", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:52:04.885354", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "CITRIXFS02", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:52:12.522716", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "XENAPP30", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:52:20.288380", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC_C-iDRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:52:27.714891", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "NLHNAS12", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:52:34.885999", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "MAGIC-D_iDRAC", "object_id": null, "message": "Updated existing object", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:52:42.003868", "validation_status": null}], "checkpoint_version": "2.0"}