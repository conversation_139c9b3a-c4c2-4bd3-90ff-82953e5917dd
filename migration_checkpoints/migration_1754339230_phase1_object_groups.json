{"session_id": "migration_1754339230", "timestamp": "2025-08-04T13:27:20.510572", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0015249252319335938, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0003631114959716797, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed service: obj-tcp-eq-80 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15002 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15331 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-3389 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2222 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-6544 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-2020 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-23 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-15031 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed service: obj-tcp-eq-5631 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.00025916099548339844, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed object_group: Medivators - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.GROUP.NEW - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.PEER.NET1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: FoodService - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DI.Net.Group - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: Cardinal - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: medinotes - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.000370025634765625, "success_rate": 0.0}}, "current_phase": "phase1_object_groups", "phase_result": {"phase_name": "phase1_object_groups", "total_objects": 111, "created": 0, "updated": 0, "failed": 111, "skipped": 0, "details": ["❌ Failed object_group: Medivators - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.GROUP.NEW - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: NUVODIA.INTERNAL.PEER.NET1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DM_INLINE_NETWORK_6 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: FoodService - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: DI.Net.Group - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: STRATEGICSOLUTIONS.EXTERNAL.GROUP - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: Cardinal - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed object_group: medinotes - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.000370025634765625, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Medivators", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510054", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA.INTERNAL.GROUP.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510056", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA.INTERNAL.PEER.NET1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510058", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510059", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510061", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FoodService", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510062", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DI.Net.Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510063", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "STRATEGICSOLUTIONS.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510065", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "<PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510066", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "medinotes", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510068", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CitrixServers.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510069", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MilleniumPACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510070", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ExchangeServers", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510071", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "TeleMedVT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510073", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "PacsServers", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510074", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MDI.OUT.<PERSON>ow", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510075", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "eRXdataCenters", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510076", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Medent.Interface", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510078", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SMHA.RAD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510079", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "RAD.PACS.READ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510080", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SOPHOS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510082", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CitrixServers1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510083", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CitrixServers1_ref", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510084", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MVO_Allow_OUTBOUND_Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510086", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ProvationServers", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510087", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "AAI.NYOH.PACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510088", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Dolby_OUT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510089", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Dolby_Servers", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510091", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Healthtouch.out", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510092", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FoodSVC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510093", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ALBANYPACS.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510094", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HIXNY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510096", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Olympus.inside.group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510097", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MDI_Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510098", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Brian_DHCP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510099", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Schumacher.Inside", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510101", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENTHQ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510102", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT_GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510103", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "WINDOWS_XP_DENY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510104", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "APPLE.OUT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510106", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ProviderOrg.External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510107", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CITRIX_EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510108", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CITRIXGATEWAY.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510110", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CITRIX_INTERNAL_TO_DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510111", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MIS_TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510112", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MARKETO_SPAMMER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510114", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "ENDOWORKS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510115", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CE2000.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510116", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CE2000.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510117", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_24.97.36.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510118", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_65.114.41.136", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510120", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_12.39.198.49", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510121", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_173.84.224.94", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510122", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Group_12.152.123.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510123", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HIXNY.MBMS.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510125", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA_VPN_NLH_PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510126", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CLEARWATERTEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510127", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HIXNY.INTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510128", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MDI.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510130", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NYOH.INTERNAL.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510131", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NUVODIA.VPN.SENDPOINT.MASTER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510132", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510133", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510135", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510136", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SMHA.RAD.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510137", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510139", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT.NIMBLE.OPENVPN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510140", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510141", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "MEDENT.NIMBLE.OPENVPN.INSIDE.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510142", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "TCPUDP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510144", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "EXPANSE_VLANS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510145", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SmartNet_Devices", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510146", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Domain.Controllers.Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510147", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Quest.NLH2Quest.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510148", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510150", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CHANGE.HEALTHCARE.NLH.INTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510151", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CHC.EXTERNAL.NETWORK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510152", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "PHINMS.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510154", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLI.INTERNAL.NAT.CHC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510155", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLI-BG-GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510156", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510157", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "QUEST.VPN.INTERNAL.GROUP.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510159", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "WEBSSO.MEDITECH.COM.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510160", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "BACKLINE.LDAP.NLH.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510161", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FIRECALLSYSTEM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510162", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FIRECALLSYSTEM__ENDPOINTS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510164", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "IT_DEPT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510165", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FULL_PORT_ACCESS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510166", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "CISCO_INTERNAL_2_EXTERNAL_ACL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510167", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HEALTHTOUCH.NLH.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510169", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HEALTHTOUCH.PEER.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510170", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Greycastle_Testing_External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510171", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "LINKBG.SPAM.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510172", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "love.explorethebest.com.spam.group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510174", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.ACRONIS.GROUP.INSIDE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510175", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.ACRONIS.GROUP.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510176", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "BARRACUDA.LDAP.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510178", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "REYHEALTH.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510179", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "EMAIL.BLACKLIST.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510180", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.DI.GEDEVICES", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510181", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "DM_INLINE_NETWORK_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510183", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "blackblazeb2.goup", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510184", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "HAYNS.EXTERNAL.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510185", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510186", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Incident.External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510188", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "NLH.Firewall.Internal.Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510189", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "Clearwater.Internal.Group", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510190", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510191", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "SSI.EXTERNAL.PEER.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510193", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "BANDWIDTH.TEST.GROUP.OUTSIDE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510194", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkGroup", "object_name": "FRESHWORKS.EXCLUSIONS.GROUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:27:20.510196", "validation_status": null}], "checkpoint_version": "2.0"}