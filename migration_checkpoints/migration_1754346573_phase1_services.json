{"session_id": "migration_1754346573", "timestamp": "2025-08-04T15:30:07.760208", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 2, "created": 2, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created host: TestHost_Server1", "✅ Created host: TestHost_Server2"], "duration_seconds": 14.078372955322266, "success_rate": 100.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 1, "created": 1, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created network: TestNetwork_Subnet"], "duration_seconds": 7.548114061355591, "success_rate": 100.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 1, "created": 1, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created service: TestService_HTTP"], "duration_seconds": 6.128085136413574, "success_rate": 100.0}}, "current_phase": "phase1_services", "phase_result": {"phase_name": "phase1_services", "total_objects": 1, "created": 1, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created service: TestService_HTTP"], "duration_seconds": 6.128085136413574, "success_rate": 100.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "created", "object_type": "ProtocolPortObject", "object_name": "TestService_HTTP", "object_id": "005056BF-7B88-0ed3-0000-017187339626", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:30:07.759514", "validation_status": null}], "checkpoint_version": "2.0"}