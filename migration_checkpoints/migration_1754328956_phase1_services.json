{"session_id": "migration_1754328956", "timestamp": "2025-08-04T14:30:13.351873", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 629, "failed": 0, "skipped": 0, "details": [], "duration_seconds": 2848.3845858573914, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 37, "failed": 26, "skipped": 0, "details": ["❌ Failed to create network: TeleMedVT3 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TelemedVT4 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TelemedVT5 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: TeleMedVT1 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Medent.VPN.net - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Olympus.Inside.New - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: speculator - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: GEserviceNET - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: Mill.PACS.NET - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to create network: DI.NET - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 280.62504529953003, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed to create service: obj-tcp-eq-80 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to update service: obj-tcp-eq-15002 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-15331 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-3389 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-2222 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-6544 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-2020 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to create service: obj-tcp-eq-23 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to update service: obj-tcp-eq-15031 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to create service: obj-tcp-eq-5631 - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 122.99100136756897, "success_rate": 0.0}}, "current_phase": "phase1_services", "phase_result": {"phase_name": "phase1_services", "total_objects": 29, "created": 0, "updated": 0, "failed": 29, "skipped": 0, "details": ["❌ Failed to create service: obj-tcp-eq-80 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to update service: obj-tcp-eq-15002 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-15331 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-3389 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-2222 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-6544 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: obj-tcp-eq-2020 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to create service: obj-tcp-eq-23 - fmcapi post() failed - no ID returned. Result: None", "❌ Failed to update service: obj-tcp-eq-15031 - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to create service: obj-tcp-eq-5631 - fmcapi post() failed - no ID returned. Result: None"], "duration_seconds": 122.99100136756897, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-80", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:28:14.608389", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-15002", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:28:18.822285", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-15331", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:28:23.153043", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-3389", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:28:27.405884", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-2222", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:28:31.631218", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-6544", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:28:35.822567", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-2020", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:28:40.041204", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-23", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:28:44.332522", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-15031", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:28:48.617101", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-5631", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:28:52.820388", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-udp-eq-15032", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:28:57.010089", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-udp-eq-5632", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:01.316609", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-25", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:05.511392", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-443", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:09.763597", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-55443", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:14.089090", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-3401", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:18.406594", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-53048", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:22.613083", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-53372", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:26.796045", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-53050", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:30.991144", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-53374", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:35.276620", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-21", "object_id": null, "message": "fmcapi post() failed - no ID returned. Result: None", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:39.517501", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "NLI-BG13-FTP", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:43.827974", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "W32.MYDOOM.OLD", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:48.085145", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "GREYCASTLE_VPN", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:52.270048", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "IMO_CLOUD", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:29:56.487054", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "NOVA-8070-TCP", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:00.691102", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "REYHEALTH.EXTERNAL.PORT1", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:04.941645", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "REYHEALTH.EXTERNAL.PORT2", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:09.133856", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "NOVA.TOPAZ", "object_id": null, "message": "fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:30:13.351695", "validation_status": null}], "checkpoint_version": "2.0"}