{"session_id": "migration_1754339485", "timestamp": "2025-08-04T13:31:35.919964", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.005800008773803711, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0012547969818115234, "success_rate": 0.0}}, "current_phase": "phase1_networks", "phase_result": {"phase_name": "phase1_networks", "total_objects": 63, "created": 0, "updated": 0, "failed": 63, "skipped": 0, "details": ["❌ Failed network: TeleMedVT3 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT4 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TelemedVT5 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: TeleMedVT1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Medent.VPN.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: SMHApacsSUBNET - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: PACS_VCE - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: pacs.net_1 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed network: Olympus.Inside.New - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0012547969818115234, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TeleMedVT3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919490", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TelemedVT4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919496", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TelemedVT5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919498", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "TeleMedVT1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919499", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Medent.VPN.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919501", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SMHApacsSUBNET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919502", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "pacs.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919504", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "PACS_VCE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919505", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "pacs.net_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919507", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Olympus.Inside.New", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919508", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "speculator", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919509", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GEserviceNET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919511", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Mill.PACS.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919512", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "DI.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919514", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "STUDENT_VLAN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919515", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "questlab", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919516", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "iPEOPLEremote", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919518", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "LAN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919519", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "RALSplusLAN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919521", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "PhilipsSupport", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919522", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "STRAT_SOL.NET.INTERNAL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919523", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MVOrtho.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919525", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "LAN_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919526", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MilleniumPACSnat", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919527", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MVOatJSC.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919529", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SENTRYDS.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919530", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SENTRYDS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919532", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "pacs.net-01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919533", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "LAN-01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919534", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MilleniumPACSnat-10.205.56.127", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919536", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "obj-10.205.56.128-10.205.56.255", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919537", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "obj_any", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919538", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "obj_any-03", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919540", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "NUVODIA_NETWORK_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919541", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GUEST_WLAN_NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919543", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GUEST_NETWORK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919544", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "VENDOR_WLAN_NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919545", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CREDITCARD_CAFE_EXTERNAL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919547", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CREDITCARD_CAFE2_EXTERNAL2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919548", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CREDITCARD_CAFE_EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919549", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "STRAT_SOL.NET.INTERNAL2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919551", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919552", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919554", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919555", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "EXPANSE_VLAN4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919556", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "QUEST.VPN.EXTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919558", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CHC.OPTUM.NAT.INTERNAL.SUB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919559", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919561", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919562", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "BARRACUDA.CLOUD.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919563", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919565", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "ACRONIS.EXTERNAL.RANGE4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919566", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "GESUPPORT.INTERNAL.NET", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919567", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CLEARWATER3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919569", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "CLEARWATER4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919570", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "backblazeb2.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919572", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "HANYS.EXTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919573", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "HANYS.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919574", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "HANYS.EXTERNAL.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919576", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "Clearwater.Internal.Peer.Range", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919577", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "NLH.Firewall.Range.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919579", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "SSI.EXTERNAL.PEER.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919580", "validation_status": null}, {"success": false, "action": "failed", "object_type": "NetworkObject", "object_name": "MICROSOFTSTREAM.COM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:31:35.919581", "validation_status": null}], "checkpoint_version": "2.0"}