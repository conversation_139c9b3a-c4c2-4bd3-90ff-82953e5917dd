{"session_id": "migration_1754344722", "timestamp": "2025-08-04T14:59:56.668264", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost1", "✅ Updated host: TestHost2", "✅ Updated host: TestHost3"], "duration_seconds": 23.287909269332886, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork1", "✅ Updated network: TestNetwork2"], "duration_seconds": 17.15118908882141, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestHTTP", "✅ Updated service: TestHTTPS", "✅ Updated service: TestSSH"], "duration_seconds": 9.325829029083252, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 1, "created": 0, "updated": 1, "failed": 0, "skipped": 0, "details": ["✅ Updated object_group: TestNetworkGroup"], "duration_seconds": 3.545584201812744, "success_rate": 0.0}, "phase1_service_groups": {"phase_name": "phase1_service_groups", "total_objects": 1, "created": 0, "updated": 1, "failed": 0, "skipped": 0, "details": ["✅ Updated service_group: TestWebServices"], "duration_seconds": 3.017983913421631, "success_rate": 0.0}, "phase1_access_rules": {"phase_name": "phase1_access_rules", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule1 - No Access Control Policy found - cannot create access rules"], "duration_seconds": 9.872738122940063, "success_rate": 0.0}}, "current_phase": "phase1_access_rules", "phase_result": {"phase_name": "phase1_access_rules", "total_objects": 1, "created": 0, "updated": 0, "failed": 1, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule1 - No Access Control Policy found - cannot create access rules"], "duration_seconds": 9.872738122940063, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "TestRule1", "object_id": null, "message": "No Access Control Policy found - cannot create access rules", "data": null, "phantom_object": false, "timestamp": "2025-08-04T14:59:56.667946", "validation_status": null}], "checkpoint_version": "2.0"}