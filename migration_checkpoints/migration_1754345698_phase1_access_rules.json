{"session_id": "migration_1754345698", "timestamp": "2025-08-04T15:17:50.439727", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 0, "updated": 5, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost_WebServer", "✅ Updated host: TestHost_DatabaseServer", "✅ Updated host: TestHost_FileServer", "✅ Updated host: TestHost_PrintServer", "✅ Updated host: TestHost_BackupServer"], "duration_seconds": 36.6576030254364, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork_LAN", "✅ Updated network: TestNetwork_DMZ", "✅ Updated network: TestNetwork_Management"], "duration_seconds": 24.02049994468689, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 4, "created": 0, "updated": 4, "failed": 0, "skipped": 0, "details": ["✅ Updated service: TestService_HTTP_8080", "✅ Updated service: TestService_HTTPS_8443", "✅ Updated service: TestService_Database", "✅ Updated service: TestService_FTP"], "duration_seconds": 12.104196786880493, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated object_group: TestGroup_Servers", "✅ Updated object_group: TestGroup_Networks"], "duration_seconds": 6.6342127323150635, "success_rate": 0.0}, "phase1_service_groups": {"phase_name": "phase1_service_groups", "total_objects": 1, "created": 0, "updated": 1, "failed": 0, "skipped": 0, "details": ["✅ Updated service_group: TestGroup_WebServices"], "duration_seconds": 2.9946770668029785, "success_rate": 0.0}, "phase1_access_rules": {"phase_name": "phase1_access_rules", "total_objects": 2, "created": 0, "updated": 0, "failed": 2, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule_AllowWeb - Failed to create access rule: super(): no arguments", "❌ Failed to create access_rule: TestRule_AllowDatabase - Failed to create access rule: super(): no arguments"], "duration_seconds": 77.41527581214905, "success_rate": 0.0}}, "current_phase": "phase1_access_rules", "phase_result": {"phase_name": "phase1_access_rules", "total_objects": 2, "created": 0, "updated": 0, "failed": 2, "skipped": 0, "details": ["❌ Failed to create access_rule: TestRule_AllowWeb - Failed to create access rule: super(): no arguments", "❌ Failed to create access_rule: TestRule_AllowDatabase - Failed to create access rule: super(): no arguments"], "duration_seconds": 77.41527581214905, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "TestRule_AllowWeb", "object_id": null, "message": "Failed to create access rule: super(): no arguments", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:17:11.775251", "validation_status": null}, {"success": false, "action": "failed", "object_type": "AccessRule", "object_name": "TestRule_AllowDatabase", "object_id": null, "message": "Failed to create access rule: super(): no arguments", "data": null, "phantom_object": false, "timestamp": "2025-08-04T15:17:50.439376", "validation_status": null}], "checkpoint_version": "2.0"}