{"session_id": "migration_1754345222", "timestamp": "2025-08-04T15:09:25.660985", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 5, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created host: TestHost_WebServer", "✅ Created host: TestHost_DatabaseServer", "✅ Created host: TestHost_FileServer", "✅ Created host: TestHost_PrintServer", "✅ Created host: TestHost_BackupServer"], "duration_seconds": 38.063902139663696, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 3, "created": 3, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created network: TestNetwork_LAN", "✅ Created network: TestNetwork_DMZ", "✅ Created network: TestNetwork_Management"], "duration_seconds": 24.722725868225098, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 4, "created": 4, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created service: TestService_HTTP_8080", "✅ Created service: TestService_HTTPS_8443", "✅ Created service: TestService_Database", "✅ Created service: TestService_FTP"], "duration_seconds": 26.82383894920349, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 2, "created": 2, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created object_group: TestGroup_Servers", "✅ Created object_group: TestGroup_Networks"], "duration_seconds": 35.037697076797485, "success_rate": 0.0}, "phase1_service_groups": {"phase_name": "phase1_service_groups", "total_objects": 1, "created": 1, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created service_group: TestGroup_WebServices"], "duration_seconds": 12.20233702659607, "success_rate": 0.0}}, "current_phase": "phase1_service_groups", "phase_result": {"phase_name": "phase1_service_groups", "total_objects": 1, "created": 1, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created service_group: TestGroup_WebServices"], "duration_seconds": 12.20233702659607, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "created", "object_type": "PortObjectGroup", "object_name": "TestGroup_WebServices", "object_id": "005056BF-7B88-0ed3-0000-017187313404", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:09:25.660721", "validation_status": null}], "checkpoint_version": "2.0"}