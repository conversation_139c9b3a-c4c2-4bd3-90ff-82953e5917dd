{"session_id": "migration_1754343708", "timestamp": "2025-08-04T14:43:03.852543", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 3, "created": 0, "updated": 3, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost1", "✅ Updated host: TestHost2", "✅ Updated host: TestHost3"], "duration_seconds": 22.75806713104248, "success_rate": 0.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 2, "created": 0, "updated": 2, "failed": 0, "skipped": 0, "details": ["✅ Updated network: TestNetwork1", "✅ Updated network: TestNetwork2"], "duration_seconds": 16.493091106414795, "success_rate": 0.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 3, "created": 0, "updated": 0, "failed": 3, "skipped": 0, "details": ["❌ Failed to update service: TestHTTP - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: TestHTTPS - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType", "❌ Failed to update service: TestSSH - fmcapi put() failed: fmcapi.api_objects.apiclasstemplate.APIClassTemplate.parse_kwargs() argument after ** must be a mapping, not NoneType"], "duration_seconds": 18.183600902557373, "success_rate": 0.0}, "phase1_object_groups": {"phase_name": "phase1_object_groups", "total_objects": 1, "created": 1, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created object_group: TestNetworkGroup"], "duration_seconds": 15.315642833709717, "success_rate": 0.0}}, "current_phase": "phase1_object_groups", "phase_result": {"phase_name": "phase1_object_groups", "total_objects": 1, "created": 1, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created object_group: TestNetworkGroup"], "duration_seconds": 15.315642833709717, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "created", "object_type": "NetworkGroup", "object_name": "TestNetworkGroup", "object_id": "005056BF-7B88-0ed3-0000-017187308374", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T14:43:03.852276", "validation_status": null}], "checkpoint_version": "2.0"}