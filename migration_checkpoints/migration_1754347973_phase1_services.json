{"session_id": "migration_1754347973", "timestamp": "2025-08-04T16:09:57.279808", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 100, "created": 99, "updated": 0, "failed": 1, "skipped": 0, "details": ["✅ Updated host: RadSaratoga", "✅ Updated host: RadAmsMem", "✅ Updated host: RadStMarys", "✅ Updated host: RadSeton", "✅ Updated host: RadBellevue", "❌ Failed host: NexTalkSec - Create failed: fmcapi post() failed - no ID returned."], "duration_seconds": 757.3419950008392, "success_rate": 99.0}, "phase1_networks": {"phase_name": "phase1_networks", "total_objects": 20, "created": 20, "updated": 0, "failed": 0, "skipped": 0, "details": ["✅ Created network: TeleMedVT3", "✅ Created network: TelemedVT4", "✅ Created network: TelemedVT5", "✅ Created network: TeleMedVT1", "✅ Created network: Medent.VPN.net"], "duration_seconds": 158.29800391197205, "success_rate": 100.0}, "phase1_services": {"phase_name": "phase1_services", "total_objects": 10, "created": 7, "updated": 0, "failed": 3, "skipped": 0, "details": ["❌ Failed service: obj-tcp-eq-80 - Create failed: fmcapi post() failed - no ID returned.", "✅ Created service: obj-tcp-eq-15002", "✅ Created service: obj-tcp-eq-15331", "✅ Created service: obj-tcp-eq-3389", "✅ Created service: obj-tcp-eq-2222", "✅ Created service: obj-tcp-eq-6544", "❌ Failed service: obj-tcp-eq-23 - Create failed: fmcapi post() failed - no ID returned.", "❌ Failed service: obj-tcp-eq-5631 - <PERSON><PERSON> failed: fmcapi post() failed - no ID returned."], "duration_seconds": 105.40840601921082, "success_rate": 70.0}}, "current_phase": "phase1_services", "phase_result": {"phase_name": "phase1_services", "total_objects": 10, "created": 7, "updated": 0, "failed": 3, "skipped": 0, "details": ["❌ Failed service: obj-tcp-eq-80 - Create failed: fmcapi post() failed - no ID returned.", "✅ Created service: obj-tcp-eq-15002", "✅ Created service: obj-tcp-eq-15331", "✅ Created service: obj-tcp-eq-3389", "✅ Created service: obj-tcp-eq-2222", "✅ Created service: obj-tcp-eq-6544", "❌ Failed service: obj-tcp-eq-23 - Create failed: fmcapi post() failed - no ID returned.", "❌ Failed service: obj-tcp-eq-5631 - <PERSON><PERSON> failed: fmcapi post() failed - no ID returned."], "duration_seconds": 105.40840601921082, "success_rate": 70.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-80", "object_id": null, "message": "Create failed: fmcapi post() failed - no ID returned.", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:08:34.316735", "validation_status": null}, {"success": true, "action": "created", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-15002", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:08:40.404085", "validation_status": null}, {"success": true, "action": "created", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-15331", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:08:46.660412", "validation_status": null}, {"success": true, "action": "created", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-3389", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:08:53.055674", "validation_status": null}, {"success": true, "action": "created", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-2222", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:08:59.345205", "validation_status": null}, {"success": true, "action": "created", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-6544", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:09:05.670406", "validation_status": null}, {"success": true, "action": "created", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-2020", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:09:12.021321", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-23", "object_id": null, "message": "Create failed: fmcapi post() failed - no ID returned.", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:09:30.460338", "validation_status": null}, {"success": true, "action": "created", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-15031", "object_id": null, "message": "Created after bulk failure", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:09:36.650616", "validation_status": null}, {"success": false, "action": "failed", "object_type": "ProtocolPortObject", "object_name": "obj-tcp-eq-5631", "object_id": null, "message": "Create failed: fmcapi post() failed - no ID returned.", "data": null, "phantom_object": false, "timestamp": "2025-08-04T16:09:57.279135", "validation_status": null}], "checkpoint_version": "2.0"}