{"session_id": "migration_1754345698", "timestamp": "2025-08-04T15:15:41.728445", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 0, "updated": 5, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost_WebServer", "✅ Updated host: TestHost_DatabaseServer", "✅ Updated host: TestHost_FileServer", "✅ Updated host: TestHost_PrintServer", "✅ Updated host: TestHost_BackupServer"], "duration_seconds": 36.6576030254364, "success_rate": 0.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 5, "created": 0, "updated": 5, "failed": 0, "skipped": 0, "details": ["✅ Updated host: TestHost_WebServer", "✅ Updated host: TestHost_DatabaseServer", "✅ Updated host: TestHost_FileServer", "✅ Updated host: TestHost_PrintServer", "✅ Updated host: TestHost_BackupServer"], "duration_seconds": 36.6576030254364, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost_WebServer", "object_id": "005056BF-7B88-0ed3-0000-017187313138", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:15:12.436411", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost_DatabaseServer", "object_id": "005056BF-7B88-0ed3-0000-017187313157", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:15:19.747640", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost_FileServer", "object_id": "005056BF-7B88-0ed3-0000-017187313176", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:15:27.278245", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost_PrintServer", "object_id": "005056BF-7B88-0ed3-0000-017187313195", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:15:34.407531", "validation_status": null}, {"success": true, "action": "updated", "object_type": "HostObject", "object_name": "TestHost_BackupServer", "object_id": "005056BF-7B88-0ed3-0000-017187313214", "message": null, "data": {}, "phantom_object": false, "timestamp": "2025-08-04T15:15:41.728134", "validation_status": null}], "checkpoint_version": "2.0"}