{"session_id": "migration_1754339420", "timestamp": "2025-08-04T13:30:30.598779", "connection_type": "fmcapi", "completed_phases": {"phase1_hosts": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0018858909606933594, "success_rate": 0.0}}, "current_phase": "phase1_hosts", "phase_result": {"phase_name": "phase1_hosts", "total_objects": 629, "created": 0, "updated": 0, "failed": 629, "skipped": 0, "details": ["❌ Failed host: RadSaratoga - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadAmsMem - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadStMarys - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadSeton - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: RadBellevue - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: CITRIXFS02 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: XENAPP30 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC_C-iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: NLHNAS12 - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "❌ Failed host: MAGIC-D_iDRAC - Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'"], "duration_seconds": 0.0018858909606933594, "success_rate": 0.0}, "phantom_objects": [], "object_details": [{"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadSaratoga", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595150", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadAmsMem", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595159", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadStMarys", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595173", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadSeton", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595175", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RadBellevue", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595177", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CITRIXFS02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595179", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP30", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595181", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_C-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595183", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHNAS12", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595184", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC-D_iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595187", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595188", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHNAS11", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595190", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_A-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595192", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_E-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595194", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGIC_D-NEWiSCSI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595196", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "UNITY-SDC_iSCSI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595197", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-WEB01-WS01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595199", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595201", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595203", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595204", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595207", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595209", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICF", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595211", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595214", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DICTATION02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595216", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDILIVE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595219", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_FS_SEC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595222", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CTScanner", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595224", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON><PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595227", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RandF", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595229", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PetLinks1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595232", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PetLinks2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595234", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAGICD3_DRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595237", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LIEBERT.2FL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595239", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal132", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595241", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal133", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595244", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal144", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595246", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal145", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595248", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal176", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595250", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal177", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595252", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal194", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595254", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Cardinal195", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595256", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON><PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595258", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "medinote2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595260", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "medinote1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595262", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN3.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595264", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN5.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595266", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN1.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595268", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN4.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595270", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN9.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595272", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN10.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595274", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN11.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595276", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595278", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595280", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595282", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595284", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MilleniumPACS5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595286", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHEXCHANGE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595288", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB_VIP_NETSCALER1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595290", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595292", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_CACHE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595294", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_STORE1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595295", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_STORE2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595297", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_STORE144", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595299", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ResnickPacs1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595301", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ResnickPACS2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595304", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleRadPC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595306", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CatScan", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595307", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PERTH_MRI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595309", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PETScanCT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595311", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XELERIS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595313", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INFINIA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595315", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "D5000", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595317", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ultrasound1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595319", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ultrasound2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595321", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ultrasound3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595323", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KonicaJM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595325", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Konicardr1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595328", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KonicaRdr2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595330", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KonicaRdr3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595332", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595334", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "US_LOGI_E9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595336", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk242", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595338", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk243", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595340", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk244", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595342", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk245", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595344", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk246", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595346", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalk247", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595358", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalkPrime", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595360", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NexTalkSec", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595362", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Spantel.Prod", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595364", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SpantelHL7.test", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595367", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRXcenter2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595369", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRXcenter3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595371", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRXcenter1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595373", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRxChicago", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595377", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "eRxDallas", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595379", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MedentRemote", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595381", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Medent.RPTS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595383", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "STpc.rtr", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595385", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "spc.rtr", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595387", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ppc.pix", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595389", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.ps1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595391", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.ps2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595393", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.ps3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595395", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.ps4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595397", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.syn1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595399", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.syn2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595401", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.orpc1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595403", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.orpc2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595406", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.orpc3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595408", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595410", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595412", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595414", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.KPServer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595416", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595418", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "smha.mammo", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595420", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "smha.pacsed30", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595422", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "smha.pacrd06", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595424", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595427", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595429", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SHMA.read7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595431", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read8", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595433", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read9", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595435", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.read10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595437", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.Synapse.Dest", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595438", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P-DI-MGR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595440", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_READ3_NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595442", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_CIO1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595444", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_DI_NUMED", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595447", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MAMMO40", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595449", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MOMMO41", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595451", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "phil<PERSON><PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595453", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSP19WEB.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595455", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595457", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595459", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PCC_BILL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595461", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595463", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595465", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_REP4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595467", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SOPHOSEMAIL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595468", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SOPHOSWEB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595470", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NATHAN6.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595472", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ORTIZ_LT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595474", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "p_mis_netadmin", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595476", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_OR3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595478", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_OR1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595480", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_OR2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595482", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS_DI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595484", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDC1_IPMI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595486", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDC2_IPMI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595488", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AAI.120", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595490", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AAI.124", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595492", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AAI.125", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595494", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AAI.52", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595496", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INTERLACE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595498", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHUTILITY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595500", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHTEST01-NIC2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595502", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BPC-UPS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595504", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ALBANYMED.IN.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595506", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ALBANYMED.IN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595508", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "hixny.com_integration", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595509", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "hixny.com_prod", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595511", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "webservices.hixny.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595513", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDITEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595515", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595517", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT03", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595519", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB_VIRTUALIP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595521", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB_VIRTUALIP_NETSCALER2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595523", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT05", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595525", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PHA_WS3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595527", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PHA_WS2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595529", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_MR_SCAN1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595531", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "easyeeg", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595533", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VENUE50_p_pacs_cdburn", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595535", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "integration.hixny.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595537", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Hixney.net_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595539", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CITRIX_STOREFRONT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595541", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P-IT-MGR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595543", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETSCALER.VPX", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595545", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETSCALER.WEB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595546", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETSCALERSUBNETIP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595548", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDC01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595550", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDC02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595552", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_CISCO_01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595554", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "p_mis_netadmin2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595556", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Direct.Hixny.Com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595558", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Healthstream.SMPT.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595560", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Hixny.net", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595562", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Hixny.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595564", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "statrad.hl7.test", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595566", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PAT_FIN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595568", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHENDO01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595570", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHENDO01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595572", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ENDOWORKS02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595574", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ENDOWORKS03", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595576", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IS_PACS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595578", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "retsolinc2.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595580", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "retsolinc3.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595582", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SophosMailExt", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595584", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "IRIS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595586", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "smtp.biz.rr.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595588", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Hypertype", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595590", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MVP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595592", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LeaderHFTPsite", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595594", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LeaderHFTPsite2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595596", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "stentor.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595598", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TOGARM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595600", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Infotrak", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595602", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "sftp.lifethc.org", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595604", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleVideo1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595606", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Televid2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595608", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CONNECTPLUS01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595609", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VeriquestPC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595611", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VeriquestSite", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595613", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT_PORTAL_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595615", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HYPER-_REPLICA_BROKER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595617", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Sodexho", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595619", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Provation-out", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595621", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VeriquestServer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595623", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON><PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595625", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "IMO_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595627", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WWW.UPTODATE.COM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595629", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP22", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595631", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HYPER-V_CLUSTER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595633", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENTPORTAL.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595635", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "remote.nlh.org", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595637", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mail.nlh.org", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595639", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DIRECT.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595641", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleMed_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595643", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDI.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595644", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCISCO", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595646", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LabCorp3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595648", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LabCorpDev", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595651", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LabCorpProd", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595653", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TheOutsourceGroup", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595654", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleradIT_Millenium1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595656", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TeleradIT_Millenium2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595658", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FastChart.Inside", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595660", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ellis.inside", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595662", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "STATRAD.DR.SVR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595664", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHTEST01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595666", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH.ORG.EXTERNAL.FORMS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595668", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-************", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595670", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-***********", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595685", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-***********", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595687", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-************", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595689", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-***********", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595691", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-************", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595693", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-************", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595695", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Ellis.Peer.New", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595697", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH.PEER.INTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595699", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Medent.Peer.New.", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595701", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.MBMS.MILLENIUMBILLING.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595703", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.MBMS.MILLENIUMBILLING.INTERNAL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595706", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MCKESSON.MC.PHARM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595708", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "newsync3.mkesson.com", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595710", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "obj-0.0.0.0", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595712", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.pacs1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595714", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.pacs2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595716", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.pacs3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595718", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS.VCE1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595720", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS.VCE2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595722", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS.VCE3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595724", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PACS.VCE4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595726", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT_NAS_INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595728", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595730", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595732", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PDX.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595734", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PDX.External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595736", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.PEER.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595738", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.INTERNAL1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595740", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XCHANGEWORX.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595742", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETSCALER.NLHRESTAPI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595743", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA_VPN_NLH_PEER1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595745", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA_VPN_NLH_PEER2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595747", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FIREPOWER_VM_ESXI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595749", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.READ.10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595751", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ESRS_EMC_VIRTUAL_APPLIANCE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595753", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595755", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595757", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RESTFULAPI.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595759", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595761", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595763", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595765", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595767", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595768", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_MIS52_DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595770", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDITEST_SENDTRYDS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595772", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP25", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595774", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "skype.nlh.org_external", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595776", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "st_netadmin", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595778", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.RAD.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595780", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MVO_AMST_PEER_NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595782", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "GUEST_INTERFACE_EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595784", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "VENDOR_EXTERNAL_INTERFACE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595786", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "p_mis_netadmin.dmz", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595788", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-ISWEB.DMZVR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595790", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AMC.PACS.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595792", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BRIAN_DHCP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595793", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BPC.External", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595795", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_MIS_CISCOMON", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595797", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP17", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595799", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP18", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595801", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP19", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595803", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_MIS52.WAYNE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595805", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETADMIN.DMZ.TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595807", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "EUGENE10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595809", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDITECHAPIVIP1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595811", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDITECHAPIVIP2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595813", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "StratSolution.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595815", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_PHA_PDX1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595817", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPRTG01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595819", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RCARE-SERVER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595821", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDMZ01_SWITCH", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595823", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595825", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PRTG.NLH.ORG.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595827", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKLINE.VPN.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595829", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "UNITEDLABNETWORK.VPN.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595831", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_18.204.173.205", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595833", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKLINE.LDAP.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595835", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT.NIMBLE.INSIDE.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595837", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595839", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT.NIMBLE.OPENVPN.OUTSIDE.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595840", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ROBOT_GE_VOT_TRAIN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595842", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BILL_BAIRD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595844", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS29-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595846", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DOLBEY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595848", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DOLBEYTEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595850", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH_DCDS_9300s", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595852", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Schumacher.Inside1.new.ADTPROD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595854", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Schumacher.Inside2.new.ADTTEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595856", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Schumacher.VPN.Peer.New", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595858", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENT-EXPORT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595860", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUEST.VPN.PEER.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595862", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUEST.VPN.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595864", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "<PERSON>", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595866", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.PEER.INTERNAL.TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595868", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.PEER.INTERNAL.PROD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595870", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSP19OFCWEB.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595872", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENTPORTAL.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595874", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mtrestexpapis-live01.nlh.org.external", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595875", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mtrestexpapis-test01.nlh.org.external", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595877", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mtrestexpapis-test01.nlh.org.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595879", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "mtrestexpapis-live01.nlh.org.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595881", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595883", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.IP1.PROD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595885", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHANGE.HEALTHCARE.EXTERNAL.IP2.TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595887", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI.T.BG01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595889", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHC.EXTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595891", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHC.EXTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595893", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-T-BG01.CHC.NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595895", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDILIVE.CHC.NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595897", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDITEST.CHC.NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595899", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-T-BG01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595901", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHFTP01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595903", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SR_STACK_01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595904", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-BG01.nlh.org", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595906", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-BG04.CHC.NAT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595908", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-BG04", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595910", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHC.EXTERNAL.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595912", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595914", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WEBSSO.MEDITECH.COM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595916", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WEBSSO2FA.MEDITECH.COM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595918", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.INTERNAL.PUSH_SERVER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595920", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIXNY.INTERNAL.TESTHUB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595922", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FIRECALL_JSC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595924", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FIRECALLSYSTEM_ENDPOINTS1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595926", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FIRECALLSYSTEM_ENDPOINTS2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595928", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKLINE.VPN.PEER2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595929", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKLINE.LDAP.INTERNAL2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595931", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_35.155.201.32", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595933", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BANDWIDTH_TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595935", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IS_1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595937", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IS_3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595939", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IT_COOR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595941", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IT_TECH1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595943", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HIRAM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595945", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "RYAN", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595947", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NICK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595949", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "IT_TEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595951", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P-BOARDROOM1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595953", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS08", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595955", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS21-iLO", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595957", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHADMINCENTER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595958", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP24", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595960", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SQL01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595962", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FAXSERVER.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595964", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHFUSION", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595966", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BACKUPEXEC01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595968", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ARCHIVE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595970", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PRINT", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595972", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBACKUP", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595974", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INTERLACETEST", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595976", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHMONITOR01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595978", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SANPHNHM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595979", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK_BCR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595981", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK_VISTA2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595983", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK_VISTA1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595985", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK_LCM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595987", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595989", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS31-iDRAC", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595992", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP21", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595994", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCITRIXGATEWAY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595995", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ST_NETADMIN2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595997", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DR_CECIL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.595999", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P_IS_RAMANI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596001", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "US_LOGU_E9_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596003", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596005", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLI-BG13", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596007", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHTESTMOBILE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596009", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA.NLH.ORG.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596011", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BANDWIDTH_TEST_2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596013", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_192.168.253.161", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596015", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_172.16.41.10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596017", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Barracuda.Web.NLH.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596018", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Barracuda.Email.NLH.Internal", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596020", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596022", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH.PEER.INTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596024", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_216.41.86.228", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596026", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DMZ_TEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596028", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DUOTEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596030", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DUOTEST.NLH.ORG.DMZ", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596032", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BARRACUDA.EMAIL.INSIDE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596034", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH.CORE.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596036", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DCDS.CORE.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596038", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "GPC_STACK", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596040", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSSI", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596042", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBRAUNPUMPS.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596044", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBRAUNPUMPS.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596045", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "P-ITMGR", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596047", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDIVATOR66838147", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596049", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDIVATOR66838143", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596051", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AMC.VPN.PEER.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596053", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.NEW.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596055", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.NEW.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596057", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ULN.VPN.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596059", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CISCOPRIME.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596061", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CISCOPRIMEINF", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596063", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MIS_TEST2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596065", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CISCONMON", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596066", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SYSLOGSERVER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596068", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA-QIE.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596070", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA.INTERLACE.PEER.EXTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596072", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WLC1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596074", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "sendgrid.net.virus", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596076", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA.INTERLACE.PEER.EXTERNAL2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596078", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "love.explorethebest.com.spam.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596080", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "love.explorethebest.com.spam.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596082", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "love.explorethebest.com.spam.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596084", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CISCO.WSA.INTERNAL", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596085", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HARRIET.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596087", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS32.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596089", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596091", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS19B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596093", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "WILLYWONKA.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596095", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSYN01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596097", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSYN02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596099", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSYN03.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596101", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSYN04.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596103", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSP19APP.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596105", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS18C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596107", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS19C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596109", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS14.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596110", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS26D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596112", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DDPC.FIREALARM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596114", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCUIS16B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596116", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS17B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596118", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS19A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596120", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SUMMIT.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596122", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS25A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596124", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ONEVIEW.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596126", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DR1.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596127", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS26B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596129", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBACKUP02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596131", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KRONOSNEW.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596133", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SMHA.RAD.EXTERNAL.NEW", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596135", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BARRACUDA.LDAP.EXTERNAL.PEER.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596137", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BARRACUDA.LDAP.EXTERNAL.PEER.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596139", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BARRACUDA.LDAP.EXTERNAL.PEER.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596141", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "REYHEALTH.EXTERNAL.EXTERNAL.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596143", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "REYHEALTH.EXTERNAL.EXTERNAL.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596145", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CHC.OPTUM.EXTERNAL.VPN.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596147", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS18D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596149", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "STREAMTASK.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596150", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "GPSUPPORT.VPN.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596152", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.AWSERVER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596154", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.AWSERVER.ILO", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596156", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.CTSCANNER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596158", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.CT.ADV.WS", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596160", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.GE.MAMMO.INTERFACE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596162", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MAMMO.SHUTTLE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596164", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MRI.ALLIANCE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596166", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MUSE01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596168", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MUSE02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596170", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MUSE03", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596172", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.MAMMO", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596173", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.NUCMEDCAMERA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596175", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.PETCTVIEWER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596177", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.PERTH.XRAY", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596179", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.R.AND.F", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596196", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.ROOMA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596198", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DI.XELERIS.NM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596200", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596202", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CLEARWATER1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596204", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CLEARWATER2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596206", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "JELMENDORFSPAM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596208", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS25C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596210", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PROVMDAPP.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596212", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPROVMDORACLE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596214", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHMUSE01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596216", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHMUSE02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596218", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS16A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596220", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "DESIGO.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596222", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596224", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596226", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596228", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIOUS01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596230", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPRTGPROBE04", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596232", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10C", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596233", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS28", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596235", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596238", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PATIENT.CONNECT.ARTERA.INTERNAL.PEER.4", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596239", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS07.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596241", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NURSECALLAPP.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596243", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHBRAUNPUMPS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596245", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "BRAUNWEB", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596247", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS09A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596249", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS09B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596251", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS09C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596253", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCISCO.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596255", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS13.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596257", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SQLTEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596259", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHMONITOR.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596261", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPRTG01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596262", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHKIWISYSLOG01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596264", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS17A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596266", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596268", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CITRIXSF.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596270", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHWEB01..NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596272", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "AVAYACALLACCT.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596274", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHSSI.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596276", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "TEMPTRAK.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596278", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PRINT.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596280", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUICKCHARGE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596282", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH3M.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596284", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS19D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596286", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHAV01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596287", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS23A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596289", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS23B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596291", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS23C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596293", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS23D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596295", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDHCP01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596297", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS25B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596299", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS21.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596301", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CENTRALINK.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596303", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS27.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596305", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596307", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE03.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596309", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "KRONOSTEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596311", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE-TEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596312", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INTERLACETEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596314", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHINT-TEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596316", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS29.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596318", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHFUSION.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596320", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE-CCGHL7.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596322", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS31.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596324", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596326", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596328", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCA.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596330", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHPRTGPROBE3.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596332", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS10D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596334", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CODONICS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596336", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MDITEST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596338", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "CITRIXFS02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596339", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "XENAPP02.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596341", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MEDENTPRINT01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596343", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HEALTHTOUCH01.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596345", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS18A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596347", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "INTERLACE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596349", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NOVA-QIE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596351", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS18B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596353", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHUTILITY.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596355", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHCODONICS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596357", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHLICENSE.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596359", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "HPDMAN.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596361", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "SCVMM.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596362", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS24A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596364", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS24B.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596366", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS24C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596368", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS24D.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596370", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS26A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596372", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ESICALLACCT26A.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596374", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ESRS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596376", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHDRFIRST.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596378", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHELOCK.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596380", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS26C.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596382", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "COBAS.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596384", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "PRADEV.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596385", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLHADMINCENTER.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596387", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS28.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596389", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NURSECALLHD.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596391", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NLH-iUV.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596393", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "LUCIUS30.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596395", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE-APP.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596397", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MUSE-NXWEB.NLH.ORG", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596399", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Clearwater.External.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596401", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ASA01", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596403", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "ASA02", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596405", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_172.16.201.35", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596406", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_192.168.178.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596408", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596410", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "QUICKCHARGE.EXTERNAL.WHITELIST.PEER.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596412", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MMI.BILLING.EXTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596414", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "MMI.BILLING.INTERNAL.PEER", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596416", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.MEDICOM", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596418", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.AMBRA", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596420", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.POWERSHARE", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596422", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NYOH.INTERNAL.CLOUD", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596424", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Nuvodia.OneOncology.Cloud.External.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596426", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "Nuvodia.OneOncology.Cloud.Internal.Peer", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596428", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NETWORK_OBJ_162.245.33.10", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596430", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "NUVODIA.INTERNAL.NEW.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596432", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.1", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596434", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.2", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596435", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.3", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596437", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.5", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596439", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.6", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596441", "validation_status": null}, {"success": false, "action": "failed", "object_type": "HostObject", "object_name": "FRESHWORKS.EXCLUSIONS.7", "object_id": null, "message": "Bulk operation exception: '<' not supported between instances of 'NoneType' and 'str'", "data": null, "phantom_object": false, "timestamp": "2025-08-04T13:30:30.596443", "validation_status": null}], "checkpoint_version": "2.0"}