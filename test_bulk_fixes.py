#!/usr/bin/env python3
"""
Quick test to verify bulk API fixes

This script tests the URL construction and authentication fixes.
"""

import json

def create_minimal_test():
    """Create minimal test config"""
    config = {
        "api_calls": {
            "host_objects": {
                "data": [
                    {
                        "name": "QuickTest_Host_001",
                        "type": "Host",
                        "value": "*************",
                        "description": "Quick test host"
                    },
                    {
                        "name": "QuickTest_Host_002",
                        "type": "Host", 
                        "value": "*************",
                        "description": "Quick test host 2"
                    },
                    {
                        "name": "QuickTest_Host_003",
                        "type": "Host",
                        "value": "*************", 
                        "description": "Quick test host 3"
                    },
                    {
                        "name": "QuickTest_Host_004",
                        "type": "Host",
                        "value": "*************",
                        "description": "Quick test host 4"
                    },
                    {
                        "name": "QuickTest_Host_005",
                        "type": "Host",
                        "value": "*************",
                        "description": "Quick test host 5"
                    },
                    {
                        "name": "QuickTest_Host_006",
                        "type": "Host",
                        "value": "*************",
                        "description": "Quick test host 6"
                    }
                ]
            }
        }
    }
    
    with open("quick_test_bulk.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("✅ Created quick_test_bulk.json with 6 host objects")
    print("\n🔧 To test the fixes:")
    print("   python.exe .\\fmc_migration_v2.py .\\quick_test_bulk.json --overwrite --quiet")
    print("\n🔍 Look for:")
    print("   - No 'https://10.168.178.14https://...' URLs")
    print("   - No 'No authentication method available' errors")
    print("   - Proper bulk API completion")

if __name__ == "__main__":
    create_minimal_test()
