#!/usr/bin/env python3
"""
Quick test to verify bulk API performance improvements

This script creates test configurations and measures the actual performance
difference between bulk and individual API operations.

Usage:
    python test_bulk_performance.py

Author: AI Assistant
Version: 2.0
Date: 2025-08-04
"""

import json
import time
import sys
import subprocess
from pathlib import Path

def create_test_config(num_objects: int, filename: str):
    """Create a test configuration with specified number of objects"""
    config = {
        "api_calls": {
            "host_objects": {
                "data": []
            }
        }
    }
    
    # Create test host objects
    for i in range(num_objects):
        config["api_calls"]["host_objects"]["data"].append({
            "name": f"PerfTest_Host_{i+1:04d}",
            "type": "Host",
            "value": f"192.168.{(i // 254) + 100}.{(i % 254) + 1}",
            "description": f"Performance test host {i+1}"
        })
    
    with open(filename, 'w') as f:
        json.dump(config, f, indent=2)
    
    return filename

def run_migration_test(config_file: str, use_bulk: bool = True) -> dict:
    """Run migration test and measure performance"""
    
    # Prepare command
    cmd = ["python", "fmc_migration_v2.py", config_file, "--quiet"]
    if not use_bulk:
        cmd.append("--no-bulk")
    
    print(f"🔄 Running {'bulk' if use_bulk else 'individual'} API test...")
    print(f"   Command: {' '.join(cmd)}")
    
    # Measure execution time
    start_time = time.time()
    
    try:
        # Run the migration
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        duration = time.time() - start_time
        
        # Parse results from output
        success = result.returncode == 0
        output_lines = result.stdout.split('\n') if result.stdout else []
        error_lines = result.stderr.split('\n') if result.stderr else []
        
        # Look for performance indicators in output
        created_count = 0
        failed_count = 0
        
        for line in output_lines + error_lines:
            if "created" in line.lower() and "failed" in line.lower():
                # Try to extract numbers from summary line
                try:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if "created" in part.lower() and i > 0:
                            created_count = int(parts[i-1])
                        elif "failed" in part.lower() and i > 0:
                            failed_count = int(parts[i-1])
                except:
                    pass
        
        return {
            'success': success,
            'duration': duration,
            'created': created_count,
            'failed': failed_count,
            'output': result.stdout,
            'errors': result.stderr,
            'use_bulk': use_bulk
        }
        
    except subprocess.TimeoutExpired:
        duration = time.time() - start_time
        return {
            'success': False,
            'duration': duration,
            'created': 0,
            'failed': 0,
            'output': '',
            'errors': 'Test timed out after 5 minutes',
            'use_bulk': use_bulk
        }
    except Exception as e:
        duration = time.time() - start_time
        return {
            'success': False,
            'duration': duration,
            'created': 0,
            'failed': 0,
            'output': '',
            'errors': str(e),
            'use_bulk': use_bulk
        }

def main():
    """Main test function"""
    print("🧪 FMC Bulk API Performance Test")
    print("=" * 50)
    
    # Test with different object counts
    test_counts = [10, 25, 50]
    
    for count in test_counts:
        print(f"\n📊 Testing with {count} objects...")
        
        # Create test configuration
        config_file = f"perf_test_{count}.json"
        create_test_config(count, config_file)
        print(f"✅ Created test config: {config_file}")
        
        # Test individual API
        print(f"\n🔄 Testing individual API...")
        individual_result = run_migration_test(config_file, use_bulk=False)
        
        # Test bulk API
        print(f"\n🚀 Testing bulk API...")
        bulk_result = run_migration_test(config_file, use_bulk=True)
        
        # Compare results
        print(f"\n📈 Results for {count} objects:")
        print(f"   Individual API: {individual_result['duration']:.2f}s (success: {individual_result['success']})")
        print(f"   Bulk API:       {bulk_result['duration']:.2f}s (success: {bulk_result['success']})")
        
        if individual_result['duration'] > 0 and bulk_result['duration'] > 0:
            improvement = ((individual_result['duration'] - bulk_result['duration']) / individual_result['duration']) * 100
            print(f"   Improvement:    {improvement:.1f}% faster")
            
            if improvement > 0:
                print(f"   Time saved:     {individual_result['duration'] - bulk_result['duration']:.2f}s")
            else:
                print(f"   Time penalty:   {bulk_result['duration'] - individual_result['duration']:.2f}s")
        
        # Show any errors
        if individual_result['errors']:
            print(f"   Individual errors: {individual_result['errors'][:100]}...")
        if bulk_result['errors']:
            print(f"   Bulk errors: {bulk_result['errors'][:100]}...")
        
        # Clean up
        try:
            Path(config_file).unlink()
        except:
            pass
    
    print("\n" + "=" * 50)
    print("✅ Performance test completed!")
    print("\n💡 Notes:")
    print("   - If bulk API is not faster, check FMC connection and API limits")
    print("   - Bulk API benefits increase with larger datasets")
    print("   - Network latency affects individual API calls more than bulk")

if __name__ == "__main__":
    main()
