#!/usr/bin/env python3
"""
Test 100% success rate with a small batch of real ASA objects
"""

import json
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_small_batch():
    """Test migration with a small batch of real ASA objects"""
    print("🚀 Testing 100% Success Rate with Small Batch (10 hosts)...")
    
    config_file = "fmc_migration_config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ Configuration file {config_file} not found!")
        return False
    
    try:
        from fmc_migration_v2 import FMCMigrationEngine, HostObject
        
        # Load config and take first 10 hosts
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        if 'host_objects' not in config.get('api_calls', {}):
            print("❌ No host objects found in configuration!")
            return False
        
        # Take first 10 hosts
        all_hosts = config['api_calls']['host_objects']['data']
        test_hosts = all_hosts[:10]
        
        print(f"📊 Testing with {len(test_hosts)} host objects")
        
        # Initialize migration engine
        engine = FMCMigrationEngine(
            verify_ssl=False,
            overwrite=True,
            quiet=False,
            use_bulk_api=True
        )
        
        print("🏠 Migrating Host Objects...")
        results = engine.migrate_objects(
            'hosts',
            test_hosts,
            HostObject,
            "Host Objects"
        )
        
        # Analyze results
        total_objects = results.total_objects
        total_created = results.created
        total_updated = results.updated
        total_failed = results.failed
        total_skipped = results.skipped
        
        success_rate = results.success_rate
        
        print("\n" + "="*60)
        print("🎯 SMALL BATCH TEST RESULTS")
        print("="*60)
        print(f"📊 Total Objects: {total_objects}")
        print(f"✅ Created: {total_created}")
        print(f"🔄 Updated: {total_updated}")
        print(f"❌ Failed: {total_failed}")
        print(f"⏭️ Skipped: {total_skipped}")
        print(f"🎯 Success Rate: {success_rate:.1f}%")
        
        if total_failed > 0:
            print(f"\n❌ Failures:")
            for detail in results.details:
                if "Failed" in detail or "❌" in detail:
                    print(f"   • {detail}")
        
        # Check if we achieved 100% success
        if success_rate >= 100.0 and total_failed == 0:
            print("\n🎉 SUCCESS! Achieved 100% migration success rate!")
            return True
        else:
            print(f"\n⚠️ Did not achieve 100% success rate. Current: {success_rate:.1f}%")
            print(f"   Failed objects: {total_failed}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_small_batch()
    sys.exit(0 if success else 1)
