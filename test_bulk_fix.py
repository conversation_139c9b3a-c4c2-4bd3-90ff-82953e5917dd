#!/usr/bin/env python3
"""
Test script to verify the bulk API authentication fix

This script creates a small test configuration and runs it to verify
that the bulk API authentication is working correctly.

Usage:
    python test_bulk_fix.py

Author: AI Assistant
Version: 2.0
Date: 2025-08-04
"""

import json
import subprocess
import sys
import time

def create_small_test_config():
    """Create a small test configuration for bulk API testing"""
    config = {
        "api_calls": {
            "host_objects": {
                "data": [
                    {
                        "name": "BulkTest_Host_001",
                        "type": "Host",
                        "value": "*************",
                        "description": "Bulk API test host 1"
                    },
                    {
                        "name": "BulkTest_Host_002", 
                        "type": "Host",
                        "value": "*************",
                        "description": "Bulk API test host 2"
                    },
                    {
                        "name": "BulkTest_Host_003",
                        "type": "Host", 
                        "value": "*************",
                        "description": "Bulk API test host 3"
                    },
                    {
                        "name": "BulkTest_Host_004",
                        "type": "Host",
                        "value": "*************", 
                        "description": "Bulk API test host 4"
                    },
                    {
                        "name": "BulkTest_Host_005",
                        "type": "Host",
                        "value": "*************",
                        "description": "Bulk API test host 5"
                    },
                    {
                        "name": "BulkTest_Host_006",
                        "type": "Host",
                        "value": "*************",
                        "description": "Bulk API test host 6"
                    }
                ]
            }
        }
    }
    
    filename = "test_bulk_fix.json"
    with open(filename, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Created test configuration: {filename}")
    print(f"   - {len(config['api_calls']['host_objects']['data'])} host objects")
    return filename

def run_test(config_file, use_bulk=True):
    """Run the migration test"""
    cmd = ["python", "fmc_migration_v2.py", config_file, "--overwrite", "--quiet"]
    if not use_bulk:
        cmd.append("--no-bulk")
    
    test_type = "bulk API" if use_bulk else "individual API"
    print(f"\n🔄 Testing {test_type}...")
    print(f"   Command: {' '.join(cmd)}")
    
    start_time = time.time()
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        duration = time.time() - start_time
        
        print(f"   Duration: {duration:.2f}s")
        print(f"   Return code: {result.returncode}")
        
        # Look for key indicators in output
        output = result.stdout + result.stderr
        
        if "BULK_API_ENABLED" in output:
            print("   ✅ Bulk API was enabled")
        elif use_bulk:
            print("   ⚠️  Bulk API was not enabled (check threshold)")
            
        if "BULK_API_COMPLETE" in output:
            print("   ✅ Bulk API operation completed")
        elif use_bulk:
            print("   ❌ Bulk API operation did not complete")
            
        if "401" in output and "Access token not found" in output:
            print("   ❌ Authentication error detected")
        else:
            print("   ✅ No authentication errors")
            
        # Look for success indicators
        if "created" in output.lower() or "updated" in output.lower():
            print("   ✅ Objects were processed successfully")
        elif "failed" in output.lower():
            print("   ❌ Object processing failed")
        
        # Show first few lines of output for debugging
        output_lines = output.split('\n')[:10]
        if output_lines:
            print("   📄 Output sample:")
            for line in output_lines:
                if line.strip():
                    print(f"      {line.strip()}")
        
        return {
            'success': result.returncode == 0,
            'duration': duration,
            'output': output,
            'use_bulk': use_bulk
        }
        
    except subprocess.TimeoutExpired:
        duration = time.time() - start_time
        print(f"   ⏰ Test timed out after {duration:.2f}s")
        return {
            'success': False,
            'duration': duration,
            'output': 'Test timed out',
            'use_bulk': use_bulk
        }
    except Exception as e:
        duration = time.time() - start_time
        print(f"   ❌ Test failed: {e}")
        return {
            'success': False,
            'duration': duration,
            'output': str(e),
            'use_bulk': use_bulk
        }

def main():
    """Main test function"""
    print("🧪 Bulk API Authentication Fix Test")
    print("=" * 50)
    
    # Create test configuration
    config_file = create_small_test_config()
    
    try:
        # Test bulk API
        bulk_result = run_test(config_file, use_bulk=True)
        
        # Test individual API for comparison
        individual_result = run_test(config_file, use_bulk=False)
        
        # Compare results
        print("\n📊 Test Results Summary:")
        print("=" * 50)
        print(f"Bulk API:       {'✅ Success' if bulk_result['success'] else '❌ Failed'} ({bulk_result['duration']:.2f}s)")
        print(f"Individual API: {'✅ Success' if individual_result['success'] else '❌ Failed'} ({individual_result['duration']:.2f}s)")
        
        if bulk_result['success'] and individual_result['success']:
            improvement = ((individual_result['duration'] - bulk_result['duration']) / individual_result['duration']) * 100
            print(f"Performance:    {improvement:.1f}% improvement with bulk API")
            
        # Recommendations
        print("\n💡 Recommendations:")
        if bulk_result['success']:
            print("   ✅ Bulk API is working correctly!")
            print("   🚀 Use bulk API for large migrations")
        else:
            print("   ❌ Bulk API needs further debugging")
            print("   🔍 Check logs for authentication issues")
            print("   🔄 Use individual API as fallback")
            
    finally:
        # Clean up
        try:
            import os
            os.remove(config_file)
            print(f"\n🧹 Cleaned up test file: {config_file}")
        except:
            pass

if __name__ == "__main__":
    main()
